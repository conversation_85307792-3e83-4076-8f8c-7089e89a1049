
package com.dell.it.hip.config.FlowSteps;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TargetFileNameConfig {
    private List<TargetFileNameAttributes> fileNameSection;
    private String fileSeparator;
    private String fileExtension;
@Data
public static class TargetFileNameAttributes {
    private String derivedFrom; // FixedText, DateandTime, RandomNumber4digit, etc.
    private String valueText;
    private String partNumber;
}
}
