package com.dell.it.hip.strategy.flows;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.TargetFileNameConfig;
import com.dell.it.hip.config.FlowSteps.TargetFileNameConfig.TargetFileNameAttributes;

@Component("targetFilename")
public class TargetFileNameFlowStepStrategy extends AbstractFlowStepStrategy {

	@Override
	public String getType() {
		return "targetFilename";
	}

	@Override
	protected List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef ref, HIPIntegrationDefinition def)
			throws Exception {
		TargetFileNameConfig config = (TargetFileNameConfig) def.getConfigMap().get(ref.getPropertyRef());
		if (config == null) {
			throw new IllegalStateException("No TargetFile config for: " + ref.getPropertyRef());
		}
		Map<String, String> headerMap = new HashMap<>();
		String filename = getFileName(config,message);
		headerMap.put("filename", filename);
		List<Message<?>> result = new ArrayList<>();
		result.add(MessageBuilder.withPayload(message).build());
		// Log successful split
		wiretapService.tap(message, def, ref, "info", "Target file name " + result.size() + " messages");
		result.add(MessageBuilder.withPayload(message).copyHeaders(headerMap).build());
		return result;
	}
	
	private String getFileName(TargetFileNameConfig fileNameConfig,Message<?> message) {
		StringBuilder fileNameValue = new StringBuilder();
		if(fileNameConfig!=null && fileNameConfig.getFileNameSection()!=null) {
			int i = fileNameConfig.getFileNameSection().size() - 1;
			for (TargetFileNameAttributes fileName : fileNameConfig.getFileNameSection()) {
				fileNameValue.append(getFileNameValue(fileName.getDerivedFrom(), fileName.getValueText(),message));
				if (i-- != 0) {//If not a last element.
					fileNameValue.append(fileNameConfig.getFileSeparator());
				}else if(fileNameConfig.getFileExtension().contains(".")) {
					fileNameValue.append(fileNameConfig.getFileExtension());
				}else {
					fileNameValue.append("."+fileNameConfig.getFileExtension());
				}
			}	
		}
		return fileNameValue.toString();
	}
		
		private String getFileNameValue(String derivedFrom, String valueText,Message<?> message) {
			String value = "";
			switch(derivedFrom) {
			case "FixedText":
				value = valueText;
				break;
			case "DateandTime":
				value = new SimpleDateFormat(valueText).format(Calendar.getInstance().getTime());
				break;
			case "RandomNumber4digit":
				value = getRandomNumber(derivedFrom);
				break;
			case "RandomNumber6digit":
				value = getRandomNumber(derivedFrom);
				break;	
			case "FileNameAttribute":
				value = message.getHeaders().get(valueText).toString();
				break;
			default:
			}
			return value;
		}
		
		private String getRandomNumber(String digit) {
			String value = null;
			if(digit.equalsIgnoreCase("RandomNumber4digit")) {
				Random rnd = new Random();
			    int number = rnd.nextInt(9999);
			    value = String.format("%04d", number);
			}else if (digit.equalsIgnoreCase("RandomNumber6digit")) {
				Random rnd = new Random();
			    int number = rnd.nextInt(999999);
			    value = String.format("%06d", number);
			}
		    return value;
		}

	

}
