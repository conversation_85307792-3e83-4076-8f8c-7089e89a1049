import java.math.BigDecimal;

public class test_precision {
    public static void main(String[] args) {
        // Test BigDecimal precision behavior
        BigDecimal bd1 = new BigDecimal("1.0");
        BigDecimal bd2 = new BigDecimal("1.00");
        BigDecimal bd3 = new BigDecimal("1.50");
        
        System.out.println("BigDecimal(\"1.0\").toPlainString() = " + bd1.toPlainString());
        System.out.println("BigDecimal(\"1.00\").toPlainString() = " + bd2.toPlainString());
        System.out.println("BigDecimal(\"1.50\").toPlainString() = " + bd3.toPlainString());
        
        System.out.println("BigDecimal(\"1.0\").toString() = " + bd1.toString());
        System.out.println("BigDecimal(\"1.00\").toString() = " + bd2.toString());
        System.out.println("BigDecimal(\"1.50\").toString() = " + bd3.toString());
        
        System.out.println("BigDecimal(\"1.0\").scale() = " + bd1.scale());
        System.out.println("BigDecimal(\"1.00\").scale() = " + bd2.scale());
        System.out.println("BigDecimal(\"1.50\").scale() = " + bd3.scale());
    }
}
