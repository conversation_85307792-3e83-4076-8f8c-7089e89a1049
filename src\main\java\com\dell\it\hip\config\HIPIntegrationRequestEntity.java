package com.dell.it.hip.config;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
@Entity
@Table(name = "hip_integration_requests")
public class HIPIntegrationRequestEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String serviceManagerName;

    @Column(nullable = false)
    private String hipIntegrationName;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal version;

    @Column(nullable = false)
    private String businessFlowName;

    @Column(length = 1000)
    private String tags;

    @Column
    private String businessFlowType;

    @Column
    private String hipIntegrationType;

    @Column
    private String businessFlowVersion;

    // Store adapters, handlers, steps as J<PERSON><PERSON> blobs
    @Lob
    @Column(name = "adapters_json", columnDefinition = "CLOB")
    private String adaptersJson;

    @Lob
    @Column(name = "handlers_json", columnDefinition = "CLOB")
    private String handlersJson;

    @Lob
    @Column(name = "flow_steps_json", columnDefinition = "CLOB")
    private String flowStepsJson;

    @Lob
    @Column(name = "property_sheets_json", columnDefinition = "CLOB")
    private String propertySheetsJson;

    @Lob
    @Column(name = "throttle_settings_json", columnDefinition = "CLOB")
    private String throttleSettingsJson;

    // Status field, useful for runtime tracking
    @Column(name = "status", length = 50)
    private String status;

    // Timestamps
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Optional: for soft deletes
    @Column(name = "deleted")
    private Boolean deleted = false;

    // --- Getters and Setters ---

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = this.createdAt;
    }



    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // ... standard getters and setters for all fields ...

    // Example getter/setter for adaptersJson
    public String getAdaptersJson() {
        return adaptersJson;
    }

    public void setAdaptersJson(String adaptersJson) {
        this.adaptersJson = adaptersJson;
    }

    // (Repeat for other fields...)

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceManagerName() {
        return serviceManagerName;
    }

    public void setServiceManagerName(String serviceManagerName) {
        this.serviceManagerName = serviceManagerName;
    }

    public String getHipIntegrationName() {
        return hipIntegrationName;
    }

    public void setHipIntegrationName(String hipIntegrationName) {
        this.hipIntegrationName = hipIntegrationName;
    }

    /**
     * Gets the version as a String for application logic compatibility.
     * Converts from BigDecimal to String, handling null values gracefully.
     * Preserves the original precision to maintain version format consistency.
     *
     * @return version as String preserving original precision, or null if version is null
     */
    public String getVersion() {
        if (version == null) {
            return null;
        }
        // Use toPlainString() to preserve original precision without forcing decimal places
        return version.toPlainString();
    }

    /**
     * Sets the version from a String for application logic compatibility.
     * Converts from String to BigDecimal, handling null values and simple decimal formats.
     * For complex semantic versions (e.g., "1.2.3"), converts to a decimal representation.
     *
     * @param version version as String, can be null
     */
    public void setVersion(String version) {
        if (version == null) {
            this.version = null;
        } else {
            try {
                // Handle simple decimal versions directly
                if (isSimpleDecimal(version)) {
                    this.version = new BigDecimal(version);
                } else {
                    // Convert complex semantic versions to decimal representation
                    this.version = new BigDecimal(convertSemanticVersionToDecimal(version));
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid version format: " + version +
                    ". Version must be a valid decimal number or semantic version (e.g., '1.0', '2.1', '1.2.3', '3').", e);
            }
        }
    }

    /**
     * Checks if a version string is a simple decimal (at most one decimal point).
     *
     * @param version version string to check
     * @return true if it's a simple decimal, false otherwise
     */
    private boolean isSimpleDecimal(String version) {
        return version.indexOf('.') == version.lastIndexOf('.');
    }

    /**
     * Converts semantic version format to decimal format for BigDecimal storage.
     * Uses a hash-based approach to ensure uniqueness while maintaining some ordering.
     * Examples: "1.2.3" -> "1.0203", "1.0.0" -> "1.0000", "2.1" -> "2.1"
     *
     * @param version semantic version string
     * @return decimal version string suitable for BigDecimal
     */
    private String convertSemanticVersionToDecimal(String version) {
        // Remove trailing dots first
        String cleanVersion = version.replaceAll("\\.+$", "");
        String[] parts = cleanVersion.split("\\.");

        // Filter out empty parts (from cases like "1..0")
        java.util.List<String> validParts = new java.util.ArrayList<>();
        for (String part : parts) {
            if (!part.isEmpty()) {
                validParts.add(part);
            }
        }

        if (validParts.size() <= 2) {
            return cleanVersion; // Already in simple decimal format
        }

        // Convert to decimal format with zero-padding: major.MMPP (MM=minor, PP=patch)
        // This allows up to 99 for minor and patch versions
        StringBuilder decimal = new StringBuilder(validParts.get(0));
        decimal.append('.');

        // Add minor version (default to 0 if not present)
        int minor = validParts.size() > 1 ? Integer.parseInt(validParts.get(1)) : 0;
        decimal.append(String.format("%02d", Math.min(minor, 99)));

        // Add patch version (default to 0 if not present)
        int patch = validParts.size() > 2 ? Integer.parseInt(validParts.get(2)) : 0;
        decimal.append(String.format("%02d", Math.min(patch, 99)));

        return decimal.toString();
    }

    /**
     * Gets the raw BigDecimal version value for JPA/database operations.
     * This method is primarily for internal use by JPA and should not be used by application logic.
     *
     * @return version as BigDecimal, or null if version is null
     */
    public BigDecimal getVersionBigDecimal() {
        return version;
    }

    /**
     * Sets the raw BigDecimal version value for JPA/database operations.
     * This method is primarily for internal use by JPA and should not be used by application logic.
     *
     * @param version version as BigDecimal, can be null
     */
    public void setVersionBigDecimal(BigDecimal version) {
        this.version = version;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getHandlersJson() {
        return handlersJson;
    }

    public void setHandlersJson(String handlersJson) {
        this.handlersJson = handlersJson;
    }

    public String getFlowStepsJson() {
        return flowStepsJson;
    }

    public void setFlowStepsJson(String flowStepsJson) {
        this.flowStepsJson = flowStepsJson;
    }

    public String getPropertySheetsJson() {
        return propertySheetsJson;
    }

    public void setPropertySheetsJson(String propertySheetsJson) {
        this.propertySheetsJson = propertySheetsJson;
    }

    public String getThrottleSettingsJson() {
        return throttleSettingsJson;
    }

    public void setThrottleSettingsJson(String throttleSettingsJson) {
        this.throttleSettingsJson = throttleSettingsJson;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}