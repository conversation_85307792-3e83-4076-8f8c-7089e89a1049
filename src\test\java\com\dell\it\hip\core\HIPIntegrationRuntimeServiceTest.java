package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Comprehensive unit tests for HIPIntegrationRuntimeService class.
 * Tests all methods and edge cases to improve code coverage.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationRuntimeServiceTest {

    @Mock
    private HIPRedisCompatibilityService redisService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private HIPIntegrationRuntimeService runtimeService;

    private final String TEST_SERVICE_MANAGER = "test-service-manager";
    private final String TEST_INTEGRATION_NAME = "test-integration";
    private final String TEST_VERSION = "1.0";
    private final String TEST_ADAPTER_ID = "test-adapter";

    @BeforeEach
    void setUp() throws Exception {
        // Set private field serviceManagerName using reflection
        setPrivateField(runtimeService, "serviceManagerName", TEST_SERVICE_MANAGER);

        // Setup Redis service mock - no additional setup needed
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    @Test
    @DisplayName("Test updateHIPIntegrationStatus_ValidStatus_UpdatesSuccessfully")
    void testUpdateHIPIntegrationStatus_ValidStatus_UpdatesSuccessfully() {
        // Arrange
        IntegrationStatus status = IntegrationStatus.RUNNING;
        String expectedKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;

        // Act
        runtimeService.updateHIPIntegrationStatus(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, status);

        // Assert
        verify(redisService).set(expectedKey, status.name());
    }

    @Test
    @DisplayName("Test updateHIPIntegrationStatus_ExceptionThrown_LogsError")
    void testUpdateHIPIntegrationStatus_ExceptionThrown_LogsError() {
        // Arrange
        IntegrationStatus status = IntegrationStatus.ERROR;
        String expectedKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;
        doThrow(new RuntimeException("Redis error")).when(redisService).set(expectedKey, status.name());

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            runtimeService.updateHIPIntegrationStatus(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, status);
        });

        verify(redisService).set(expectedKey, status.name());
    }

    @Test
    @DisplayName("Test getHIPIntegrationStatus_ExistingStatus_ReturnsCorrectStatus")
    void testGetHIPIntegrationStatus_ExistingStatus_ReturnsCorrectStatus() {
        // Arrange
        String expectedKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;
        when(redisService.get(expectedKey)).thenReturn("RUNNING");

        // Act
        IntegrationStatus result = runtimeService.getHIPIntegrationStatus(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(IntegrationStatus.RUNNING, result);
        verify(redisService).get(expectedKey);
    }

    @Test
    @DisplayName("Test getHIPIntegrationStatus_NonExistentStatus_ReturnsUnregistered")
    void testGetHIPIntegrationStatus_NonExistentStatus_ReturnsUnregistered() {
        // Arrange
        String expectedKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;
        when(redisService.get(expectedKey)).thenReturn(null);

        // Act
        IntegrationStatus result = runtimeService.getHIPIntegrationStatus(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(IntegrationStatus.UNREGISTERED, result);
        verify(redisService).get(expectedKey);
    }

    @Test
    @DisplayName("Test getHIPIntegrationStatus_InvalidStatus_ReturnsError")
    void testGetHIPIntegrationStatus_InvalidStatus_ReturnsError() {
        // Arrange
        String expectedKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;
        when(redisService.get(expectedKey)).thenReturn("INVALID_STATUS");

        // Act
        IntegrationStatus result = runtimeService.getHIPIntegrationStatus(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(IntegrationStatus.ERROR, result);
        verify(redisService).get(expectedKey);
    }

    @Test
    @DisplayName("Test updateThrottle_ValidSettings_UpdatesSuccessfully")
    void testUpdateThrottle_ValidSettings_UpdatesSuccessfully() throws Exception {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings(10, 60, true);
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;
        String serializedSettings = "{\"maxMessagesPerPeriod\":10,\"periodSeconds\":60,\"enabled\":true}";
        when(objectMapper.writeValueAsString(settings)).thenReturn(serializedSettings);

        // Act
        runtimeService.updateThrottle(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID, settings);

        // Assert
        verify(redisService).set(expectedKey, serializedSettings);
        verify(objectMapper).writeValueAsString(settings);
    }

    @Test
    @DisplayName("Test updateThrottle_NullSettings_DeletesKey")
    void testUpdateThrottle_NullSettings_DeletesKey() {
        // Arrange
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;

        // Act
        runtimeService.updateThrottle(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID, null);

        // Assert
        verify(redisService).delete(expectedKey);
        verifyNoInteractions(objectMapper);
    }

    @Test
    @DisplayName("Test updateThrottle_SerializationException_LogsError")
    void testUpdateThrottle_SerializationException_LogsError() throws Exception {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings(10, 60, true);
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;
        when(objectMapper.writeValueAsString(settings)).thenThrow(new RuntimeException("Serialization error"));

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            runtimeService.updateThrottle(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID, settings);
        });

        verify(objectMapper).writeValueAsString(settings);
        verifyNoInteractions(redisService);
    }

    @Test
    @DisplayName("Test getThrottleSettings_ExistingSettings_ReturnsSettings")
    void testGetThrottleSettings_ExistingSettings_ReturnsSettings() throws Exception {
        // Arrange
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;
        String serializedSettings = "{\"maxMessagesPerPeriod\":10,\"periodSeconds\":60,\"enabled\":true}";
        ThrottleSettings expectedSettings = new ThrottleSettings(10, 60, true);
        when(redisService.get(expectedKey)).thenReturn(serializedSettings);
        when(objectMapper.readValue(serializedSettings, ThrottleSettings.class)).thenReturn(expectedSettings);

        // Act
        ThrottleSettings result = runtimeService.getThrottleSettings(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        assertEquals(expectedSettings, result);
        verify(redisService).get(expectedKey);
        verify(objectMapper).readValue(serializedSettings, ThrottleSettings.class);
    }

    @Test
    @DisplayName("Test getThrottleSettings_NonExistentSettings_ReturnsNull")
    void testGetThrottleSettings_NonExistentSettings_ReturnsNull() {
        // Arrange
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;
        when(redisService.get(expectedKey)).thenReturn(null);

        // Act
        ThrottleSettings result = runtimeService.getThrottleSettings(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        assertNull(result);
        verify(redisService).get(expectedKey);
        verifyNoInteractions(objectMapper);
    }

    @Test
    @DisplayName("Test getThrottleSettings_DeserializationException_ReturnsNull")
    void testGetThrottleSettings_DeserializationException_ReturnsNull() throws Exception {
        // Arrange
        String expectedKey = "hip:runtime:throttle:" + TEST_SERVICE_MANAGER + ":" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION + ":" + TEST_ADAPTER_ID;
        String invalidJson = "invalid json";
        when(redisService.get(expectedKey)).thenReturn(invalidJson);
        when(objectMapper.readValue(invalidJson, ThrottleSettings.class)).thenThrow(new RuntimeException("Deserialization error"));

        // Act
        ThrottleSettings result = runtimeService.getThrottleSettings(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        assertNull(result);
        verify(redisService).get(expectedKey);
        verify(objectMapper).readValue(invalidJson, ThrottleSettings.class);
    }

    @Test
    @DisplayName("Test recordSftpCallback_ValidParameters_LogsCallback")
    void testRecordSftpCallback_ValidParameters_LogsCallback() {
        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            runtimeService.recordSftpCallback(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);
        });

        // This method only logs, so we just verify it doesn't throw
    }

    @Test
    @DisplayName("Test removeAllRuntimeState_ValidParameters_RemovesStatusKey")
    void testRemoveAllRuntimeState_ValidParameters_RemovesStatusKey() {
        // Arrange
        String expectedStatusKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;

        // Act
        runtimeService.removeAllRuntimeState(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(redisService).delete(expectedStatusKey);
    }

    @Test
    @DisplayName("Test removeAllRuntimeState_ExceptionThrown_LogsError")
    void testRemoveAllRuntimeState_ExceptionThrown_LogsError() {
        // Arrange
        String expectedStatusKey = "hip:runtime:" + TEST_SERVICE_MANAGER + ":status:" + TEST_INTEGRATION_NAME + ":" + TEST_VERSION;
        doThrow(new RuntimeException("Redis error")).when(redisService).delete(expectedStatusKey);

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            runtimeService.removeAllRuntimeState(TEST_SERVICE_MANAGER, TEST_INTEGRATION_NAME, TEST_VERSION);
        });

        verify(redisService).delete(expectedStatusKey);
    }
}
