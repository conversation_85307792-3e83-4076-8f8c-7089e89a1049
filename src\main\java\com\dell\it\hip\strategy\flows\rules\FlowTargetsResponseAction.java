package com.dell.it.hip.strategy.flows.rules;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.HandlerTarget;
import com.dell.it.hip.config.rules.Rule;

public class FlowTargetsResponseAction implements RuleAction {
    
	private static final Logger logger = LoggerFactory.getLogger(FlowTargetsResponseAction.class);
    
    @Override
    public String getName() { return "flowTargetResponse"; }

    @Override
    public String getType() { return "FlowTargetResponse"; }

    @Override
    public Message<?> performAction(Message<?> message, Map<String, Object> params, Rule rule, HIPIntegrationDefinition def, Map<String, Object> context) {
        String primaryHandler = params != null ? (String) params.get("primaryHandler") : null;
        String fallbackHandler = params != null ? (String) params.get("fallbackHandler") : null;
        if (primaryHandler == null && fallbackHandler == null) {
            logger.warn("FlowTargetsResponseAction: Both primary and fallback handlers are null for rule '{}'", rule != null ? rule.getRuleName() : null);
        } else {
            logger.info("FlowTargetsResponseAction: Adding handler target [primary={}, fallback={}] from rule '{}'", primaryHandler, fallbackHandler, rule != null ? rule.getRuleName() : null);
            getOrCreateHandlerTargets(context).add(new HandlerTarget(primaryHandler, fallbackHandler));
        }
        return message;
    }

    private List<HandlerTarget> getOrCreateHandlerTargets(Map<String, Object> context) {
        @SuppressWarnings("unchecked")
        List<HandlerTarget> handlerTargets = (List<HandlerTarget>) context.get("handlerTargets");
        if (handlerTargets == null) {
            handlerTargets = new ArrayList<>();
            context.put("handlerTargets", handlerTargets);
        }
        return handlerTargets;
    }
}