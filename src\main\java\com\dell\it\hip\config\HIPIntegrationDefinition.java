package com.dell.it.hip.config;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;

public class HIPIntegrationDefinition {

    private String hipIntegrationName;
    private String serviceManagerName;
    private String version;
    private String owner;
    private String businessFlowName;
    private String description;

    @JsonDeserialize(using = TagsDeserializer.class)
    private List<Tag> tags;

    private ThrottleSettings throttleSettings;



    // Config ref lists
    private List<AdapterConfigRef> adapterConfigRefs = new ArrayList<>();
    private List<HandlerConfigRef> handlerConfigRefs = new ArrayList<>();
    private List<FlowStepConfigRef> flowStepConfigRefs = new ArrayList<>();

    // propertyRef -> config instance (AdapterConfig, HandlerConfig, FlowStepConfig, etc)
    private Map<String, Object> configMap = new HashMap<>();

    // For support/debugging, property key-value map (merged from property sheets)
    private Map<String, Object> mergedProperties = new HashMap<>();

    private String businessFlowType;

    private String hipIntegrationType;
    private String businessFlowVersion;
    
    private String flowType;


    // --- Getters and Setters ---

    public String getHipIntegrationName() {
        return hipIntegrationName;
    }

    public void setHipIntegrationName(String hipIntegrationName) {
        this.hipIntegrationName = hipIntegrationName;
    }

    public String getServiceManagerName() {
        return serviceManagerName;
    }

    public void setServiceManagerName(String serviceManagerName) {
        this.serviceManagerName = serviceManagerName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    /**
     * Convenience method to get tags as a comma-separated string for backward compatibility.
     * @return comma-separated string of tag values
     */
    public String getTagsAsString() {
        if (tags == null || tags.isEmpty()) {
            return "";
        }
        return tags.stream()
                .map(tag -> tag.getKey().equals("legacy") ? tag.getValue() : tag.getKey() + ":" + tag.getValue())
                .reduce((a, b) -> a + "," + b)
                .orElse("");
    }

    /**
     * Convenience method to set tags from a comma-separated string for backward compatibility.
     * @param tagsString comma-separated string of tag values
     */
    public void setTagsFromString(String tagsString) {
        if (tagsString == null || tagsString.trim().isEmpty()) {
            this.tags = new ArrayList<>();
            return;
        }

        this.tags = new ArrayList<>();
        String[] tagArray = tagsString.split(",");
        for (String tagValue : tagArray) {
            String trimmedTag = tagValue.trim();
            if (!trimmedTag.isEmpty()) {
                this.tags.add(new Tag("legacy", trimmedTag));
            }
        }
    }

    public ThrottleSettings getThrottleSettings() {
        return throttleSettings;
    }

    public void setThrottleSettings(ThrottleSettings throttleSettings) {
        this.throttleSettings = throttleSettings;
    }


    public List<AdapterConfigRef> getAdapterConfigRefs() {
        return adapterConfigRefs;
    }

    public void setAdapterConfigRefs(List<AdapterConfigRef> adapterConfigRefs) {
        this.adapterConfigRefs = adapterConfigRefs;
    }

    public List<HandlerConfigRef> getHandlerConfigRefs() {
        return handlerConfigRefs;
    }

    public void setHandlerConfigRefs(List<HandlerConfigRef> handlerConfigRefs) {
        this.handlerConfigRefs = handlerConfigRefs;
    }

    public List<FlowStepConfigRef> getFlowStepConfigRefs() {
        return flowStepConfigRefs;
    }

    public void setFlowStepConfigRefs(List<FlowStepConfigRef> flowStepConfigRefs) {
        this.flowStepConfigRefs = flowStepConfigRefs;
    }

    public Map<String, Object> getConfigMap() {
        return configMap;
    }

    public void setConfigMap(Map<String, Object> configMap) {
        this.configMap = configMap;
    }

    public Map<String, Object> getMergedProperties() {
        return mergedProperties;
    }

    public void setMergedProperties(Map<String, Object> mergedProperties) {
        this.mergedProperties = mergedProperties;
    }

    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    // --- Utility for direct lookup ---
    @SuppressWarnings("unchecked")
    public <T> T getConfig(String propertyRef, Class<T> type) {
        Object obj = configMap.get(propertyRef);
        if (obj == null) return null;
        return (T) obj;
    }

	public String getFlowType() {
		return flowType;
	}

	public void setFlowType(String flowType) {
		this.flowType = flowType;
	}
}