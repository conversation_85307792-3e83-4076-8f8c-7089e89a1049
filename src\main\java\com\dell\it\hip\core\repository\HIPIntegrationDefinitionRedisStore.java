package com.dell.it.hip.core.repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.DeserializationFeature;

@Component("redisStore")
@ConditionalOnProperty(name = "integration.store.type", havingValue = "REDIS")
public class HIPIntegrationDefinitionRedisStore implements HIPIntegrationDefinitionStore {

    private static final Logger logger = LoggerFactory.getLogger(HIPIntegrationDefinitionRedisStore.class);

    @Autowired
    private HIPRedisCompatibilityService redisService;
    private static final String PREFIX = "hip_integration:";

    // Configure ObjectMapper for consistent BigDecimal handling
    private final ObjectMapper objectMapper;

    public HIPIntegrationDefinitionRedisStore() {
        this.objectMapper = new ObjectMapper();
        // Configure to use BigDecimal for floating point numbers
        this.objectMapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
        // Ensure consistent number handling
        this.objectMapper.configure(DeserializationFeature.USE_BIG_INTEGER_FOR_INTS, false);
    }

    private String key(String serviceManager, String name, String version) {
        return PREFIX + serviceManager + ":" + name + ":" + version;
    }

    /**
     * Gets the version in the correct format for Redis key generation.
     * For simple decimal versions, preserves the original format (e.g., "1.0" stays "1.0").
     * For semantic versions, uses the converted format (e.g., "1.2.3" becomes "1.0203").
     * This ensures Redis key consistency with the expected format.
     */
    private String getVersionForKey(HIPIntegrationRequestEntity entity) {
        if (entity.getVersionBigDecimal() == null) {
            return null;
        }
        // Use toPlainString() to avoid unnecessary decimal formatting
        return entity.getVersionBigDecimal().toPlainString();
    }

    @Override
    public void save(HIPIntegrationRequestEntity entity) {
        String versionForKey = getVersionForKey(entity);
        if (versionForKey == null) {
            throw new RuntimeException("Version is required for Redis key generation");
        }
        String redisKey = key(entity.getServiceManagerName(), entity.getHipIntegrationName(), versionForKey);
        try {
            // Use configured ObjectMapper for consistent BigDecimal handling
            String value = objectMapper.writeValueAsString(entity);

            // Log the version field handling for debugging
            logger.debug("Saving entity to Redis hash - Field: {}, Version (String): {}, Version (BigDecimal): {}",
                redisKey, entity.getVersion(), entity.getVersionBigDecimal());

            // Use hash-based storage through compatibility service
            redisService.set(redisKey, value);

            logger.debug("Successfully saved entity to Redis hash with field: {}", redisKey);
        } catch (Exception e) {
            logger.error("Failed to save entity to Redis hash with field: {} - {}", redisKey, e.getMessage(), e);
            throw new RuntimeException("Failed to save to Redis", e);
        }
    }
    @Override
    public HIPIntegrationRequestEntity find(String serviceManagerName, String hipIntegrationName, String version) {
        String redisKey = key(serviceManagerName, hipIntegrationName, version);
        String value = redisService.get(redisKey);
        if (value == null) {
            logger.debug("No entity found in Redis hash for field: {}", redisKey);
            return null;
        }

        try {
            // First, deserialize to JsonNode to inspect the version field
            JsonNode jsonNode = objectMapper.readTree(value);
            JsonNode versionNode = jsonNode.get("version");

            // Deserialize the entity using configured ObjectMapper
            HIPIntegrationRequestEntity entity = objectMapper.readValue(value, HIPIntegrationRequestEntity.class);

            // Ensure version field consistency - if the JSON contains a BigDecimal that was serialized as a number,
            // we need to make sure it's properly handled by the entity's conversion methods
            if (versionNode != null && entity.getVersionBigDecimal() != null) {
                // Verify that the String version matches the BigDecimal version
                String stringVersion = entity.getVersion();
                BigDecimal bigDecimalVersion = entity.getVersionBigDecimal();

                logger.debug("Retrieved entity from Redis hash - Field: {}, Version (String): {}, Version (BigDecimal): {}, JSON Version Node: {}",
                    redisKey, stringVersion, bigDecimalVersion, versionNode.toString());

                // If there's a mismatch, log a warning but trust the BigDecimal value
                if (stringVersion != null && !stringVersion.equals(bigDecimalVersion.toPlainString())) {
                    logger.warn("Version field inconsistency detected in Redis hash data for field: {} - String: {}, BigDecimal: {}",
                        redisKey, stringVersion, bigDecimalVersion.toPlainString());
                }
            }

            return entity;
        } catch (Exception e) {
            logger.error("Failed to deserialize entity from Redis hash with field: {} - {}", redisKey, e.getMessage(), e);
            throw new RuntimeException("Failed to deserialize from Redis hash", e);
        }
    }
    @Override
    public boolean exists(String serviceManagerName, String hipIntegrationName, String version) {
        String redisKey = key(serviceManagerName, hipIntegrationName, version);
        return Boolean.TRUE.equals(redisService.hasKey(redisKey));
    }
    @Override
    public List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName) {
        Set<Object> fields = redisService.keys(PREFIX + serviceManagerName + ":*");
        if (fields == null || fields.isEmpty()) {
            logger.debug("No fields found in Redis hash for service manager: {}", serviceManagerName);
            return Collections.emptyList();
        }

        List<HIPIntegrationRequestEntity> out = new ArrayList<>();

        for (Object fieldObj : fields) {
            String field = fieldObj.toString();
            String value = redisService.get(field);
            if (value != null) {
                try {
                    // Use the same deserialization logic as the find method with configured ObjectMapper
                    JsonNode jsonNode = objectMapper.readTree(value);
                    HIPIntegrationRequestEntity entity = objectMapper.readValue(value, HIPIntegrationRequestEntity.class);

                    // Log version field for debugging
                    logger.debug("Retrieved entity from Redis hash batch operation - Field: {}, Version (String): {}, Version (BigDecimal): {}",
                        field, entity.getVersion(), entity.getVersionBigDecimal());

                    out.add(entity);
                } catch (Exception e) {
                    logger.warn("Failed to deserialize entity from Redis hash with field: {} - {}", field, e.getMessage());
                    // Continue processing other entities instead of failing completely
                }
            }
        }

        logger.debug("Retrieved {} entities from Redis hash for service manager: {}", out.size(), serviceManagerName);
        return out;
    }
	@Override
	public void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(String serviceManagerName,
			String hipIntegrationName, String version) {
		 String redisKey = key(serviceManagerName, hipIntegrationName, version);
	        redisService.delete(redisKey);
	        logger.debug("Deleted entity from Redis hash with field: {}", redisKey);
	}

}
