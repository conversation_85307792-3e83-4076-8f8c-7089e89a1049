package com.dell.it.hip.integration;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.MediaType;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.TestJacksonConfiguration;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.controller.HIPIntegrationManagementController;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationDuplicateException;
import com.dell.it.hip.util.PropertySheetFetcher;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.dell.it.hip.util.redis.HIPRedisHashService;
import com.emc.it.eis.activity.transaction.monitoring.logging.exporter.sender.ActmonService;
import com.emc.it.eis.transaction.monitoring.logging.msgbuilder.builder.LoggingRequestBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Integration tests for HIP Integration Management functionality.
 * Tests the complete flow including duplicate validation and BigDecimal version handling.
 */
@SpringBootTest(
    classes = HIPIntegrationManagementIntegrationTest.TestApplication.class,
    properties = {
        "spring.main.allow-bean-definition-overriding=true",
        "hip_environment=test",
        "SPRING_PROFILES_ACTIVE=test",
        "configserver_uri=http://localhost:8888",
        "configproperties_sheet_name=test-config",
        "spring.cloud.config.enabled=false",
        "spring.cloud.config.import-check.enabled=false",
        "spring.data.redis.enabled=false",
        "kafka.actmon.producer.topic=test-actmon-topic",
        "kafka.bootstrap.servers=localhost:9092",
        "kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer",
        "kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer",
        "spring.kafka.enabled=false",
        "app_config_name=test-app-config",
        "service.manager.name=test-app-config"
    }
)
@ActiveProfiles("test")
@DirtiesContext(methodMode = DirtiesContext.MethodMode.AFTER_METHOD)
@Import({HIPIntegrationManagementIntegrationTest.TestActmonConfiguration.class, HIPIntegrationManagementIntegrationTest.TestSecurityConfig.class})
public class HIPIntegrationManagementIntegrationTest {



    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Autowired
    private HIPIntegrationManagementController controller;

    @MockBean
    private ActmonService actmonService;

    @MockBean
    private LoggingRequestBuilder loggingRequestBuilder;

    @MockBean
    private PropertySheetFetcher propertySheetFetcher;

    @MockBean
    private RedissonClient redissonClient;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity())
                .build();
        objectMapper = new ObjectMapper();

        // Mock PropertySheetFetcher to return required configurations
        setupPropertySheetFetcherMock();
    }

    private void setupPropertySheetFetcherMock() {
        // Create mock configurations for the test
        Map<String, Object> mockConfigs = new HashMap<>();

        // Mock Kafka adapter configuration as JSON string
        String kafkaConfigJson = "{"
            + "\"kafka.consumer.bootstrap.servers\": \"localhost:9092\","
            + "\"kafka.consumer.topic.name\": \"test-topic\","
            + "\"kafka.consumer.client.id\": \"test-client\","
            + "\"kafka.consumer.group.id\": \"test-group\","
            + "\"kafka.consumer.key.deserializer\": \"org.apache.kafka.common.serialization.StringDeserializer\","
            + "\"kafka.consumer.value.deserializer\": \"org.apache.kafka.common.serialization.StringDeserializer\""
            + "}";
        mockConfigs.put("test-kafka-config", kafkaConfigJson);

        // Mock handler configuration as JSON string
        String handlerConfigJson = "{"
            + "\"type\": \"kafkaHandler\","
            + "\"role\": \"primary\""
            + "}";
        mockConfigs.put("test-kafka-handler-config", handlerConfigJson);

        // Mock validation step configuration as JSON string
        String validationConfigJson = "{"
            + "\"type\": \"validation\","
            + "\"role\": \"primary\""
            + "}";
        mockConfigs.put("test-validation-config", validationConfigJson);

        // Configure the mock to return these configurations
        when(propertySheetFetcher.fetchAndMerge(any())).thenReturn(mockConfigs);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_Success() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("test-integration", "1.0");

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("HIPIntegration registered"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_DuplicateValidation() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("duplicate-test", "1.0");

        // Act - First registration should succeed
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Force transaction commit by checking if the integration exists
        // This ensures the first registration is fully committed before the second attempt
        Thread.sleep(100); // Small delay to ensure transaction completion

        // Verify the integration was saved (using the actual service manager name from config)
        assertTrue(hipIntegrationRegistry.exists("test-app-config", "duplicate-test", "1.0"));

        // Act - Second registration should fail with 409 Conflict
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_ALREADY_EXISTS"))
                .andExpect(jsonPath("$.message").value(containsString("already exists")))
                .andExpect(jsonPath("$.message").value(containsString("duplicate-test")))
                .andExpect(jsonPath("$.message").value(containsString("1.0")));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_DifferentVersionsAllowed() throws Exception {
        // Arrange
        HIPIntegrationRequest request1 = createTestRequest("version-test", "1.0");
        HIPIntegrationRequest request2 = createTestRequest("version-test", "2.0");

        // Act & Assert - Both should succeed
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_SemanticVersionHandling() throws Exception {
        // Test that semantic versions are handled correctly
        String[] semanticVersions = {"1.0.0", "1.2.3", "2.1.0"};

        for (String version : semanticVersions) {
            HIPIntegrationRequest request = createTestRequest("semantic-test-" + version.replace(".", ""), version);

            mockMvc.perform(post("/hip/management/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().string("HIPIntegration registered"));
        }
    }

   @Test
    void testRegisterIntegration_UnauthorizedAccess() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("unauthorized-test", "1.0");

        // Act & Assert - Should fail without proper role
        // Spring Security returns 403 (Forbidden) when no authentication is provided for a protected resource
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "USER") // Wrong role
    void testRegisterIntegration_InsufficientRole() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("role-test", "1.0");

        // Act & Assert - Should fail with insufficient role
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    void testHIPIntegrationRequestEntity_VersionConversion() {
        // Test the BigDecimal version conversion in isolation
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();

        // Test simple decimal versions
        entity.setVersion("1.0");
        assertEquals("1.0", entity.getVersion());

        // Test semantic versions
        entity.setVersion("1.2.3");
        assertEquals("1.0203", entity.getVersion()); // Converted to decimal format

        // Test null handling
        entity.setVersion(null);
        assertNull(entity.getVersion());

        // Test BigDecimal access
        entity.setVersion("2.5");
        assertNotNull(entity.getVersionBigDecimal());
        assertEquals(0, entity.getVersionBigDecimal().compareTo(new java.math.BigDecimal("2.5")));
    }

    @Test
    void testIntegrationDuplicateException_Properties() {
        // Test the exception properties
        String serviceManager = "test-service";
        String integration = "test-integration";
        String version = "1.0";

        IntegrationDuplicateException exception = new IntegrationDuplicateException(
                serviceManager, integration, version);

        assertEquals(serviceManager, exception.getServiceManagerName());
        assertEquals(integration, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertTrue(exception.getMessage().contains(serviceManager));
        assertTrue(exception.getMessage().contains(integration));
        assertTrue(exception.getMessage().contains(version));
    }

    @Test
    @WithMockUser(roles = "ADMIN")    
    void testRegisterIntegration_InvalidRequestData() throws Exception {
        // Test with invalid JSON
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json}"))
                .andExpect(status().isBadRequest());

        // Test with missing required fields
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_VersionFormatVariations() throws Exception {
        // Test various version formats that should be accepted
        String[] validVersions = {"1", "1.0", "1.0.0", "2.1.3", "10.5"};

        for (String version : validVersions) {
            HIPIntegrationRequest request = createTestRequest("version-format-" + version.replace(".", ""), version);

            mockMvc.perform(post("/hip/management/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().string("HIPIntegration registered"));
        }
    }



    private HIPIntegrationRequest createTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName(integrationName);
        request.setVersion(version);
        request.setBusinessFlowName("test-flow");
        request.setServiceManagerName("TestIntegrationManager");

        // Add required adapters to prevent NullPointerException
        AdapterConfigRef adapter = new AdapterConfigRef();
        adapter.setType("kafkaAdapter");
        adapter.setRole("input");
        adapter.setId("test-adapter");
        adapter.setPropertyRef("test-kafka-config");

        request.setAdapters(Arrays.asList(adapter));

        // Add required handlers to prevent NullPointerException
        HandlerConfigRef handler = new HandlerConfigRef();
        handler.setType("kafkaHandler");
        handler.setRole("primary");
        handler.setId("test-handler");
        handler.setPropertyRef("test-kafka-handler-config");

        request.setHandlers(Arrays.asList(handler));

        // Add required flowSteps to prevent NullPointerException
        FlowStepConfigRef flowStep = new FlowStepConfigRef();
        flowStep.setType("validation");
        flowStep.setRole("primary");
        flowStep.setId("test-validation-step");
        flowStep.setPropertyRef("test-validation-config");

        request.setFlowSteps(Arrays.asList(flowStep));

        return request;
    }

    /**
     * Test configuration to mock external dependencies
     */
    @Configuration
    static class TestActmonConfiguration {

        /**
         * Mock HIPRedisCompatibilityService for tests when Redis is disabled
         */
        @Bean
        @Primary
        public HIPRedisCompatibilityService hipRedisCompatibilityService() {
            return org.mockito.Mockito.mock(HIPRedisCompatibilityService.class);
        }

        /**
         * Mock HIPRedisHashService for tests when Redis is disabled
         */
        @Bean
        @Primary
        public HIPRedisHashService hipRedisHashService() {
            return org.mockito.Mockito.mock(HIPRedisHashService.class);
        }
    }

    /**
     * Test application configuration that excludes the problematic ActmonService package
     */
    @SpringBootApplication
    @EnableAsync
    @EnableScheduling
    @IntegrationComponentScan
    @EnableJpaRepositories(basePackages = {
        "com.dell.it.hip.core.repository",
        "com.dell.it.hip.core.registry",
        "com.dell.it.hip.security.repository",
        "com.dell.it.hip.util.routing"
    })
    @ComponentScan(basePackages = {
        "com.dell.it.hip",
        "com.emc.it.eis.transaction.monitoring.logging.msgbuilder",
        "com.emc.it.eis.oauth2server"
    })
    static class TestApplication {
    }

    @Configuration
    @EnableWebSecurity
    static class TestSecurityConfig {

        @Bean
        public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
            http
                .csrf(csrf -> csrf.disable())
                .authorizeHttpRequests(authz -> authz
                    .requestMatchers("/hip/management/register").hasRole("ADMIN")
                    .anyRequest().authenticated()
                );
            return http.build();
        }
    }
}
