package com.dell.it.hip.util.redis;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SFTPFileLockManager {

    @Autowired
    private HIPRedisCompatibilityService redisService;

    @Value("${service.manager.name}")
    private String serviceManagerName;

    public boolean acquireLock(String integrationName, String version, String adapterId, String fileName, String ownerId, long ttlSeconds) {
        String key = HIPRedisKeyUtil.sftpFileLockKey(serviceManagerName, integrationName, version, adapterId, fileName);
        Boolean set = redisService.setIfAbsent(key, ownerId, Duration.ofSeconds(ttlSeconds));
        return Boolean.TRUE.equals(set);
    }

    public void releaseLock(String integrationName, String version, String adapterId, String fileName) {
        String key = HIPRedisKeyUtil.sftpFileLockKey(serviceManagerName, integrationName, version, adapterId, fileName);
        redisService.delete(key);
    }
}