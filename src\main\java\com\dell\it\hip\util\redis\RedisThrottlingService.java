package com.dell.it.hip.util.redis;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.ThrottlingService;

/**
 * Production-grade Redis-backed implementation of cluster-wide throttling service for HIP integrations.
 * Uses hash-based Redis storage for better organization and namespace isolation.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class RedisThrottlingService implements ThrottlingService {

    @Autowired
    private HIPRedisCompatibilityService redisService;
    @Autowired
    private HIPIntegrationRuntimeService runtimeService;
    @Autowired
    private HIPClusterCoordinationService clusterCoordinationService;

    @Override
    public boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion,
                                   String adapterId, ThrottleSettings settings) {
        if (settings == null || !settings.isEnabled()) return true;

        // Use ratelimit key for token bucket
        String bucketKey = HIPRedisKeyUtil.rateLimitKey(serviceManagerName, integrationId, integrationVersion, adapterId);
        Long count = redisService.increment(bucketKey);

        if (count != null && count == 1L) {
            // Set expiration for the first increment
            redisService.expire(bucketKey, Duration.ofSeconds(settings.getPeriodSeconds()));
        }

        boolean allowed = count != null && count <= settings.getMaxMessagesPerPeriod();

        if (!allowed) {
            // Note: Hash-based storage doesn't support TTL queries in the same way
            // We'll rely on the expiration mechanism built into HIPRedisHashService
            // For now, we'll publish the refill event immediately when throttled
            clusterCoordinationService.publishClusterEvent(
                    HIPClusterEvent.forAdapterTarget(
                            "THROTTLED",
                            integrationId,
                            integrationVersion,
                            adapterId,
                            Map.of("count", count, "limit", settings.getMaxMessagesPerPeriod())
                    )
            );
        }

        return allowed;
    }

    @Override
    public void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        // Clear rate limit counter (token bucket) from hash storage
        String bucketKey = HIPRedisKeyUtil.rateLimitKey(serviceManagerName, integrationId, integrationVersion, adapterId);
        redisService.delete(bucketKey);

        // Publish cluster-wide throttle reset event
        clusterCoordinationService.publishClusterEvent(
                HIPClusterEvent.forAdapterTarget(
                        "REFILL",
                        integrationId,
                        integrationVersion,
                        adapterId,
                        Map.of("reset", true)
                )
        );
    }

    @Override
    public void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion,
                               String adapterId, ThrottleSettings settings) {
        // Update the throttle settings/config in Redis
        runtimeService.updateThrottle(serviceManagerName, integrationId, integrationVersion, adapterId, settings);

        // Publish cluster-wide throttle update event
        clusterCoordinationService.publishClusterEvent(
                HIPClusterEvent.forAdapterTarget(
                        "THROTTLE_UPDATE",
                        integrationId,
                        integrationVersion,
                        adapterId,
                        Map.of("settings", settings)
                )
        );
    }

    @Override
    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId,
                                                String integrationVersion, String adapterId) {
        return runtimeService.getThrottleSettings(serviceManagerName, integrationId, integrationVersion, adapterId);
    }
}