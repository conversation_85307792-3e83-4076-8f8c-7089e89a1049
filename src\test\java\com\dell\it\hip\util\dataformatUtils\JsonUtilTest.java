package com.dell.it.hip.util.dataformatUtils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("JsonUtil Tests")
class JsonUtilTest {

    @Test
    @DisplayName("Should get root element from JSON object")
    void testGetRootElement_ValidJSONObject_ReturnsRootElement() {
        // Arrange
        String json = "{\"user\": {\"name\": \"John\", \"age\": 30}}";
        
        // Act
        String result = JsonUtil.getRootElement(json);
        
        // Assert
        assertEquals("user", result);
    }

    @Test
    @DisplayName("Should return null for JSON array")
    void testGetRootElement_JSONArray_ReturnsNull() {
        // Arrange
        String json = "[{\"name\": \"John\"}, {\"name\": \"Jane\"}]";
        
        // Act
        String result = JsonUtil.getRootElement(json);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for empty JSON object")
    void testGetRootElement_EmptyJSONObject_ReturnsNull() {
        // Arrange
        String json = "{}";
        
        // Act
        String result = JsonUtil.getRootElement(json);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for invalid JSON")
    void testGetRootElement_InvalidJSON_ReturnsNull() {
        // Arrange
        String json = "{invalid json}";
        
        // Act
        String result = JsonUtil.getRootElement(json);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for null input")
    void testGetRootElement_NullInput_ReturnsNull() {
        // Act
        String result = JsonUtil.getRootElement(null);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should extract field by simple path")
    void testExtractField_SimplePath_ReturnsCorrectValue() {
        // Arrange
        String json = "{\"user\": {\"name\": \"John\", \"age\": 30}}";
        String jsonPath = "user.name";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertEquals("John", result);
    }

    @Test
    @DisplayName("Should extract nested field")
    void testExtractField_NestedPath_ReturnsCorrectValue() {
        // Arrange
        String json = "{\"company\": {\"employee\": {\"details\": {\"name\": \"John\"}}}}";
        String jsonPath = "company.employee.details.name";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertEquals("John", result);
    }

    @Test
    @DisplayName("Should return null for non-existent path")
    void testExtractField_NonExistentPath_ReturnsNull() {
        // Arrange
        String json = "{\"user\": {\"name\": \"John\"}}";
        String jsonPath = "user.age";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for null field value")
    void testExtractField_NullFieldValue_ReturnsNull() {
        // Arrange
        String json = "{\"user\": {\"name\": null}}";
        String jsonPath = "user.name";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle invalid JSON gracefully")
    void testExtractField_InvalidJSON_ReturnsNull() {
        // Arrange
        String json = "{invalid}";
        String jsonPath = "user.name";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle null inputs gracefully")
    void testExtractField_NullInputs_ReturnsNull() {
        // Act & Assert
        assertNull(JsonUtil.extractField(null, "user.name"));
        assertNull(JsonUtil.extractField("{\"user\": \"John\"}", null));
        assertNull(JsonUtil.extractField(null, null));
    }

    @Test
    @DisplayName("Should split JSON array by path")
    void testSplitByJsonPath_ValidArrayPath_ReturnsCorrectItems() throws Exception {
        // Arrange
        String json = "{\"users\": [{\"name\": \"John\"}, {\"name\": \"Jane\"}]}";
        String jsonPath = "/users";
        
        // Act
        List<String> result = JsonUtil.splitByJsonPath(json, jsonPath);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("John"));
        assertTrue(result.get(1).contains("Jane"));
    }

    @Test
    @DisplayName("Should handle single value node")
    void testSplitByJsonPath_SingleValueNode_ReturnsValue() throws Exception {
        // Arrange
        String json = "{\"message\": \"Hello World\"}";
        String jsonPath = "/message";
        
        // Act
        List<String> result = JsonUtil.splitByJsonPath(json, jsonPath);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals("Hello World", result.get(0));
    }

    @Test
    @DisplayName("Should fallback to root array")
    void testSplitByJsonPath_RootArrayFallback_ReturnsItems() throws Exception {
        // Arrange
        String json = "[{\"name\": \"John\"}, {\"name\": \"Jane\"}]";
        String jsonPath = "/nonexistent";
        
        // Act
        List<String> result = JsonUtil.splitByJsonPath(json, jsonPath);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("John"));
        assertTrue(result.get(1).contains("Jane"));
    }

    @Test
    @DisplayName("Should handle empty inputs for splitByJsonPath")
    void testSplitByJsonPath_EmptyInputs_ReturnsEmptyList() throws Exception {
        // Act & Assert
        assertTrue(JsonUtil.splitByJsonPath("", "/path").isEmpty());
        assertTrue(JsonUtil.splitByJsonPath(null, "/path").isEmpty());
        assertTrue(JsonUtil.splitByJsonPath("{\"data\": []}", null).isEmpty());
    }

    @Test
    @DisplayName("Should split top-level JSON array")
    void testSplitJsonArray_ValidArray_ReturnsItems() throws Exception {
        // Arrange
        String json = "[{\"name\": \"John\", \"age\": 30}, {\"name\": \"Jane\", \"age\": 25}]";
        
        // Act
        List<String> result = JsonUtil.splitJsonArray(json);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("John"));
        assertTrue(result.get(1).contains("Jane"));
    }

    @Test
    @DisplayName("Should return empty list for non-array JSON")
    void testSplitJsonArray_NonArray_ReturnsEmptyList() throws Exception {
        // Arrange
        String json = "{\"name\": \"John\"}";
        
        // Act
        List<String> result = JsonUtil.splitJsonArray(json);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle empty inputs for splitJsonArray")
    void testSplitJsonArray_EmptyInputs_ReturnsEmptyList() throws Exception {
        // Act & Assert
        assertTrue(JsonUtil.splitJsonArray("").isEmpty());
        assertTrue(JsonUtil.splitJsonArray(null).isEmpty());
    }

    @Test
    @DisplayName("Should deserialize JSON to object")
    void testFromJson_ValidJSON_ReturnsObject() {
        // Arrange
        String json = "{\"name\": \"John\", \"age\": 30}";
        
        // Act
        TestUser result = JsonUtil.fromJson(json, TestUser.class);
        
        // Assert
        assertNotNull(result);
        assertEquals("John", result.getName());
        assertEquals(30, result.getAge());
    }

    @Test
    @DisplayName("Should throw exception for invalid JSON deserialization")
    void testFromJson_InvalidJSON_ThrowsException() {
        // Arrange
        String json = "{invalid json}";
        
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            JsonUtil.fromJson(json, TestUser.class);
        });
        assertTrue(exception.getMessage().contains("Failed to parse JSON"));
    }

    @Test
    @DisplayName("Should serialize object to JSON")
    void testToJson_ValidObject_ReturnsJSON() {
        // Arrange
        TestUser user = new TestUser("John", 30);
        
        // Act
        String result = JsonUtil.toJson(user);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.contains("John"));
        assertTrue(result.contains("30"));
    }

    @Test
    @DisplayName("Should handle null object serialization")
    void testToJson_NullObject_ReturnsNullString() {
        // Act
        String result = JsonUtil.toJson(null);
        
        // Assert
        assertEquals("null", result);
    }

    @Test
    @DisplayName("Should extract numeric field as text")
    void testExtractField_NumericField_ReturnsAsText() {
        // Arrange
        String json = "{\"user\": {\"age\": 30}}";
        String jsonPath = "user.age";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertEquals("30", result);
    }

    @Test
    @DisplayName("Should extract boolean field as text")
    void testExtractField_BooleanField_ReturnsAsText() {
        // Arrange
        String json = "{\"user\": {\"active\": true}}";
        String jsonPath = "user.active";
        
        // Act
        String result = JsonUtil.extractField(json, jsonPath);
        
        // Assert
        assertEquals("true", result);
    }

    // Test helper class
    public static class TestUser {
        private String name;
        private int age;
        
        public TestUser() {}
        
        public TestUser(String name, int age) {
            this.name = name;
            this.age = age;
        }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public int getAge() { return age; }
        public void setAge(int age) { this.age = age; }
    }
}
