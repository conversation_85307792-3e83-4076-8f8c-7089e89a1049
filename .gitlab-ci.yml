variables:  
  PACKAGE_TYPE: "war"
  Contivo_Support: "no"
  Actmon_Logging_Clientrmq: "no"
  PROJECT_VERSION: "6.2.0"
  SONAR_EXCLUSIONS: 'com/emc/it/eis/cic/contivo/transformers/v1/*.java,com/opentext/contivo/callcommand/fulcrum/xref/migration/*.java,**/client/*.java,**/client/**/*.java,**/IbmmqAdapterConfig.java,**/SftpAdapterConfig.java,**/DynamicIBMMQAdapterConfig.java,**/DynamicRabbitMQAdapterConfig.java,**/DynamicKafkaAdapterConfig.java,**/AggregatorFlowStepConfig.java,**/AttributeProcessorFlowStepConfig.java,**/DedupFlowStepConfig.java,**/DefaultSplitterConfig.java,**/DocTypeConfig.java,**/Edi997FlowStepConfig.java,**/EDIFlowStepConfig.java,**/EnvelopeFlowStepConfig.java,**/FlowRoutingConfig.java,**/FlowTargetsRoutingConfig.java,**/HandlerTarget.java,**/SplitterDocTypeConfig.java,**/SplitterFlowStepConfig.java,**/StrictOrderConfig.java,**/ValidationConfig.java,**/KafkaHandlerConfig.java,**/DynamicHttpsHandlerConfig.java,**/RedisHealthIndicator.java,**/RetrySettings.java,**/Rule.java,**/RuleCondition.java,**/TransactionStatus.java,**/HIPIntegrationControlEvent.java,**/Tag.java,**/IntegrationDefinitionsWithStatusResponse.java,**/IntegrationStatusResponse.java,**/AttributeProcessorStepConfigRef.java,**/AttributeProcessResult.java,**/AttributeMapping.java,**/User.java,**/Role.java,**/AdapterConfigurationException.java,**/ExternalSystemUnavailableException.java,**/IntegrationNotFoundException.java,**/MessageTransformationException.java,**/RoutingDecisionException.java,**/ThrottleLimitExceededException.java,**/ObjectMapperSingleton.java,**/ThrottleSettingsConverter.java,**/HeaderFilterUtil.java,**/HeaderMapperUtil.java,**/IbmmqHeaderMapperUtil.java,**/KafkaHeaderMapperUtil.java,**/RabbitMqHeaderMapperUtil.java,**/SftpHeaderMapperUtil.java,**/RegexUtil.java,**/TransactionErrorEvent.java,**/InputAdapterStrategy.java,**/HandlerStrategy.java,**/FlowStepStrategy.java,**/HipServicesApplication.java,**/Constant.java,**/Constant$*.java,**/CacheClassLoader.java,**/ContivoTransformerService.java,**/ConcurrentSoftHashMap*.java,**/JavaRTCompiler.java,**/UserDetailsServiceImpl.java,**/HandlerConfigRef.java,**/HandlerConfig.java,**/HIPIntegrationDefinitionRedisStore.java,**/HIPIntegrationDefinitionJpaStore.java,**/AdapterConfigRef.java,**/ContivoMapCache.java,**/RedisNASFileLockManager.java,**/SFTPFileLockManager.java,**/RedisThrottlingService.java,**/DedupKeyUtil.java,**/JwtAuthenticationEntryPoint.java,**/JwtAuthenticationFilter.java,**/JwtTokenProvider.java,**/HIPClusterEvent.java,**/HIPIntegrationDefinition.java,**/RedisConfig.java,**/RedissonClientConfig.java,**/HIPIntegrationRequestEntity.java,**/RuleRef.java,**/HIPRuleEntity.java,**/IntegrationVersionStatus.java,**/CompressionUtil.java,**/RetryTemplateFactory.java,**/OpenTelemetryPropagationUtil*.java,**/ArchiveService.java,**/LocalThrottleWindow.java,**/SftpUtil.java,**/PropertySheetFetcher.java,**/DynamicIbmmqInputAdapter*.java,**/DynamicKafkaInputAdapter.java,**/DynamicHttpsInputAdapter.java,**/AbstractDynamicInputAdapter.java,**/DynamicSFTPInputAdapter*.java,**/DynamicRabbitMQInputAdapter*.java,**/DynamicNasInputAdapter.java,**/DynamicHttpRouterConfig.java,**/ExternalApiHealthIndicator.java,**/SftpHealthIndicator.java,**/Edi997AcknowledgeService.java,**/EDIWriter.java,**/EdiEnvelopeGenerator*.java,**/RangeAwareEdiControlManager*.java,**/EdiControlNumberUpdater.java,**/EdiIntegrationExample.java,**/EdiDocumentGenerator*.java,**/AttributeProcessorFlowStepConfigDeserializer.java,**/SplitterFlowStepConfigDeserializer.java,**/ValidationFlowStepConfigDeserializer.java,**/EnvelopeFlowStepConfigDeserializer.java,**/FlowStepConfig.java,**/FlowStepConfigRef.java,**/FlowTargetsRoutingConfigDeserializer.java,**/SelectContivoMapRuleAction.java,**/StopRuleAction.java,**/FlowResponseAction.java,**/FlowTargetsResponseAction.java,**/RuleProcessor.java,**/KafkaAdapterUtil.java,**/ValidationFlowStepStrategy.java,**/FlowTargetsRoutingFlowStepStrategy.java,**/SplitterFlowStepStrategy.java,**/TargetFileNameFlowStepStrategy.java,**/EnvelopeFlowStepStrategy.java,**/FlowRoutingFlowStepStrategy.java,**/MappingTransformerFlowStepStrategy.java,**/AggregatorFlowStepStrategy*.java,**/DocTypeProcessorStrategy.java,**/AttributeProcessorFlowStepStrategy.java,**/DedupFlowStepStrategy.java,**/Edi997FlowStepStrategy.java,**/AbstractFlowStepStrategy.java,**/EDIFlowStepStrategy.java,**/EdiValidator.java,**/StructuralValidator.java,**/SchemaValidator.java,**/JsonSchemaValidator.java,**/CsvSchemaValidator.java,**/XmlSchemaValidator.java,**/MessageFormatDetector.java,**/HIPIntegrationMapper*.java,**/DedupMetricRegistry.java,**/ContivoTransformerRegistry.java,**/StrictOrderMetricRegistry.java,**/RuleActionRegistry.java,**/StrategyRegistry.java,**/OAuth2TokenService.java,**/DynamicHttpsOutputHandler.java,**/DynamicKafkaOutputHandler.java,**/AbstractOutputHandlerStrategy.java,**/DynamicNasOutputHandler.java,**/DynamicSftpOutputHandler.java,**/DynamicRabbitMQOutputHandler.java,**/DynamicIbmmqOutputHandler.java,**/FlatFileUtil.java,**/XmlUtil*.java,**/WiretapService.java,**/TransactionLoggingUtil*.java,**/HIPIntegrationManagementController.java,**/RuleCondition$Operator.java,**/Rule$ExecuteActionWhen.java,**/Rule$Operator.java,**/Rule$RuleScope.java,**/Rule$RuleStatus.java,**/IntegrationStatusResponse$IntegrationVersionStatus.java,**/AttributeProcessorFlowStepConfig$AttributeBehavior.java,**/DedupFlowStepConfig$DedupBehavior.java,**/EnvelopeFlowStepConfig$EnvelopeBehavior.java,**/ValidationFlowStepConfig$ValidationBehavior.java,**/AggregatorFlowStepConfig$AggregatorBehavior.java,**/HIPRedisKeyUtil.java,**/MappingTransformerFlowStepConfig.java,**/ValidationUtils.java,**/HIPIntegrationOrchestrationService.java,**/RuleCache.java,**/HIPIntegrationRuntimeService.java,**/HIPClusterCoordinationService.java,**/ServiceManager.java,**/ConfigClassRegistry.java,**/Dynamic*Config.java,**/Dynamic*Handler*.java,**/Dynamic*Adapter*.java,**/Integration*Exception.java,**/GlobalExceptionHandler*.java,**/OpenTelemetryConfig.java,**/OpenApiConfig.java,**/RetryConfig.java,**/HIPIntegrationChannelsConfig.java,**/TagsDeserializer.java,**/HIPAsyncConfig.java,**/IntegrationStatus.java,**/HIPIntegrationRequest.java,**/StrictOrderProcessorFlowStepStrategy.java,**/EDIFlowStepConfigDeserializer.java,**/MappingTransformerFlowStepConfigDeserializer.java,**/DocTypeProcessorStepConfigDeserializer.java,**/DocTypeProcessorStepConfig.java,**/DocTypeRuleOperator.java,**/InputValidationUtil.java,**/ThrottleSettings.java,**/JsonUtil.java,**/CsvUtil.java,**/StaediUtil.java,**/HipMetricsService.java,**/*$*.java'

include:
  - project: 'ServiceAICNP/shared-template'
    ref: HIP-MVP5-feature
    file: 'b2b/b2bflow-image.yml'