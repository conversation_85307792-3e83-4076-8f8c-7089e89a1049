package com.dell.it.hip.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;

/**
 * Unit tests for BigDecimal to String version conversion in HIPIntegrationRequestEntity.
 * Tests the new version field conversion methods and backward compatibility.
 */
public class HIPIntegrationRequestEntityVersionConversionTest {

    private HIPIntegrationRequestEntity entity;

    @BeforeEach
    void setUp() {
        entity = new HIPIntegrationRequestEntity();
    }

    @Test
    void testSetVersionString_SimpleDecimalFormats() {
        // Test input versions and their expected outputs preserving original precision
        String[][] versionPairs = {
            {"1.0", "1.0"}, {"2.1", "2.1"}, {"1", "1"},
            {"2", "2"}, {"100", "100"}, {"0.1", "0.1"}
        };

        for (String[] pair : versionPairs) {
            String input = pair[0];
            String expected = pair[1];
            entity.setVersion(input);
            assertEquals(expected, entity.getVersion(),
                "Simple decimal version should preserve original precision for: " + input);
            assertNotNull(entity.getVersionBigDecimal(),
                "BigDecimal version should not be null for: " + input);
        }
    }

    @Test
    void testSetVersionString_SemanticVersionFormats() {
        // Test semantic version formats (will be converted to decimal format preserving converted precision)
        String[][] semanticVersions = {
            {"1.0.0", "1.0000"},   // 1.0.0 -> 1.0000 (preserve converted precision)
            {"1.2.3", "1.0203"},   // 1.2.3 -> 1.0203 (preserve converted precision)
            {"10.5.3", "10.0503"}, // 10.5.3 -> 10.0503 (preserve converted precision)
            {"2.10.15", "2.1015"}, // 2.10.15 -> 2.1015 (preserve converted precision)
            {"0.0.1", "0.0001"}    // 0.0.1 -> 0.0001 (preserve converted precision)
        };

        for (String[] versionPair : semanticVersions) {
            String input = versionPair[0];
            String expected = versionPair[1];

            entity.setVersion(input);
            assertEquals(expected, entity.getVersion(),
                "Semantic version " + input + " should convert to " + expected + " (preserving converted precision)");
            assertNotNull(entity.getVersionBigDecimal(),
                "BigDecimal version should not be null for: " + input);
        }
    }

    @Test
    void testSetVersionString_NullValue() {
        // Test null handling
        entity.setVersion(null);
        assertNull(entity.getVersion());
        assertNull(entity.getVersionBigDecimal());
    }

    @Test
    void testSetVersionString_InvalidFormats() {
        // Test invalid version formats
        String[] invalidVersions = {
            "abc", "1.0.x", "v1.0", "1.0-SNAPSHOT", "", "  ", "1..0"
        };

        for (String invalidVersion : invalidVersions) {
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
                entity.setVersion(invalidVersion);
            }, "Should throw exception for invalid version: " + invalidVersion);

            assertTrue(exception.getMessage().contains("Invalid version format"));
            assertTrue(exception.getMessage().contains(invalidVersion));
        }
    }

    @Test
    void testSetVersionString_EdgeCaseValidFormats() {
        // Test edge cases that are actually valid
        String[] edgeCaseVersions = {".1", "0.1"};

        for (String version : edgeCaseVersions) {
            // These should not throw exceptions, though they may be normalized
            assertDoesNotThrow(() -> {
                entity.setVersion(version);
                assertNotNull(entity.getVersion());
            }, "Should not throw exception for edge case version: " + version);
        }
    }

    @Test
    void testSetVersionString_TrailingDotHandling() {
        // Test versions with trailing dots (BigDecimal accepts these, preserving original precision)
        String[][] trailingDotVersions = {
            {"10.", "10"},     // 10. -> 10 (normalized)
            {"1.0.", "1.0"}    // 1.0. -> 1.0 (normalized)
        };

        for (String[] versionPair : trailingDotVersions) {
            String input = versionPair[0];
            String expected = versionPair[1];

            entity.setVersion(input);
            assertEquals(expected, entity.getVersion(),
                "Trailing dot version " + input + " should normalize to " + expected + " (preserving original precision)");
        }
    }

    @Test
    void testGetVersionString_FromBigDecimal() {
        // Test getting string version from BigDecimal (preserving original precision)
        String[][] testPairs = {
            {"1.0", "1.0"}, {"2.1", "2.1"}, {"10.5", "10.5"},
            {"1", "1"}, {"0.1", "0.1"}, {"100.25", "100.25"}
        };

        for (String[] pair : testPairs) {
            BigDecimal bigDecimalVersion = new BigDecimal(pair[0]);
            String expected = pair[1];
            entity.setVersionBigDecimal(bigDecimalVersion);
            String stringVersion = entity.getVersion();
            assertNotNull(stringVersion);
            assertEquals(expected, stringVersion,
                "BigDecimal " + pair[0] + " should format to " + expected + " (preserving original precision)");
        }
    }

    @Test
    void testGetVersionString_NullBigDecimal() {
        // Test null BigDecimal handling
        entity.setVersionBigDecimal(null);
        assertNull(entity.getVersion());
    }

    @Test
    void testVersionRoundTrip_SimpleDecimalVersions() {
        // Test complete round-trip conversion for simple decimal versions (preserving original precision)
        String[][] versionPairs = {
            {"1.0", "1.0"}, {"2.1", "2.1"}, {"10.5", "10.5"},
            {"1", "1"}, {"0.1", "0.1"}, {"100.25", "100.25"}
        };

        for (String[] pair : versionPairs) {
            String input = pair[0];
            String expected = pair[1];
            entity.setVersion(input);
            String retrievedVersion = entity.getVersion();
            assertEquals(expected, retrievedVersion,
                "Round-trip conversion should preserve original precision for: " + input);
        }
    }

    @Test
    void testVersionRoundTrip_SemanticVersions() {
        // Test round-trip conversion for semantic versions (will be converted preserving converted precision)
        String[][] semanticVersions = {
            {"1.2.3", "1.0203"},   // 1.0203 -> 1.0203 (preserve converted precision)
            {"2.0.0", "2.0000"},   // 2.0000 -> 2.0000 (preserve converted precision)
            {"10.5.3", "10.0503"}  // 10.0503 -> 10.0503 (preserve converted precision)
        };

        for (String[] versionPair : semanticVersions) {
            String input = versionPair[0];
            String expected = versionPair[1];

            entity.setVersion(input);
            String retrievedVersion = entity.getVersion();
            assertEquals(expected, retrievedVersion,
                "Semantic version " + input + " should convert to " + expected + " (preserving converted precision)");
        }
    }

    @Test
    void testVersionRoundTrip_BigDecimalToStringToBigDecimal() {
        // Test round-trip from BigDecimal perspective
        BigDecimal[] testValues = {
            new BigDecimal("1.0"), new BigDecimal("2.1"), new BigDecimal("10.5"),
            new BigDecimal("1"), new BigDecimal("0.1")
        };

        for (BigDecimal originalBigDecimal : testValues) {
            entity.setVersionBigDecimal(originalBigDecimal);
            String stringVersion = entity.getVersion();
            entity.setVersion(stringVersion);
            BigDecimal retrievedBigDecimal = entity.getVersionBigDecimal();
            
            assertEquals(0, originalBigDecimal.compareTo(retrievedBigDecimal), 
                "Round-trip BigDecimal conversion should preserve value: " + originalBigDecimal);
        }
    }

    @Test
    void testVersionPrecisionHandling() {
        // Test that precision is maintained
        entity.setVersion("1.00");
        assertEquals("1.00", entity.getVersion());
        
        entity.setVersion("10.50");
        assertEquals("10.50", entity.getVersion());
        
        entity.setVersion("0.10");
        assertEquals("0.10", entity.getVersion());
    }

    @Test
    void testVersionScaleHandling() {
        // Test different scales (preserving original precision)
        entity.setVersion("1");
        assertEquals("1", entity.getVersion());

        entity.setVersion("1.0");
        assertEquals("1.0", entity.getVersion());

        entity.setVersion("1.00");
        assertEquals("1.00", entity.getVersion());
    }

    @Test
    void testBackwardCompatibility() {
        // Test that existing application code patterns still work (preserving original precision)
        entity.setVersion("1.0");

        // These are typical operations that application code might do
        String version = entity.getVersion();
        assertNotNull(version);
        assertEquals("1.0", version);

        // Version comparison (typical application logic) - comparing formatted strings
        entity.setVersion("2.0");
        assertTrue(entity.getVersion().compareTo("1.0") > 0);

        entity.setVersion("1.0");
        assertTrue(entity.getVersion().compareTo("2.0") < 0);

        entity.setVersion("1.0");
        assertEquals(0, entity.getVersion().compareTo("1.0"));
    }

    @Test
    void testVersionEquality() {
        // Test that semantically equivalent versions preserve their original precision
        entity.setVersion("1.0");
        String version1 = entity.getVersion();

        entity.setVersion("1.00");
        String version2 = entity.getVersion();

        // Each should preserve their original precision (BigDecimal preserves scale)
        assertNotEquals(version1, version2); // Different scales: "1.0" vs "1.00"
        assertEquals("1.0", version1);
        assertEquals("1.00", version2);
    }

    @Test
    void testLargeVersionNumbers() {
        // Test handling of large version numbers (preserving original precision)
        String largeVersion = "999999.999999";
        entity.setVersion(largeVersion);
        assertEquals("999999.999999", entity.getVersion()); // Preserve original precision
        assertNotNull(entity.getVersionBigDecimal());
    }

    @Test
    void testZeroVersions() {
        // Test zero and near-zero versions (preserving original precision)
        String[][] zeroVersionPairs = {
            {"0", "0"}, {"0.0", "0.0"}, {"0.1", "0.1"},
            {"0.01", "0.01"}, {"0.001", "0.001"}  // Preserve original precision
        };

        for (String[] pair : zeroVersionPairs) {
            String input = pair[0];
            String expected = pair[1];
            entity.setVersion(input);
            assertEquals(expected, entity.getVersion(),
                "Zero version " + input + " should preserve original precision: " + expected);
            assertNotNull(entity.getVersionBigDecimal());
        }
    }

    @Test
    void testVersionWithLeadingZeros() {
        // Test versions with leading zeros (normalized preserving original precision)
        entity.setVersion("01.0");
        // BigDecimal will normalize this, so we expect "1.0" (preserving original precision)
        assertEquals("1.0", entity.getVersion());

        entity.setVersion("001.000");
        // BigDecimal will normalize this, so we expect "1.000" (preserving original precision)
        assertEquals("1.000", entity.getVersion());
    }

    @Test
    void testDirectBigDecimalAccess() {
        // Test direct BigDecimal getter/setter (for JPA use)
        BigDecimal testValue = new BigDecimal("2.5");
        entity.setVersionBigDecimal(testValue);
        
        assertEquals(testValue, entity.getVersionBigDecimal());
        assertEquals("2.5", entity.getVersion()); // Preserve original precision
    }

    @Test
    void testExceptionMessageQuality() {
        // Test that exception messages are helpful
        String invalidVersion = "invalid-version";
        
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            entity.setVersion(invalidVersion);
        });
        
        String message = exception.getMessage();
        assertTrue(message.contains("Invalid version format"));
        assertTrue(message.contains(invalidVersion));
        assertTrue(message.contains("valid decimal number"));
        assertTrue(message.contains("1.0") || message.contains("examples"));
    }
}
