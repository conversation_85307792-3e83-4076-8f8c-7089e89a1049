package com.dell.it.hip.core;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class HIPClusterCoordinationService {

    private final String serviceManagerName;
    private final StringRedisTemplate redisTemplate; // Keep for pub/sub operations
    private final HIPRedisCompatibilityService redisService; // Use for basic operations
    private final RedisTemplate<String, HIPClusterEvent> hipEventRedisTemplate;
    private final RedisMessageListenerContainer redisListenerContainer;
    private final ChannelTopic clusterControlTopic;

    @Autowired
    public HIPClusterCoordinationService(
            @Value("${service.manager.name}") String serviceManagerName,
            StringRedisTemplate redisTemplate,
            HIPRedisCompatibilityService redisService,
            RedisTemplate<String, HIPClusterEvent> hipEventRedisTemplate,
            RedisMessageListenerContainer redisListenerContainer) {
        this.serviceManagerName = serviceManagerName;
        this.redisTemplate = redisTemplate; // Keep for pub/sub operations
        this.redisService = redisService; // Use for basic operations
        this.hipEventRedisTemplate = hipEventRedisTemplate;
        this.redisListenerContainer = redisListenerContainer;
        this.clusterControlTopic = new ChannelTopic(HIPRedisKeyUtil.clusterControlTopic(serviceManagerName));
    }

    // ========== ADAPTER PAUSE/RESUME ==========

    public void pause(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        redisService.set(key, "PAUSED");
        Map<String, Object> payload = Collections.singletonMap("paused", true);
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "PAUSE",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                payload
        ));
    }

    public void resume(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        redisService.delete(key);
        Map<String, Object> payload = Collections.singletonMap("paused", false);
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "RESUME",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                payload
        ));
    }

    // ========== HANDLER PAUSE/RESUME ==========

    public void pauseHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = HIPRedisKeyUtil.handlerPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        redisService.set(key, "PAUSED");
        Map<String, Object> payload = Collections.singletonMap("paused", true);
        publishClusterEvent(HIPClusterEvent.forHandlerTarget(
                "PAUSE",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getId(),
                payload
        ));
    }

    public void resumeHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = HIPRedisKeyUtil.handlerPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getId());
        redisService.delete(key);
        Map<String, Object> payload = Collections.singletonMap("paused", false);
        publishClusterEvent(HIPClusterEvent.forHandlerTarget(
                "RESUME",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getId(),
                payload
        ));
    }

    public void shutdownHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        resumeHandler(def, ref);
        Map<String, Object> payload = Collections.singletonMap("shutdown", true);
        publishClusterEvent(HIPClusterEvent.forHandlerTarget(
                "SHUTDOWN",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getId(),
                payload
        ));
    }

    public void shutdownAdapter(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        removeThrottle(def, ref);
        resume(def, ref); // Ensure not paused
        Map<String, Object> payload = Collections.singletonMap("shutdown", true);
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "SHUTDOWN",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                payload
        ));
    }

    // ========== THROTTLE - per instance ==========

    public void setThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        if (settings == null) {
            redisService.delete(key);
        } else {
            redisService.set(key, settings.toString());
        }
        Map<String, Object> payload = Collections.singletonMap("settings", settings);
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "THROTTLE_UPDATE",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                payload
        ));
    }

    public void removeThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        redisService.delete(key);
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "THROTTLE_REMOVED",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                Collections.emptyMap()
        ));
    }

    // ========== RATE LIMIT CHECK ==========

    public boolean isInputAllowed(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings) {
        if (settings == null || settings.getMaxMessagesPerPeriod() <= 0 || settings.getPeriodSeconds() <= 0) {
            return true;
        }
        String key = HIPRedisKeyUtil.rateLimitKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        Long count = redisService.increment(key);
        if (count != null && count == 1L) {
            redisService.expire(key, Duration.ofSeconds(settings.getPeriodSeconds()));
        }
        boolean allowed = count != null && count <= settings.getMaxMessagesPerPeriod();
        if (!allowed) {
            Map<String, Object> payload = Collections.singletonMap("currentCount", count);
            publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                    "THROTTLED",
                    def.getHipIntegrationName(),
                    def.getVersion(),
                    ref.getPropertyRef(),
                    payload
            ));
        }
        return allowed;
    }

    // ========== DEDUPLICATION ==========

    public boolean isDuplicate(HIPIntegrationDefinition def, AdapterConfigRef ref, String dedupId, long ttlSeconds) {
        String key = HIPRedisKeyUtil.dedupKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef()) + ":" + dedupId;
        Boolean alreadySeen = redisService.hasKey(key);
        if (alreadySeen == null || !alreadySeen) {
            redisService.set(key, "1", Duration.ofSeconds(ttlSeconds));
            return false;
        }
        return true;
    }

    // ========== REGISTRATION/UNREGISTRATION EVENTS ==========

    public void broadcastRegistration(HIPIntegrationDefinition def) {
        publishClusterEvent(HIPClusterEvent.forTarget(
                "REGISTERED",
                "ADAPTER",
                def.getHipIntegrationName(),
                def.getVersion(),
                "*",
                Collections.emptyMap()
        ));
    }

    public void broadcastUnregistration(HIPIntegrationDefinition def) {
        publishClusterEvent(HIPClusterEvent.forTarget(
                "UNREGISTERED",
                "ADAPTER",
                def.getHipIntegrationName(),
                def.getVersion(),
                "*",
                Collections.emptyMap()
        ));
    }

    // ========== REFILL AND CUSTOM EVENTS ==========

    public void broadcastRefill(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                "REFILL",
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef(),
                Collections.emptyMap()
        ));
    }

    public void broadcastCustomEvent(String eventType, HIPIntegrationDefinition def, AdapterConfigRef ref, Map<String, Object> payload) {
        publishClusterEvent(HIPClusterEvent.forAdapterTarget(
                eventType,
                def.getHipIntegrationName(),
                def.getVersion(),
                ref != null ? ref.getPropertyRef() : "*",
                payload != null ? payload : Collections.emptyMap()
        ));
    }

    // ========== PUBLISH CLUSTER EVENT ==========

    public void publishClusterEvent(HIPClusterEvent event) {
        hipEventRedisTemplate.convertAndSend(clusterControlTopic.getTopic(), event);
    }

    // ========== EVENT LISTENER REGISTRATION ==========

    public void registerClusterEventListener(Consumer<HIPClusterEvent> callback) {
        redisListenerContainer.addMessageListener(
                (MessageListener) (message, pattern) -> {
                    try {
                        // Use Jackson2JsonRedisSerializer for deserialization
                        // Must match the serializer configured in hipEventRedisTemplate
                        Jackson2JsonRedisSerializer<HIPClusterEvent> serializer =
                                new Jackson2JsonRedisSerializer<>(HIPClusterEvent.class);
                        HIPClusterEvent event = serializer.deserialize(message.getBody());
                        if (event != null) {
                            callback.accept(event);
                        }
                    } catch (Exception e) {
                        // log error or rethrow as needed
                        e.printStackTrace();
                    }
                },
                clusterControlTopic
        );
    }

    // ========== STATE QUERIES ==========

    public boolean isAdapterPaused(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.adapterPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        return Boolean.TRUE.equals(redisService.hasKey(key));
    }

    public Boolean isHandlerPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = HIPRedisKeyUtil.handlerPauseKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        return redisService.hasKey(key);
    }

    public boolean isAdapterThrottled(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef());
        return Boolean.TRUE.equals(redisService.hasKey(key));
    }

    // ========== UTILITY ==========

    public String integrationAdapterKey(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        return def.getHipIntegrationName() + ":" + def.getVersion() + ":" + ref.getPropertyRef();
    }
}