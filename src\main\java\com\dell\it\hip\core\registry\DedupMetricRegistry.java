package com.dell.it.hip.core.registry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;

@Component
public class DedupMetricRegistry {

    private final HIPRedisCompatibilityService redisService;

    @Autowired
    public DedupMetricRegistry(HIPRedisCompatibilityService redisService) {
        this.redisService = redisService;
    }

    private String getDupKey(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        return String.format("dedup:dup:%s:%s:%s:%s",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef());
    }
    private String getInsertKey(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        return String.format("dedup:ins:%s:%s:%s:%s",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef());
    }

    public void incrementDuplicate(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        redisService.increment(getDupKey(def, ref));
    }
    public void incrementInserted(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        redisService.increment(getInsertKey(def, ref));
    }
    public long getDuplicateCount(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        String key = getDupKey(def, ref);
        String val = redisService.get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
    public long getInsertedCount(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        String key = getInsertKey(def, ref);
        String val = redisService.get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
}