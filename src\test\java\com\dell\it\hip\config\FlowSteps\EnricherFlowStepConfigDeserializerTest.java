package com.dell.it.hip.config.FlowSteps;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import java.io.IOException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class EnricherFlowStepConfigDeserializerTest {

    private EnricherFlowStepConfigDeserializer deserializer;
    private ObjectMapper mapper;

    @BeforeEach
    void setup() {
        deserializer = new EnricherFlowStepConfigDeserializer();
        mapper = new ObjectMapper();
    }

    @Test
    void testDeserialize_withFullFlatStructure() throws Exception {
        String flatJson = "{\n" +
            "  \"configurationList[0].action\": \"ENRICH\",\n" +
            "  \"configurationList[0].targetFileNameSetting\": true,\n" +
            "  \"configurationList[0].targetFileNameConfig.fileSeparator\": \"-\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileExtension\": \".xml\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[0].derivedFrom\": \"FixedText\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[0].valueText\": \"INV\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[0].partNumber\": \"1\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[1].derivedFrom\": \"DateandTime\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[1].valueText\": \"20250724\",\n" +
            "  \"configurationList[0].targetFileNameConfig.fileNameSection[1].partNumber\": \"2\",\n" +
            "  \"defaultConfig.action\": \"SKIP\",\n" +
            "  \"defaultConfig.targetFileNameSetting\": false\n" +
            "}";

        JsonParser parser = mapper.getFactory().createParser(flatJson);
        parser.nextToken(); // Move to first token

        EnricherFlowStepConfig cfg = deserializer.deserialize(parser, mock(DeserializationContext.class));

        assertNotNull(cfg);
        assertEquals(1, cfg.getDocTypeConfigs().size());

        EnricherFlowStepConfig.DocTypeEnricherConfig docConfig = cfg.getDocTypeConfigs().get(0);
        assertEquals(EnricherFlowStepConfig.EnricherBehavior.ENRICH, docConfig.getBehavior());
        assertTrue(docConfig.isTargetFileNameSetting());
        assertEquals("-", docConfig.getTargetFileNameConfig().getFileSeparator());
        assertEquals(".xml", docConfig.getTargetFileNameConfig().getFileExtension());
        assertEquals(2, docConfig.getTargetFileNameConfig().getFileNameSection().size());
        assertEquals("FixedText", docConfig.getTargetFileNameConfig().getFileNameSection().get(0).getDerivedFrom());
        assertEquals("INV", docConfig.getTargetFileNameConfig().getFileNameSection().get(0).getValueText());
        assertEquals("1", docConfig.getTargetFileNameConfig().getFileNameSection().get(0).getPartNumber());

        assertEquals(EnricherFlowStepConfig.EnricherBehavior.SKIP, cfg.getDefaultConfig().getBehavior());
        assertFalse(cfg.getDefaultConfig().isTargetFileNameSetting());
    }

  /*  @Test
    void testExtractIndex_andExtractPath() {
        // Extract index
        int idx = deserializer.extractIndex("configurationList[23].someField", "configurationList");
        assertEquals(23, idx);

        // Extract path
        String path1 = deserializer.extractPath("configurationList[23].someField", "configurationList");
        assertEquals("someField", path1);

        String path2 = deserializer.extractPath("configurationList[0]", "configurationList");
        assertEquals("", path2);
    }

    @Test
    void testInsert_andEnsure_methods() throws IOException {
        ObjectNode root = mapper.createObjectNode();

        // Insert simple property
        deserializer.insert(root, "simpleField", mapper.readTree("\"value\""), mapper);
        assertEquals("value", root.get("simpleField").asText());

        // Insert nested object property
        deserializer.insert(root, "nested.field", mapper.readTree("123"), mapper);
        assertEquals(123, root.get("nested").get("field").intValue());

        // Insert into array with index 0 and nested object inside array
        deserializer.insert(root, "arr[0].field", mapper.readTree("\"abc\""), mapper);
        assertEquals("abc", root.get("arr").get(0).get("field").asText());

        // Insert into array with index 2 (should add null at 1)
        deserializer.insert(root, "arr[2]", mapper.readTree("true"), mapper);
        assertTrue(root.get("arr").get(2).asBoolean());
        assertTrue(root.get("arr").get(1).isNull());

        // Ensure method: test with empty array
        com.fasterxml.jackson.databind.node.ArrayNode arrayNode = mapper.createArrayNode();
        deserializer.ensure(arrayNode, 2);
        assertEquals(3, arrayNode.size());
        assertTrue(arrayNode.get(0).isNull());
        assertTrue(arrayNode.get(2).isNull());
    }*/
    
    @Test
    void testEnumDeserialization() throws Exception {
        String json = "{\"action\":\"SKIP\"}";
        EnricherFlowStepConfig.DefaultEnricherConfig defCfg = mapper.readValue(json, EnricherFlowStepConfig.DefaultEnricherConfig.class);
        assertEquals(EnricherFlowStepConfig.EnricherBehavior.SKIP, defCfg.getBehavior());
    }


    @Test
    void testDeserialize_emptyInput_returnsEmptyConfig() throws IOException {
        String emptyJson = "{}";
        JsonParser parser = mapper.getFactory().createParser(emptyJson);
        parser.nextToken();

        EnricherFlowStepConfig cfg = deserializer.deserialize(parser, mock(DeserializationContext.class));

        assertNotNull(cfg);
        assertTrue(cfg.getDocTypeConfigs().isEmpty());
        assertNull(cfg.getDefaultConfig());
    }
}