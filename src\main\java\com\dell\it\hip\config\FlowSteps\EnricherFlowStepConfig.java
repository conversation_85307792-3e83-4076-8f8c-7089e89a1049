package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = EnricherFlowStepConfigDeserializer.class)
public class EnricherFlowStepConfig extends FlowStepConfig {
	@JsonProperty("configurationList")
    private List<DocTypeEnricherConfig> docTypeConfigs = new ArrayList<>();
    private DefaultEnricherConfig defaultConfig;

    @Data
    public static class DocTypeEnricherConfig extends DocTypeConfig {
    	@JsonProperty("action")
        private EnricherBehavior behavior = EnricherBehavior.ENRICH;
        private boolean targetFileNameSetting = false;
        private TargetFileNameConfig targetFileNameConfig;
        private Map<String, Object> enrichmentHeaders; // e.g., {"AS2-SenderID": "...", "AS2-ReceiverID": "..."}
        private String dataFormat; // optional filter for data format
    }

    @Data
    public static class DefaultEnricherConfig extends DocTypeConfig{
    	@JsonProperty("action")
        private EnricherBehavior behavior = EnricherBehavior.ENRICH;
        private boolean targetFileNameSetting = false;
        private TargetFileNameConfig targetFileNameConfig;
        private Map<String, Object> enrichmentHeaders;
        private String dataFormat;
    }

    public enum EnricherBehavior { ENRICH, SKIP, TERMINATE }



}

class EnricherFlowStepConfigDeserializer extends JsonDeserializer<EnricherFlowStepConfig> {

    @Override
    public EnricherFlowStepConfig deserialize(JsonParser parser, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) parser.getCodec();
        JsonNode flat = mapper.readTree(parser);

        EnricherFlowStepConfig cfg = new EnricherFlowStepConfig();
        TreeMap<Integer, ObjectNode> docNodes = new TreeMap<>();
        ObjectNode defaultNode = mapper.createObjectNode();

        for (Iterator<Map.Entry<String, JsonNode>> it = flat.fields(); it.hasNext();) {
            Map.Entry<String, JsonNode> e = it.next();
            String key = e.getKey();
            JsonNode val = e.getValue();

            if (key.startsWith("configurationList[")) {
                int idx = extractIndex(key, "configurationList");
                String path = extractPath(key, "configurationList");
                insert(docNodes.computeIfAbsent(idx, k -> mapper.createObjectNode()), path, val, mapper);

            } else if (key.startsWith("defaultConfig.")) {
                String path = key.substring("defaultConfig.".length());
                insert(defaultNode, path, val, mapper);
            }
        }

        cfg.setDocTypeConfigs(mapToList(docNodes, mapper, EnricherFlowStepConfig.DocTypeEnricherConfig.class));

        if (!defaultNode.isEmpty()) {
            cfg.setDefaultConfig(mapper.treeToValue(defaultNode, EnricherFlowStepConfig.DefaultEnricherConfig.class));
        }

        return cfg;
    }

    private int extractIndex(String key, String prefix) {
        int s = key.indexOf('[', prefix.length()) + 1;
        int e = key.indexOf(']', s);
        return Integer.parseInt(key.substring(s, e));
    }

    private String extractPath(String key, String prefix) {
        int end = key.indexOf(']', prefix.length()) + 1;
        String sub = key.substring(end);
        return sub.startsWith(".") ? sub.substring(1) : sub;
    }

    private <T> List<T> mapToList(Map<Integer, ObjectNode> map, ObjectMapper mapper, Class<T> cls) throws IOException {
        List<T> list = new ArrayList<>();
        for (ObjectNode node : map.values()) {
            list.add(mapper.treeToValue(node, cls));
        }
        return list;
    }

    private void insert(ObjectNode curr, String path, JsonNode val, ObjectMapper mapper) {
        String[] parts = path.split("\\.");
        for (int i = 0; i < parts.length; i++) {
            String p = parts[i];

            if (p.contains("[") && p.contains("]")) {
                String name = p.substring(0, p.indexOf('['));
                int idx = Integer.parseInt(p.substring(p.indexOf('[') + 1, p.indexOf(']')));
                ArrayNode arr = curr.withArray(name);
                ensure(arr, idx);

                if (i == parts.length - 1) {
                    arr.set(idx, val);
                } else {
                    JsonNode nxt = arr.get(idx);
                    if (nxt == null || !nxt.isObject()) {
                        nxt = mapper.createObjectNode();
                        arr.set(idx, nxt);
                    }
                    curr = (ObjectNode) nxt;
                }
            } else {
                if (i == parts.length - 1) {
                    curr.set(p, val);
                } else {
                    JsonNode nxt = curr.get(p);
                    if (nxt == null || !nxt.isObject()) {
                        nxt = mapper.createObjectNode();
                        curr.set(p, nxt);
                    }
                    curr = (ObjectNode) nxt;
                }
            }
        }
    }

    private void ensure(ArrayNode arr, int idx) {
        while (arr.size() <= idx) {
            arr.addNull();
        }
    }}

