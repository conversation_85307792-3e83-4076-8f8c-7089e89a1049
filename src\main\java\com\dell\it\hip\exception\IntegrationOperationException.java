package com.dell.it.hip.exception;

import com.dell.it.hip.util.security.InputValidationUtil;

/**
 * Exception thrown when integration lifecycle operations fail (unregister, pause, resume, shutdown).
 */
public class IntegrationOperationException extends RuntimeException {
    
    private final String integrationName;
    private final String version;
    private final String operation;
    
    public IntegrationOperationException(String message) {
        super(message);
        this.integrationName = null;
        this.version = null;
        this.operation = null;
    }
    
    public IntegrationOperationException(String message, Throwable cause) {
        super(message, cause);
        this.integrationName = null;
        this.version = null;
        this.operation = null;
    }
    
    public IntegrationOperationException(String operation, String integrationName, String version, String message) {
        super(String.format("Failed to %s integration %s:%s - %s",
            InputValidationUtil.sanitizeForErrorMessage(operation),
            InputValidationUtil.sanitizeForErrorMessage(integrationName),
            InputValidationUtil.sanitizeForErrorMessage(version),
            InputValidationUtil.sanitizeForErrorMessage(message)));
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }

    public IntegrationOperationException(String operation, String integrationName, String version, String message, Throwable cause) {
        super(String.format("Failed to %s integration %s:%s - %s",
            InputValidationUtil.sanitizeForErrorMessage(operation),
            InputValidationUtil.sanitizeForErrorMessage(integrationName),
            InputValidationUtil.sanitizeForErrorMessage(version),
            InputValidationUtil.sanitizeForErrorMessage(message)), cause);
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }

    public IntegrationOperationException(String operation, String integrationName, String version, Throwable cause) {
        super(String.format("Failed to %s integration %s:%s: %s",
            InputValidationUtil.sanitizeForErrorMessage(operation),
            InputValidationUtil.sanitizeForErrorMessage(integrationName),
            InputValidationUtil.sanitizeForErrorMessage(version),
            InputValidationUtil.sanitizeForErrorMessage(cause.getMessage())), cause);
        this.operation = operation;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public String getIntegrationName() {
        return integrationName;
    }
    
    public String getVersion() {
        return version;
    }
    
    public String getOperation() {
        return operation;
    }
}
