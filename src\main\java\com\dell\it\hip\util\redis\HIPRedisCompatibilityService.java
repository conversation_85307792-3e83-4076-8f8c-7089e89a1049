package com.dell.it.hip.util.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Backward compatibility service that provides traditional Redis operations
 * while internally using hash-based storage through HIPRedisHashService.
 * 
 * This service maintains the existing Redis operation interfaces while
 * transparently migrating to hash-based storage for better organization
 * and namespace isolation.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class HIPRedisCompatibilityService {

    private static final Logger logger = LoggerFactory.getLogger(HIPRedisCompatibilityService.class);

    private final HIPRedisHashService hashService;
    private final StringRedisTemplate stringRedisTemplate;
    private final RedisTemplate<String, byte[]> byteRedisTemplate;

    public HIPRedisCompatibilityService(
            HIPRedisHashService hashService,
            StringRedisTemplate stringRedisTemplate,
            RedisTemplate<String, byte[]> byteRedisTemplate) {
        this.hashService = hashService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.byteRedisTemplate = byteRedisTemplate;
        logger.info("HIPRedisCompatibilityService initialized");
    }

    // ========== VALUE OPERATIONS (opsForValue() equivalents) ==========

    /**
     * Set a key-value pair (maps to hash field-value)
     */
    public void set(String key, String value) {
        hashService.set(key, value);
    }

    /**
     * Set a key-value pair with expiration
     */
    public void set(String key, String value, Duration timeout) {
        hashService.set(key, value, timeout);
    }

    /**
     * Set a key-value pair with expiration in time units
     */
    public void set(String key, String value, long timeout, TimeUnit unit) {
        hashService.set(key, value, timeout, unit);
    }

    /**
     * Get a value by key (maps to hash field lookup)
     */
    public String get(String key) {
        return hashService.get(key);
    }

    /**
     * Increment a numeric value
     */
    public Long increment(String key) {
        return hashService.increment(key);
    }

    /**
     * Increment a numeric value by a specific amount
     */
    public Long increment(String key, long delta) {
        return hashService.increment(key, delta);
    }

    /**
     * Set if absent (atomic operation)
     */
    public Boolean setIfAbsent(String key, String value) {
        return hashService.setIfAbsent(key, value);
    }

    /**
     * Set if absent with expiration
     */
    public Boolean setIfAbsent(String key, String value, Duration timeout) {
        return hashService.setIfAbsent(key, value, timeout);
    }

    /**
     * Set if absent with expiration in time units
     */
    public Boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit) {
        return hashService.setIfAbsent(key, value, timeout, unit);
    }

    // ========== BYTE ARRAY OPERATIONS ==========

    /**
     * Set byte array value
     */
    public void setBytes(String key, byte[] value) {
        hashService.setBytes(key, value);
    }

    /**
     * Get byte array value
     */
    public byte[] getBytes(String key) {
        return hashService.getBytes(key);
    }

    // ========== KEY OPERATIONS ==========

    /**
     * Check if a key exists (maps to hash field existence)
     */
    public Boolean hasKey(String key) {
        return hashService.hasField(key);
    }

    /**
     * Delete a key (maps to hash field deletion)
     */
    public Boolean delete(String key) {
        Long result = hashService.delete(key);
        return result != null && result > 0;
    }

    /**
     * Delete multiple keys
     */
    public Long delete(String... keys) {
        return hashService.delete(keys);
    }

    /**
     * Get all keys matching a pattern
     * Note: This operation is less efficient with hash-based storage
     * and should be used sparingly
     */
    public Set<Object> keys(String pattern) {
        logger.warn("keys() operation with pattern '{}' is less efficient with hash-based storage", pattern);
        Set<Object> allFields = hashService.getFields();
        
        // Simple pattern matching - convert Redis pattern to regex
        String regex = pattern.replace("*", ".*").replace("?", ".");
        
        return allFields.stream()
                .filter(field -> field.toString().matches(regex))
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Set expiration for a key
     * Note: With hash-based storage, expiration is handled differently
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        logger.debug("Setting expiration for key: {} ({} {})", key, timeout, unit);
        
        // Get current value and re-set with expiration
        String currentValue = hashService.get(key);
        if (currentValue != null) {
            hashService.set(key, currentValue, timeout, unit);
            return true;
        }
        return false;
    }

    /**
     * Set expiration for a key with Duration
     */
    public Boolean expire(String key, Duration timeout) {
        logger.debug("Setting expiration for key: {} ({})", key, timeout);

        // Get current value and re-set with expiration
        String currentValue = hashService.get(key);
        if (currentValue != null) {
            hashService.set(key, currentValue, timeout);
            return true;
        }
        return false;
    }

    // ========== ZSET OPERATIONS (for StrictOrderProcessorFlowStepStrategy) ==========

    /**
     * Add to sorted set with score
     * Note: ZSet operations require special handling in hash-based storage
     */
    public Boolean zSetAdd(String key, String value, double score) {
        logger.debug("ZSet add operation: {} -> {} (score: {})", key, value, score);
        
        // For ZSet operations, we'll use a hybrid approach:
        // Store ZSet data in traditional Redis keys for now, as hash-based ZSets are complex
        // This can be optimized later with custom ZSet implementation in hash fields
        return stringRedisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * Get range from sorted set by score
     */
    public Set<String> zSetRangeByScore(String key, double min, double max) {
        logger.debug("ZSet range by score: {} ({} to {})", key, min, max);
        return stringRedisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    /**
     * Remove from sorted set
     */
    public Long zSetRemove(String key, Object... values) {
        logger.debug("ZSet remove: {} (values: {})", key, values.length);
        return stringRedisTemplate.opsForZSet().remove(key, values);
    }

    /**
     * Get sorted set operations for direct access when needed
     */
    public ZSetOperations<String, String> opsForZSet() {
        logger.debug("Providing direct ZSet operations access");
        return stringRedisTemplate.opsForZSet();
    }

    // ========== LIST OPERATIONS (for queue-like operations) ==========

    /**
     * Push to list (left push)
     */
    public Long listLeftPush(String key, String value) {
        logger.debug("List left push: {} -> {}", key, value);
        // For list operations, use traditional Redis for now
        return stringRedisTemplate.opsForList().leftPush(key, value);
    }

    /**
     * Pop from list (right pop)
     */
    public String listRightPop(String key) {
        logger.debug("List right pop: {}", key);
        return stringRedisTemplate.opsForList().rightPop(key);
    }

    /**
     * Get list size
     */
    public Long listSize(String key) {
        logger.debug("List size: {}", key);
        return stringRedisTemplate.opsForList().size(key);
    }

    // ========== SET OPERATIONS ==========

    /**
     * Add to set
     */
    public Long setAdd(String key, String... values) {
        logger.debug("Set add: {} (values: {})", key, values.length);
        // For set operations, use traditional Redis for now
        return stringRedisTemplate.opsForSet().add(key, values);
    }

    /**
     * Check if set contains member
     */
    public Boolean setIsMember(String key, Object value) {
        logger.debug("Set is member: {} -> {}", key, value);
        return stringRedisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * Get all set members
     */
    public Set<String> setMembers(String key) {
        logger.debug("Set members: {}", key);
        return stringRedisTemplate.opsForSet().members(key);
    }

    /**
     * Remove from set
     */
    public Long setRemove(String key, Object... values) {
        logger.debug("Set remove: {} (values: {})", key, values.length);
        return stringRedisTemplate.opsForSet().remove(key, values);
    }

    // ========== UTILITY METHODS ==========

    /**
     * Get the underlying hash service
     */
    public HIPRedisHashService getHashService() {
        return hashService;
    }

    /**
     * Get the cache hash key being used
     */
    public String getCacheHashKey() {
        return hashService.getCacheHashKey();
    }

    /**
     * Cleanup expired fields
     */
    public void cleanupExpiredFields() {
        hashService.cleanupExpiredFields();
    }

    /**
     * Get statistics about the hash cache
     */
    public CacheStatistics getStatistics() {
        try {
            Long totalFields = hashService.size();
            Set<Object> fields = hashService.getFields();
            
            long expiredFields = fields.stream()
                    .mapToLong(field -> {
                        String fieldStr = field.toString();
                        if (fieldStr.endsWith(":expires")) {
                            try {
                                String value = hashService.get(fieldStr);
                                if (value != null) {
                                    long expirationTime = Long.parseLong(value);
                                    return System.currentTimeMillis() > expirationTime ? 1 : 0;
                                }
                            } catch (Exception e) {
                                logger.debug("Error checking expiration for field: {}", fieldStr);
                            }
                        }
                        return 0;
                    })
                    .sum();

            return new CacheStatistics(totalFields, expiredFields, hashService.getCacheHashKey());
        } catch (Exception e) {
            logger.error("Failed to get cache statistics - {}", e.getMessage(), e);
            return new CacheStatistics(0L, 0L, hashService.getCacheHashKey());
        }
    }

    /**
     * Ping Redis to test connectivity
     * Used for health checks
     */
    public String ping() {
        try {
            return stringRedisTemplate.getConnectionFactory()
                    .getConnection()
                    .ping();
        } catch (Exception e) {
            logger.error("Redis ping failed: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Cache statistics holder
     */
    public static class CacheStatistics {
        private final Long totalFields;
        private final Long expiredFields;
        private final String cacheHashKey;

        public CacheStatistics(Long totalFields, Long expiredFields, String cacheHashKey) {
            this.totalFields = totalFields;
            this.expiredFields = expiredFields;
            this.cacheHashKey = cacheHashKey;
        }

        public Long getTotalFields() { return totalFields; }
        public Long getExpiredFields() { return expiredFields; }
        public String getCacheHashKey() { return cacheHashKey; }
        public Long getActiveFields() { return totalFields - expiredFields; }

        @Override
        public String toString() {
            return String.format("CacheStatistics{hashKey='%s', total=%d, active=%d, expired=%d}", 
                    cacheHashKey, totalFields, getActiveFields(), expiredFields);
        }
    }
}
