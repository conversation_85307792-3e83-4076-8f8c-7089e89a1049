package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that the sequence creation fix provides proper database isolation
 */
@Execution(ExecutionMode.CONCURRENT) // Enable parallel execution
public class SequenceIsolationTest {
    
    private static final String H2_DB_DRIVER = "org.h2.Driver";
    
    // Track database URLs used across all test instances
    private static final Set<String> usedUrls = ConcurrentHashMap.newKeySet();
    
    // Generate unique database URL for each test instance
    private final String dbUrl = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
    
    @Test
    void testUniqueDatabase() throws Exception {
        // Verify that each test instance gets a unique database URL
        assertTrue(usedUrls.add(dbUrl), "Database URL should be unique: " + dbUrl);
        
        System.out.println("Test using database: " + dbUrl);
        
        // Verify database can be created and used
        setupAndTestDatabase();
    }
    
    @RepeatedTest(5)
    void testRepeatedDatabaseCreation() throws Exception {
        // This test runs 5 times to verify that repeated execution works
        assertTrue(usedUrls.add(dbUrl), "Each repeated test should get unique database URL");
        
        setupAndTestDatabase();
    }
    
    @Test
    void testParallelDatabaseAccess() throws Exception {
        // This test verifies parallel execution works without conflicts
        assertTrue(usedUrls.add(dbUrl), "Parallel test should get unique database URL");
        
        setupAndTestDatabase();
        
        // Simulate some work to test parallel execution
        Thread.sleep(100);
        
        // Verify database is still accessible
        verifySequenceValues();
    }
    
    @Test
    void testSequenceCreationAndCleanup() throws Exception {
        assertTrue(usedUrls.add(dbUrl), "Database URL should be unique");
        
        // Test the complete sequence creation and cleanup cycle
        Class.forName(H2_DB_DRIVER);
        
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            // First, try to create sequences (should succeed)
            createSequencesAndTables(conn);
            
            // Verify sequences work
            long value1 = getNextSequenceValue(conn, "TGTS_LOOKUP_SEQ");
            assertEquals(1000, value1, "First sequence value should be 1000");
            
            long value2 = getNextSequenceValue(conn, "TGTS_LOOKUP_SEQ");
            assertEquals(1001, value2, "Second sequence value should be 1001");
            
            // Now test cleanup and recreation
            cleanupDatabase(conn);
            createSequencesAndTables(conn);
            
            // After cleanup and recreation, sequence should start over
            long value3 = getNextSequenceValue(conn, "TGTS_LOOKUP_SEQ");
            assertEquals(1000, value3, "After cleanup, sequence should restart at 1000");
        }
    }
    
    @Test
    void testConcurrentSequenceAccess() throws Exception {
        assertTrue(usedUrls.add(dbUrl), "Database URL should be unique");
        
        setupAndTestDatabase();
        
        // Test concurrent access to sequences within the same database
        Class.forName(H2_DB_DRIVER);
        
        try (Connection conn1 = DriverManager.getConnection(dbUrl);
             Connection conn2 = DriverManager.getConnection(dbUrl)) {
            
            // Both connections should access the same sequence
            long value1 = getNextSequenceValue(conn1, "TGTS_LOOKUP_SEQ");
            long value2 = getNextSequenceValue(conn2, "TGTS_LOOKUP_SEQ");
            
            // Values should be sequential
            assertEquals(value1 + 1, value2, "Concurrent sequence access should be sequential");
        }
    }
    
    private void setupAndTestDatabase() throws Exception {
        Class.forName(H2_DB_DRIVER);
        
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            // Test cleanup and creation cycle
            cleanupDatabase(conn);
            createSequencesAndTables(conn);
            
            // Verify sequences work
            verifySequenceValues(conn);
            
            // Verify tables can be used
            verifyTableCreation(conn);
        }
    }
    
    private void cleanupDatabase(Connection conn) throws SQLException {
        String[] cleanupStatements = {
            "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_SEQ",
            "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_ROW_SEQ",
            "DROP TABLE IF EXISTS TGTS_LOOKUP_COLUMN",
            "DROP TABLE IF EXISTS TGTS_LOOKUP_ROW", 
            "DROP TABLE IF EXISTS TGTS_LOOKUP"
        };
        
        try (Statement stmt = conn.createStatement()) {
            for (String sql : cleanupStatements) {
                try {
                    stmt.execute(sql);
                } catch (SQLException e) {
                    // Ignore errors for non-existent objects
                }
            }
        }
    }
    
    private void createSequencesAndTables(Connection conn) throws SQLException {
        String[] createStatements = {
            "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
            "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
            
            "CREATE TABLE TGTS_LOOKUP (" +
            "  TABLE_ID BIGINT PRIMARY KEY," +
            "  SOLUTION_ID VARCHAR(255) NOT NULL," +
            "  TABLE_NAME VARCHAR(255) NOT NULL," +
            "  CREATE_DATE TIMESTAMP NOT NULL," +
            "  AVAILABLE_DATE TIMESTAMP" +
            ")",
            
            "CREATE TABLE TGTS_LOOKUP_ROW (" +
            "  ROW_ID BIGINT PRIMARY KEY," +
            "  FK_TABLE_ID BIGINT NOT NULL," +
            "  ROW_TYPE INT NOT NULL," +
            "  FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)" +
            ")",
            
            "CREATE TABLE TGTS_LOOKUP_COLUMN (" +
            "  FK_ROW_ID BIGINT NOT NULL," +
            "  COL_NUM INT NOT NULL," +
            "  COL_VAL VARCHAR(4000)," +
            "  PRIMARY KEY (FK_ROW_ID, COL_NUM)," +
            "  FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID)" +
            ")"
        };
        
        try (Statement stmt = conn.createStatement()) {
            for (String sql : createStatements) {
                stmt.execute(sql);
            }
        }
    }
    
    private void verifySequenceValues() throws Exception {
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            verifySequenceValues(conn);
        }
    }
    
    private void verifySequenceValues(Connection conn) throws SQLException {
        // Test TGTS_LOOKUP_SEQ
        long value1 = getNextSequenceValue(conn, "TGTS_LOOKUP_SEQ");
        assertTrue(value1 >= 1000, "TGTS_LOOKUP_SEQ should start at 1000 or higher");
        
        long value2 = getNextSequenceValue(conn, "TGTS_LOOKUP_SEQ");
        assertEquals(value1 + 1, value2, "Sequence values should be sequential");
        
        // Test TGTS_LOOKUP_ROW_SEQ
        long rowValue1 = getNextSequenceValue(conn, "TGTS_LOOKUP_ROW_SEQ");
        assertTrue(rowValue1 >= 2000, "TGTS_LOOKUP_ROW_SEQ should start at 2000 or higher");
        
        long rowValue2 = getNextSequenceValue(conn, "TGTS_LOOKUP_ROW_SEQ");
        assertEquals(rowValue1 + 1, rowValue2, "Row sequence values should be sequential");
    }
    
    private long getNextSequenceValue(Connection conn, String sequenceName) throws SQLException {
        String sql = "SELECT NEXT VALUE FOR " + sequenceName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            assertTrue(rs.next(), "Sequence query should return a result");
            return rs.getLong(1);
        }
    }
    
    private void verifyTableCreation(Connection conn) throws SQLException {
        // Verify tables exist and can be used
        DatabaseMetaData metaData = conn.getMetaData();
        
        // Check TGTS_LOOKUP table
        try (ResultSet tables = metaData.getTables(null, null, "TGTS_LOOKUP", null)) {
            assertTrue(tables.next(), "TGTS_LOOKUP table should exist");
        }
        
        // Check TGTS_LOOKUP_ROW table
        try (ResultSet tables = metaData.getTables(null, null, "TGTS_LOOKUP_ROW", null)) {
            assertTrue(tables.next(), "TGTS_LOOKUP_ROW table should exist");
        }
        
        // Check TGTS_LOOKUP_COLUMN table
        try (ResultSet tables = metaData.getTables(null, null, "TGTS_LOOKUP_COLUMN", null)) {
            assertTrue(tables.next(), "TGTS_LOOKUP_COLUMN table should exist");
        }
        
        // Test basic insert operations
        testBasicInsertOperations(conn);
    }
    
    private void testBasicInsertOperations(Connection conn) throws SQLException {
        // Test that we can insert data using sequences
        String insertLookup = "INSERT INTO TGTS_LOOKUP (TABLE_ID, SOLUTION_ID, TABLE_NAME, CREATE_DATE) " +
                             "VALUES (NEXT VALUE FOR TGTS_LOOKUP_SEQ, ?, ?, ?)";
        
        try (PreparedStatement stmt = conn.prepareStatement(insertLookup)) {
            stmt.setString(1, "TEST_SOLUTION");
            stmt.setString(2, "TEST_TABLE");
            stmt.setTimestamp(3, new Timestamp(System.currentTimeMillis()));
            
            int rowsInserted = stmt.executeUpdate();
            assertEquals(1, rowsInserted, "Should insert 1 row into TGTS_LOOKUP");
        }
        
        // Verify the insert worked
        String selectLookup = "SELECT COUNT(*) FROM TGTS_LOOKUP WHERE SOLUTION_ID = ?";
        try (PreparedStatement stmt = conn.prepareStatement(selectLookup)) {
            stmt.setString(1, "TEST_SOLUTION");
            try (ResultSet rs = stmt.executeQuery()) {
                assertTrue(rs.next());
                assertEquals(1, rs.getInt(1), "Should find 1 row in TGTS_LOOKUP");
            }
        }
    }
    
    @Test
    void testDatabaseUrlUniqueness() {
        // Generate multiple URLs and verify they're all unique
        Set<String> urls = new HashSet<>();
        
        for (int i = 0; i < 100; i++) {
            String url = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
            assertTrue(urls.add(url), "URL should be unique: " + url);
            
            // Small delay to ensure nanoTime() changes
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        assertEquals(100, urls.size(), "Should have 100 unique URLs");
    }
    
    @Test
    void testDatabaseProductDetection() throws Exception {
        Class.forName(H2_DB_DRIVER);
        
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            DatabaseMetaData metaData = conn.getMetaData();
            String productName = metaData.getDatabaseProductName();
            
            assertTrue(productName.toLowerCase().contains("h2"), 
                      "Should detect H2 database: " + productName);
            
            System.out.println("Database: " + productName + " " + metaData.getDatabaseProductVersion());
        }
    }
}
