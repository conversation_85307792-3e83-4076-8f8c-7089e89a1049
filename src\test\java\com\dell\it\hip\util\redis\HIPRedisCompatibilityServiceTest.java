package com.dell.it.hip.util.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class HIPRedisCompatibilityServiceTest {

    @Mock
    private HIPRedisHashService hashService;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private RedisTemplate<String, byte[]> byteRedisTemplate;

    @Mock
    private ZSetOperations<String, String> zSetOperations;

    @Mock
    private RedisConnectionFactory connectionFactory;

    @Mock
    private RedisConnection connection;

    private HIPRedisCompatibilityService compatibilityService;

    @BeforeEach
    void setUp() {
        lenient().when(stringRedisTemplate.opsForZSet()).thenReturn(zSetOperations);
        lenient().when(stringRedisTemplate.getConnectionFactory()).thenReturn(connectionFactory);
        lenient().when(connectionFactory.getConnection()).thenReturn(connection);
        compatibilityService = new HIPRedisCompatibilityService(hashService, stringRedisTemplate, byteRedisTemplate);
    }

    @Test
    void testSetAndGet() {
        // Given
        String key = "test-key";
        String value = "test-value";
        when(hashService.get(key)).thenReturn(value);

        // When
        compatibilityService.set(key, value);
        String result = compatibilityService.get(key);

        // Then
        verify(hashService).set(key, value);
        assertEquals(value, result);
    }

    @Test
    void testSetWithDuration() {
        // Given
        String key = "test-key";
        String value = "test-value";
        Duration timeout = Duration.ofMinutes(5);

        // When
        compatibilityService.set(key, value, timeout);

        // Then
        verify(hashService).set(key, value, timeout);
    }

    @Test
    void testSetWithTimeUnit() {
        // Given
        String key = "test-key";
        String value = "test-value";
        long timeout = 30;
        TimeUnit unit = TimeUnit.SECONDS;

        // When
        compatibilityService.set(key, value, timeout, unit);

        // Then
        verify(hashService).set(key, value, timeout, unit);
    }

    @Test
    void testIncrement() {
        // Given
        String key = "counter-key";
        Long expectedValue = 5L;
        when(hashService.increment(key)).thenReturn(expectedValue);

        // When
        Long result = compatibilityService.increment(key);

        // Then
        verify(hashService).increment(key);
        assertEquals(expectedValue, result);
    }

    @Test
    void testIncrementWithDelta() {
        // Given
        String key = "counter-key";
        long delta = 10L;
        Long expectedValue = 15L;
        when(hashService.increment(key, delta)).thenReturn(expectedValue);

        // When
        Long result = compatibilityService.increment(key, delta);

        // Then
        verify(hashService).increment(key, delta);
        assertEquals(expectedValue, result);
    }

    @Test
    void testSetIfAbsent() {
        // Given
        String key = "new-key";
        String value = "new-value";
        when(hashService.setIfAbsent(key, value)).thenReturn(true);

        // When
        Boolean result = compatibilityService.setIfAbsent(key, value);

        // Then
        verify(hashService).setIfAbsent(key, value);
        assertTrue(result);
    }

    @Test
    void testSetIfAbsentWithDuration() {
        // Given
        String key = "new-key";
        String value = "new-value";
        Duration timeout = Duration.ofMinutes(1);
        when(hashService.setIfAbsent(key, value, timeout)).thenReturn(true);

        // When
        Boolean result = compatibilityService.setIfAbsent(key, value, timeout);

        // Then
        verify(hashService).setIfAbsent(key, value, timeout);
        assertTrue(result);
    }

    @Test
    void testSetIfAbsentWithTimeUnit() {
        // Given
        String key = "new-key";
        String value = "new-value";
        long timeout = 60;
        TimeUnit unit = TimeUnit.SECONDS;
        when(hashService.setIfAbsent(key, value, timeout, unit)).thenReturn(true);

        // When
        Boolean result = compatibilityService.setIfAbsent(key, value, timeout, unit);

        // Then
        verify(hashService).setIfAbsent(key, value, timeout, unit);
        assertTrue(result);
    }

    @Test
    void testByteOperations() {
        // Given
        String key = "byte-key";
        byte[] value = "byte-value".getBytes();
        when(hashService.getBytes(key)).thenReturn(value);

        // When
        compatibilityService.setBytes(key, value);
        byte[] result = compatibilityService.getBytes(key);

        // Then
        verify(hashService).setBytes(key, value);
        assertArrayEquals(value, result);
    }

    @Test
    void testHasKey() {
        // Given
        String key = "test-key";
        when(hashService.hasField(key)).thenReturn(true);

        // When
        Boolean result = compatibilityService.hasKey(key);

        // Then
        verify(hashService).hasField(key);
        assertTrue(result);
    }

    @Test
    void testDeleteSingleKey() {
        // Given
        String key = "test-key";
        when(hashService.delete(key)).thenReturn(1L);

        // When
        Boolean result = compatibilityService.delete(key);

        // Then
        verify(hashService).delete(key);
        assertTrue(result);
    }

    @Test
    void testDeleteMultipleKeys() {
        // Given
        String[] keys = {"key1", "key2", "key3"};
        when(hashService.delete(keys)).thenReturn(3L);

        // When
        Long result = compatibilityService.delete(keys);

        // Then
        verify(hashService).delete(keys);
        assertEquals(3L, result);
    }

    @Test
    void testKeysWithPattern() {
        // Given
        String pattern = "test:*";
        Set<Object> allFields = Set.of("test:key1", "test:key2", "other:key3");
        when(hashService.getFields()).thenReturn(allFields);

        // When
        Set<Object> result = compatibilityService.keys(pattern);

        // Then
        verify(hashService).getFields();
        assertEquals(2, result.size());
        assertTrue(result.contains("test:key1"));
        assertTrue(result.contains("test:key2"));
        assertFalse(result.contains("other:key3"));
    }

    @Test
    void testExpireWithDuration() {
        // Given
        String key = "test-key";
        Duration timeout = Duration.ofMinutes(5);
        String currentValue = "current-value";
        when(hashService.get(key)).thenReturn(currentValue);

        // When
        Boolean result = compatibilityService.expire(key, timeout);

        // Then
        verify(hashService).get(key);
        verify(hashService).set(key, currentValue, timeout);
        assertTrue(result);
    }

    @Test
    void testExpireWithTimeUnit() {
        // Given
        String key = "test-key";
        long timeout = 300;
        TimeUnit unit = TimeUnit.SECONDS;
        String currentValue = "current-value";
        when(hashService.get(key)).thenReturn(currentValue);

        // When
        Boolean result = compatibilityService.expire(key, timeout, unit);

        // Then
        verify(hashService).get(key);
        verify(hashService).set(key, currentValue, timeout, unit);
        assertTrue(result);
    }

    @Test
    void testExpireWithNonExistentKey() {
        // Given
        String key = "non-existent-key";
        when(hashService.get(key)).thenReturn(null);

        // When
        Boolean result = compatibilityService.expire(key, Duration.ofMinutes(5));

        // Then
        verify(hashService).get(key);
        verify(hashService, never()).set(anyString(), anyString(), any(Duration.class));
        assertFalse(result);
    }

    @Test
    void testZSetOperations() {
        // Given
        String key = "zset-key";
        String value = "member1";
        double score = 1.0;
        when(zSetOperations.add(key, value, score)).thenReturn(true);

        // When
        Boolean result = compatibilityService.zSetAdd(key, value, score);

        // Then
        verify(zSetOperations).add(key, value, score);
        assertTrue(result);
    }

    @Test
    void testZSetRangeByScore() {
        // Given
        String key = "zset-key";
        double min = 0.0;
        double max = 10.0;
        Set<String> expectedMembers = Set.of("member1", "member2");
        when(zSetOperations.rangeByScore(key, min, max)).thenReturn(expectedMembers);

        // When
        Set<String> result = compatibilityService.zSetRangeByScore(key, min, max);

        // Then
        verify(zSetOperations).rangeByScore(key, min, max);
        assertEquals(expectedMembers, result);
    }

    @Test
    void testZSetRemove() {
        // Given
        String key = "zset-key";
        Object[] values = {"member1", "member2"};
        when(zSetOperations.remove(key, values)).thenReturn(2L);

        // When
        Long result = compatibilityService.zSetRemove(key, values);

        // Then
        verify(zSetOperations).remove(key, values);
        assertEquals(2L, result);
    }

    @Test
    void testOpsForZSet() {
        // When
        ZSetOperations<String, String> result = compatibilityService.opsForZSet();

        // Then
        assertEquals(zSetOperations, result);
    }

    @Test
    void testGetHashService() {
        // When
        HIPRedisHashService result = compatibilityService.getHashService();

        // Then
        assertEquals(hashService, result);
    }

    @Test
    void testGetCacheHashKey() {
        // Given
        String expectedKey = "TEST-CACHE";
        when(hashService.getCacheHashKey()).thenReturn(expectedKey);

        // When
        String result = compatibilityService.getCacheHashKey();

        // Then
        verify(hashService).getCacheHashKey();
        assertEquals(expectedKey, result);
    }

    @Test
    void testCleanupExpiredFields() {
        // When
        compatibilityService.cleanupExpiredFields();

        // Then
        verify(hashService).cleanupExpiredFields();
    }

    @Test
    void testGetStatistics() {
        // Given
        Long totalFields = 100L;
        Set<Object> fields = Set.of("field1", "field2", "field1:expires");
        String cacheKey = "TEST-CACHE";
        
        when(hashService.size()).thenReturn(totalFields);
        when(hashService.getFields()).thenReturn(fields);
        when(hashService.getCacheHashKey()).thenReturn(cacheKey);
        when(hashService.get("field1:expires")).thenReturn(String.valueOf(System.currentTimeMillis() - 1000)); // expired

        // When
        HIPRedisCompatibilityService.CacheStatistics stats = compatibilityService.getStatistics();

        // Then
        assertNotNull(stats);
        assertEquals(totalFields, stats.getTotalFields());
        assertEquals(cacheKey, stats.getCacheHashKey());
        assertTrue(stats.toString().contains(cacheKey));
    }

    @Test
    void testPing_Success() {
        // Given
        when(connection.ping()).thenReturn("PONG");

        // When
        String result = compatibilityService.ping();

        // Then
        assertEquals("PONG", result);
        verify(stringRedisTemplate).getConnectionFactory();
        verify(connectionFactory).getConnection();
        verify(connection).ping();
    }

    @Test
    void testPing_Exception() {
        // Given
        RuntimeException exception = new RuntimeException("Connection failed");
        when(connection.ping()).thenThrow(exception);

        // When & Then
        RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
            compatibilityService.ping();
        });

        assertEquals("Connection failed", thrown.getMessage());
        verify(stringRedisTemplate).getConnectionFactory();
        verify(connectionFactory).getConnection();
        verify(connection).ping();
    }
}
