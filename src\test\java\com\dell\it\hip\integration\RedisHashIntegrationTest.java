package com.dell.it.hip.integration;

import com.dell.it.hip.util.redis.HIPRedisHashService;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assumptions.assumeTrue;

/**
 * Integration tests for Redis hash-based functionality using TestContainers.
 * Tests are automatically skipped when <PERSON><PERSON> is disabled or Docker is not available.
 */
@SpringBootTest
@ActiveProfiles("test")
@EnabledIf("com.dell.it.hip.integration.BaseIntegrationTest#isDockerAvailable")
class RedisHashIntegrationTest extends BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisHashIntegrationTest.class);

    @Autowired(required = false)
    private HIPRedisHashService hashService;

    @Autowired(required = false)
    private HIPRedisCompatibilityService compatibilityService;

    @Autowired(required = false)
    private StringRedisTemplate stringRedisTemplate;

    @Test
    void testHashServiceBasicOperations() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing basic hash service operations");

        // Test basic set and get
        String field = "test:basic:field";
        String value = "test-value-" + System.currentTimeMillis();
        
        hashService.set(field, value);
        String retrievedValue = hashService.get(field);
        
        assertEquals(value, retrievedValue);
        logger.info("✓ Basic set/get operations work correctly");

        // Test field existence
        assertTrue(hashService.hasField(field));
        assertFalse(hashService.hasField("non-existent-field"));
        logger.info("✓ Field existence checks work correctly");

        // Test deletion
        Long deleteResult = hashService.delete(field);
        assertTrue(deleteResult > 0);
        assertNull(hashService.get(field));
        logger.info("✓ Field deletion works correctly");
    }

    @Test
    void testHashServiceWithExpiration() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing hash service expiration functionality");

        String field = "test:expiration:field";
        String value = "expiring-value";
        Duration shortTimeout = Duration.ofSeconds(2);

        // Set with expiration
        hashService.set(field, value, shortTimeout);
        
        // Should be available immediately
        assertEquals(value, hashService.get(field));
        logger.info("✓ Field with expiration is immediately available");

        // Wait for expiration and verify cleanup
        try {
            Thread.sleep(3000); // Wait 3 seconds
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Should be expired and cleaned up
        String expiredValue = hashService.get(field);
        assertNull(expiredValue);
        logger.info("✓ Expired field is automatically cleaned up");
    }

    @Test
    void testHashServiceIncrementOperations() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing hash service increment operations");

        String counterField = "test:counter:field";
        
        // Test basic increment
        Long count1 = hashService.increment(counterField);
        assertEquals(1L, count1);
        
        Long count2 = hashService.increment(counterField);
        assertEquals(2L, count2);
        logger.info("✓ Basic increment operations work correctly");

        // Test increment by delta
        Long count3 = hashService.increment(counterField, 5L);
        assertEquals(7L, count3);
        logger.info("✓ Increment by delta works correctly");

        // Cleanup
        hashService.delete(counterField);
    }

    @Test
    void testHashServiceSetIfAbsent() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing hash service setIfAbsent operations");

        String field = "test:setifabsent:field";
        String value1 = "first-value";
        String value2 = "second-value";

        // First set should succeed
        Boolean result1 = hashService.setIfAbsent(field, value1);
        assertTrue(result1);
        assertEquals(value1, hashService.get(field));
        logger.info("✓ SetIfAbsent succeeds for new field");

        // Second set should fail
        Boolean result2 = hashService.setIfAbsent(field, value2);
        assertFalse(result2);
        assertEquals(value1, hashService.get(field)); // Should still be first value
        logger.info("✓ SetIfAbsent fails for existing field");

        // Cleanup
        hashService.delete(field);
    }

    @Test
    void testCompatibilityServiceBackwardCompatibility() {
        // Skip test if Redis services are not available
        assumeTrue(compatibilityService != null, "HIPRedisCompatibilityService not available - skipping test");

        logger.info("Testing compatibility service backward compatibility");

        String key = "test:compatibility:key";
        String value = "compatibility-value";

        // Test traditional Redis operations through compatibility layer
        compatibilityService.set(key, value);
        String retrievedValue = compatibilityService.get(key);
        
        assertEquals(value, retrievedValue);
        logger.info("✓ Traditional set/get operations work through compatibility layer");

        // Test key existence
        assertTrue(compatibilityService.hasKey(key));
        logger.info("✓ Key existence check works through compatibility layer");

        // Test increment operations
        String counterKey = "test:compatibility:counter";
        Long count1 = compatibilityService.increment(counterKey);
        assertEquals(1L, count1);
        
        Long count2 = compatibilityService.increment(counterKey, 5L);
        assertEquals(6L, count2);
        logger.info("✓ Increment operations work through compatibility layer");

        // Test deletion
        Boolean deleteResult = compatibilityService.delete(key);
        assertTrue(deleteResult);
        assertNull(compatibilityService.get(key));
        logger.info("✓ Deletion works through compatibility layer");

        // Cleanup
        compatibilityService.delete(counterKey);
    }

    @Test
    void testHashServiceByteOperations() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing hash service byte array operations");

        String field = "test:bytes:field";
        byte[] originalBytes = ("test-byte-data-" + System.currentTimeMillis()).getBytes();
        
        hashService.setBytes(field, originalBytes);
        byte[] retrievedBytes = hashService.getBytes(field);
        
        assertArrayEquals(originalBytes, retrievedBytes);
        logger.info("✓ Byte array operations work correctly");

        // Cleanup
        hashService.delete(field);
    }

    @Test
    void testHashServiceBulkOperations() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing hash service bulk operations");

        // Set multiple fields
        String[] fields = {"bulk:field1", "bulk:field2", "bulk:field3"};
        String[] values = {"value1", "value2", "value3"};
        
        for (int i = 0; i < fields.length; i++) {
            hashService.set(fields[i], values[i]);
        }

        // Test getting all fields
        Set<Object> allFields = hashService.getFields();
        assertTrue(allFields.size() >= fields.length);
        logger.info("✓ Bulk field retrieval works correctly");

        // Test getting all entries
        Map<Object, Object> allEntries = hashService.getAll();
        assertTrue(allEntries.size() >= fields.length);
        for (int i = 0; i < fields.length; i++) {
            assertEquals(values[i], allEntries.get(fields[i]));
        }
        logger.info("✓ Bulk entry retrieval works correctly");

        // Test bulk deletion
        Long deleteCount = hashService.delete(fields);
        assertTrue(deleteCount >= fields.length);
        logger.info("✓ Bulk deletion works correctly");
    }

    @Test
    void testCacheStatistics() {
        // Skip test if Redis services are not available
        assumeTrue(compatibilityService != null, "HIPRedisCompatibilityService not available - skipping test");

        logger.info("Testing cache statistics functionality");

        // Add some test data
        String[] testFields = {"stats:field1", "stats:field2", "stats:field3"};
        for (String field : testFields) {
            compatibilityService.set(field, "test-value");
        }

        // Get statistics
        HIPRedisCompatibilityService.CacheStatistics stats = compatibilityService.getStatistics();
        
        assertNotNull(stats);
        assertTrue(stats.getTotalFields() >= testFields.length);
        assertNotNull(stats.getCacheHashKey());
        assertTrue(stats.getActiveFields() >= 0);
        logger.info("✓ Cache statistics: {}", stats);

        // Cleanup
        compatibilityService.delete(testFields);
    }

    @Test
    void testHashServiceCleanupExpiredFields() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null, "HIPRedisHashService not available - skipping test");

        logger.info("Testing expired field cleanup functionality");

        // Create fields with short expiration
        String[] expiredFields = {"cleanup:field1", "cleanup:field2"};
        Duration shortTimeout = Duration.ofSeconds(1);
        
        for (String field : expiredFields) {
            hashService.set(field, "expiring-value", shortTimeout);
        }

        // Create a field without expiration
        String permanentField = "cleanup:permanent";
        hashService.set(permanentField, "permanent-value");

        // Wait for expiration
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Run cleanup
        hashService.cleanupExpiredFields();

        // Verify expired fields are gone but permanent field remains
        for (String field : expiredFields) {
            assertNull(hashService.get(field));
        }
        assertEquals("permanent-value", hashService.get(permanentField));
        logger.info("✓ Expired field cleanup works correctly");

        // Cleanup
        hashService.delete(permanentField);
    }

    @Test
    void testCacheNamespaceIsolation() {
        // Skip test if Redis services are not available
        assumeTrue(hashService != null && stringRedisTemplate != null, 
                   "Redis services not available - skipping test");

        logger.info("Testing cache namespace isolation");

        String cacheHashKey = hashService.getCacheHashKey();
        assertNotNull(cacheHashKey);
        logger.info("Using cache hash key: {}", cacheHashKey);

        // Set a value through hash service
        String field = "namespace:test:field";
        String value = "namespace-test-value";
        hashService.set(field, value);

        // Verify it's stored in the correct hash
        String directValue = (String) stringRedisTemplate.opsForHash().get(cacheHashKey, field);
        assertEquals(value, directValue);
        logger.info("✓ Data is correctly stored in the named hash cache");

        // Verify it's not stored as a direct key
        String directKeyValue = stringRedisTemplate.opsForValue().get(field);
        assertNull(directKeyValue);
        logger.info("✓ Data is not stored as direct Redis keys");

        // Cleanup
        hashService.delete(field);
    }
}
