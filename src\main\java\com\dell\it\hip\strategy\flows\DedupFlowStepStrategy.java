package com.dell.it.hip.strategy.flows;
import java.time.Duration;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.DedupFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.DocTypeConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.core.registry.DedupMetricRegistry;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.DedupKeyUtil;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.dell.it.hip.util.validation.MessageFormatDetector;

@Component("dedup")
public class DedupFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired
    private HIPRedisCompatibilityService redisService;
    @Autowired
    private WiretapService wiretapService;
    @Autowired(required = false)
    private DedupMetricRegistry metricsRegistry; // Optional, for Prometheus etc.

    @Override
    public String getType() {
        return "dedup";
    }

    @Override
    public List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        DedupFlowStepConfig config = (DedupFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());
        
        if (config == null) {
            emitWiretapError(message, def, stepConfigRef, "No Dedup config for: " + stepConfigRef.getPropertyRef());
            return Collections.emptyList();
        }
        
        String docTypeName = null;
        String docTypeVersion = null;

        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null)
            dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));

     // === Find matching docType config or default ===
        DedupFlowStepConfig.DocTypeDedupConfig dtConfig = findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        DedupFlowStepConfig.DefaultDedupConfig defaultCfg = config.getDefaultConfig();

        DedupFlowStepConfig.DedupBehavior behavior = dtConfig != null ? dtConfig.getBehavior()
                           : (defaultCfg != null ? defaultCfg.getBehavior() : DedupFlowStepConfig.DedupBehavior.DEDUP);
        
     // === Behavior handling ===
        if (behavior == DedupFlowStepConfig.DedupBehavior.SKIP) {
            wiretapService.tap(message, def, stepConfigRef, "info", "Strict Order Processor: SKIP for docType " + docTypeHeader);
            return Collections.singletonList(message);
        }
        if (behavior == DedupFlowStepConfig.DedupBehavior.TERMINATE) {
            wiretapService.tap(message, def, stepConfigRef, "terminated", "Strict Order Processor: TERMINATE for docType " + docTypeHeader);
            return Collections.emptyList();
        }

        String dedupKey = DedupKeyUtil.buildDedupKey(def, dtConfig, message);

        Boolean isDuplicate = redisService.hasKey(dedupKey);
        String eventLevel = dtConfig.getWiretapEventLevel() != null ? dtConfig.getWiretapEventLevel() : "warn";

        if (Boolean.TRUE.equals(isDuplicate)) {
            // Wiretap duplicate event
            wiretapService.tap(message, def, stepConfigRef, eventLevel, "Deduplication: Duplicate detected, key=" + dedupKey);

            // Metrics (optional)
            if (dtConfig.isEnableMetrics() && metricsRegistry != null)
                metricsRegistry.incrementDuplicate(def, stepConfigRef);

            // Policy-based handling
            switch (dtConfig.getOnDuplicate().toUpperCase()) {
                case "DROP":
                    return Collections.emptyList();
                case "FLAG":
                    Message<?> flagged = MessageBuilder.fromMessage(message)
                            .setHeader("dedupFlag", true).build();
                    return List.of(flagged);
                case "CONTINUE":
                    return List.of(message);
                case "DEADLETTER":
                    // If you have a dead letter mechanism, send to wiretap as error and drop
                    wiretapService.tap(message, def, stepConfigRef, "error", "Dedup DEADLETTER: Duplicate message sent to DLQ, key=" + dedupKey);
                    return Collections.emptyList();
                default:
                    return Collections.emptyList();
            }
        } else {
            // Insert new key with expiry using hash-based storage
            redisService.set(dedupKey, "1", Duration.ofSeconds(dtConfig.getDedupExpirySeconds()));
            if (dtConfig.isLogOnInsert())
                wiretapService.tap(message, def, stepConfigRef, "info", "Dedup key created: " + dedupKey);

            if (dtConfig.isEnableMetrics() && metricsRegistry != null)
                metricsRegistry.incrementInserted(def, stepConfigRef);

            return List.of(message);
        }
    }
    
 // === docType config matching ===
    private DedupFlowStepConfig.DocTypeDedupConfig findMatchingDocTypeConfig(DedupFlowStepConfig config, String docTypeName, String docTypeVersion, String dataFormat ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (DedupFlowStepConfig.DocTypeDedupConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }

    // Enterprise admin API to clear a dedup key (use in orchestrationService)
    public void purgeDedupKey(HIPIntegrationDefinition def, DedupFlowStepConfig.DocTypeDedupConfig dtConfig, Message<?> message, DocTypeConfig DocTypeConfig) {
        String dedupKey = DedupKeyUtil.buildDedupKey(def, dtConfig, message);
        redisService.delete(dedupKey);
    }
    
    private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError", errorMsg);
    }
}