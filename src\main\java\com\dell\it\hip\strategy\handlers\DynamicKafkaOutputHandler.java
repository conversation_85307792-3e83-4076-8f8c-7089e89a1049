package com.dell.it.hip.strategy.handlers;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicKafkaHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.HeaderFilterUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
@Component("kafkaHandler")
public class DynamicKafkaOutputHandler extends AbstractOutputHandlerStrategy {
	
	private static final Logger logger = LoggerFactory.getLogger(DynamicKafkaOutputHandler.class);

    private final Map<String, KafkaProducer<byte[], byte[]>> producerMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();
    private final Set<String> headersToFilter;
    
    @Autowired
    private Tracer kafkaSendTracer;

    public DynamicKafkaOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "kafkahandler";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        // If paused, just throw to trigger orchestration fallback (do NOT wiretap here)
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicKafkaHandlerConfig config = def.getConfig(ref.getPropertyRef(), DynamicKafkaHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicKafkaHandlerConfig not found for ref: " + ref.getPropertyRef());

        String topic = config.getTopic();
        if (topic == null)
            throw new IllegalArgumentException("Kafka topic not configured");

        boolean useGzip = Boolean.TRUE.equals(config.getGzipEnabled());

        byte[] payload = useGzip ? CompressionUtil.compress(message.getPayload()) : convertToBytes(message.getPayload());
        Map<String, Object> filteredHeaders = HeaderFilterUtil.filterHeaders(message.getHeaders(), headersToFilter);
        
        //inside the same thread where the parent span was started
        //Context parentContext = Context.current();
        Context parentContext = OpenTelemetryPropagationUtil.extractContextFromMessage(message);
        
     // 2. Start a new PRODUCER span for Kafka send
        Span span = kafkaSendTracer.spanBuilder("kafka.send")
                .setSpanKind(SpanKind.PRODUCER)
                .setParent(parentContext)
                .startSpan();
        try (Scope scope = span.makeCurrent()) {
        	
        	 KafkaProducer<byte[], byte[]> producer = getOrCreateProducer(config, ref);

             ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(topic, payload);
             
             record.headers().remove("traceparent");
          // 4. Inject trace context into Kafka headers
             GlobalOpenTelemetry.getPropagators().getTextMapPropagator()
                 .inject(Context.current(), record.headers(), OpenTelemetryPropagationUtil.KafkaHeaderSetter.INSTANCE);

             
             filteredHeaders.forEach((k, v) -> {
                 if (v instanceof String) {
                     record.headers().add(k, ((String) v).getBytes(StandardCharsets.UTF_8));
                 } else if (v instanceof byte[]) {
                     record.headers().add(k, (byte[]) v);
                 }
             });
             
             for (Header header : record.headers()) {
                 logger.info("Kafka Header: {} = {}", header.key(), new String(header.value(), StandardCharsets.UTF_8));
             }

             producer.send(record, (metadata, exception) -> {
                 if (exception != null) {
                     // This is just an async log. Error/fallback is handled in orchestration, not here.
                	 span.recordException(exception);
                     span.setStatus(StatusCode.ERROR, "Kafka send failed");
                     wiretapService.tap(message, def, ref, "error", "Kafka send failed: " + exception.getMessage());
                 } else {
                	 span.setStatus(StatusCode.OK);
                     wiretapService.tap(message, def, ref, "completed", "Message sent to Kafka: " + metadata.topic() + "-" + metadata.partition() + "@" + metadata.offset());
                 }
             });
        	
        }finally {
            span.end(); // Always end span!
        }


       
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    private KafkaProducer<byte[], byte[]> getOrCreateProducer(DynamicKafkaHandlerConfig config, HandlerConfigRef ref) {
        String key = ref.getPropertyRef();
        return producerMap.computeIfAbsent(key, k -> createProducer(config));
    }

    private KafkaProducer<byte[], byte[]> createProducer(DynamicKafkaHandlerConfig config) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, config.getAcks() != null ? config.getAcks().toString() : "all");
        if (config.getBatchSize() != null) props.put(ProducerConfig.BATCH_SIZE_CONFIG, config.getBatchSize());
        if (config.getLingerMs() != null) props.put(ProducerConfig.LINGER_MS_CONFIG, config.getLingerMs());
        if (config.getBufferMemory() != null) props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, config.getBufferMemory());
        if (config.getRetries() != null) props.put(ProducerConfig.RETRIES_CONFIG, config.getRetries());
        if (config.getMaxInFlightRequestsPerConnection() != null)
            props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, config.getMaxInFlightRequestsPerConnection());
        if (config.getDeliveryTimeoutMs() != null) props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, config.getDeliveryTimeoutMs());
        if (config.getRequestTimeoutMs() != null) props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, config.getRequestTimeoutMs());
        if (config.getEnableIdempotence() != null) props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, config.getEnableIdempotence());
        if (config.getCompressionType() != null) props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, config.getCompressionType());

        // Security configs
        if (config.getSecurityProtocol() != null) props.put("security.protocol", config.getSecurityProtocol());
        if (config.getSaslMechanism() != null) props.put("sasl.mechanism", config.getSaslMechanism());
        String saslJaasConfig = config.getSasljaasconfig().concat(" username=" + "\"" + config.getUsername() + "\"" + " password=" + "\"" + config.getPassword() + "\"" + ";");
        if (config.getSasljaasconfig() != null) props.put("sasl.jaas.config", saslJaasConfig);
        if (config.getSslTruststoreLocation() != null) props.put("ssl.truststore.location", config.getSslTruststoreLocation());
        if (config.getSslTruststorePassword() != null) props.put("ssl.truststore.password", config.getSslTruststorePassword());
        if (config.getSslKeystoreLocation() != null) props.put("ssl.keystore.location", config.getSslKeystoreLocation());
        if (config.getSslKeystorePassword() != null) props.put("ssl.keystore.password", config.getSslKeystorePassword());
        if (config.getSslKeyPassword() != null) props.put("ssl.key.password", config.getSslKeyPassword());
       ;

        if (config.getParameters() != null) {
            config.getParameters().forEach(props::putIfAbsent);
        }
        return new KafkaProducer<>(props);
    }

    // --- LIFECYCLE methods ---
    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getPropertyRef(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getPropertyRef(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getPropertyRef(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        KafkaProducer<byte[], byte[]> producer = producerMap.remove(ref.getPropertyRef());
        if (producer != null) {
            producer.close();
        }
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        producerMap.values().forEach(KafkaProducer::close);
        producerMap.clear();
        handlerPauseState.clear();
        super.dispose();
    }
}