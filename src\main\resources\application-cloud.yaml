# Spring Boot Configuration for Cloud Profile
# This configuration is used when SPRING_PROFILES_ACTIVE=cloud

spring:

  
  main:
    allow-circular-references: true
  
  # Database Configuration
  datasource:
    url: ${SHARED_SHARED}
  
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
  
  # Redis Configuration for Cloud Environment
  data:
    redis:
      host: ${REDIS_HOST:hip-gscm-redis-dev.rds-a2-np.kob.dell.com}
      port: ${REDIS_PORT:443}
      password: ${REDIS_PASSWORD:sFjIvYHMJHcv}
      timeout: 5000ms
      ssl:
        enabled: true
      enabled: true
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
        shutdown-timeout: 100ms
  
  # Configuration Server Settings
  cloud:
    config:
      name: ${configproperties_sheet_name}
      uri: ${configserver_uri:https://configserveruser:<EMAIL>}
      enabled: true
      fail-fast: false
      retry:
        initial-interval: 1000
        max-attempts: 6
        max-interval: 2000
        multiplier: 1.1
  
  config:
    import: optional:configserver:${configserver_uri}

# Redisson Configuration for Cloud
redisson:
  config: |
    singleServerConfig:
      address: "rediss://${REDIS_HOST:hip-gscm-redis-dev.rds-a2-np.kob.dell.com}:${REDIS_PORT:443}"
      password: "${REDIS_PASSWORD:sFjIvYHMJHcv}"
      connectionPoolSize: 20
      connectionMinimumIdleSize: 5
      timeout: 5000
      retryAttempts: 3
      retryInterval: 1500
      sslEnableEndpointIdentification: false
      sslTruststore: null
      sslTruststorePassword: null
    threads: 16
    nettyThreads: 32
    codec: !<org.redisson.codec.JsonJacksonCodec> {}

# Redis Cache Configuration
redis:
  cache:
    name: ${REDIS_CACHE_NAME:HIP-GSCM-CLOUD-REDISCACHE}

# Service Configuration
service:
  manager:
    name: ${SERVICE_MANAGER_NAME:CloudIntegrationManager}
  concurrency:
    corePoolSize: 12
    maxPoolSize: 48
    queueCapacity: 1000

# HIP Configuration Properties
hip:
  config:
    properties:
      sheet:
        name: ${configproperties_sheet_name:shared-service-manger}
  
  # Integration Platform Settings
  integration:
    platform:
      name: "HIP Services Cloud"
      environment: "cloud"
      cluster:
        enabled: true
        discovery:
          enabled: true
  
  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka1:9092,kafka2:9092}
    security-protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}
    sasl-mechanism: ${KAFKA_SASL_MECHANISM:SCRAM-SHA-256}

# Logging Configuration for Cloud Environment
logging:
  level:
    root: INFO
    com.dell.it.hip: INFO
    org.springframework.cloud.config: DEBUG
    org.springframework.data.redis: INFO
    org.redisson: INFO
    redis.clients.jedis: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"

# Management and Actuator Settings
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,configprops,env
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    redis:
      enabled: true
    db:
      enabled: true
  

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
#  servlet:
#    context-path: /hip-services
