package com.dell.it.hip.strategy.flows;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig.DocTypeStrictOrderConfig;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig.DefaultStrictOrderConfig;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig.StrictOrderBehavior;
import com.dell.it.hip.core.registry.StrictOrderMetricRegistry;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;

@ExtendWith(MockitoExtension.class)
class StrictOrderProcessorFlowStepStrategyTest {

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private HIPRedisCompatibilityService redisService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private WiretapService wiretapService;

    @Mock
    private StrictOrderMetricRegistry metricRegistry;
    
    @Mock
    private ValueOperations<String, String> valueOperations;
    
    @Mock
    private ZSetOperations<String, String> zSetOperations;
    
    @Mock
    private RLock lock;
    
    @Mock
    private HIPIntegrationDefinition integrationDefinition;
    
    @Mock
    private FlowStepConfigRef stepConfigRef;
    
    @InjectMocks
    private StrictOrderProcessorFlowStepStrategy strategy;

    private StrictOrderConfig strictOrderConfig;
    private DocTypeStrictOrderConfig docTypeConfig;
    private DefaultStrictOrderConfig defaultConfig;
    private Message<?> testMessage;

    @BeforeEach
    void setUp() {
        // Setup Redis template operations
        lenient().when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        lenient().when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);

        // Setup Redisson lock
        lenient().when(redissonClient.getLock(anyString())).thenReturn(lock);

        // Setup integration definition
        lenient().when(integrationDefinition.getServiceManagerName()).thenReturn("test-service");
        lenient().when(integrationDefinition.getHipIntegrationName()).thenReturn("test-integration");
        lenient().when(integrationDefinition.getVersion()).thenReturn("1.0");

        // Setup step config ref
        lenient().when(stepConfigRef.getPropertyRef()).thenReturn("strictOrderStep");
        
        // Setup strict order config
        strictOrderConfig = new StrictOrderConfig();
        docTypeConfig = new DocTypeStrictOrderConfig();
        docTypeConfig.setName("testDoc");
        docTypeConfig.setVersion("1.0");
        docTypeConfig.setDataFormat("JSON");
        docTypeConfig.setBehavior(StrictOrderBehavior.STRICTORDER);
        docTypeConfig.setOrderingKeys(Arrays.asList("partnerId", "documentId"));
        docTypeConfig.setSequenceHeader("sequenceNumber");
        docTypeConfig.setDocumentTypeId(1);
        
        defaultConfig = new DefaultStrictOrderConfig();
        defaultConfig.setBehavior(StrictOrderBehavior.STRICTORDER);
        
        strictOrderConfig.setDocTypeConfigs(Arrays.asList(docTypeConfig));
        strictOrderConfig.setDefaultConfig(defaultConfig);
        
        lenient().when(integrationDefinition.getConfig(eq("strictOrderStep"), eq(StrictOrderConfig.class)))
            .thenReturn(strictOrderConfig);
        
        // Setup test message
        Map<String, Object> headers = new HashMap<>();
        headers.put("HIP.documentType", "testDoc:1.0");
        headers.put("hip.source.dataFormat", "JSON");
        headers.put("partnerId", "PARTNER001");
        headers.put("documentId", "DOC001");
        headers.put("sequenceNumber", 1L);
        
        testMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
    }

    @Test
    @DisplayName("Should return correct strategy type")
    void testGetType() {
        assertEquals("strictOrderProcessor", strategy.getType());
    }

    @Test
    @DisplayName("Should return empty list when no config found")
    void testDoExecute_NoConfig_ReturnsEmptyList() throws Exception {
        // Arrange
        when(integrationDefinition.getConfig(anyString(), eq(StrictOrderConfig.class))).thenReturn(null);
        
        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertTrue(result.isEmpty());
        verify(wiretapService).tap(eq(testMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("error"), contains("No Strict Order Processor config"));
    }

    @Test
    @DisplayName("Should skip message when behavior is SKIP")
    void testDoExecute_SkipBehavior_ReturnsOriginalMessage() throws Exception {
        // Arrange
        docTypeConfig.setBehavior(StrictOrderBehavior.SKIP);
        
        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals(testMessage, result.get(0));
        verify(wiretapService).tap(eq(testMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("info"), contains("SKIP for docType"));
    }

    @Test
    @DisplayName("Should terminate message when behavior is TERMINATE")
    void testDoExecute_TerminateBehavior_ReturnsEmptyList() throws Exception {
        // Arrange
        docTypeConfig.setBehavior(StrictOrderBehavior.TERMINATE);
        
        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertTrue(result.isEmpty());
        verify(wiretapService).tap(eq(testMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("terminated"), contains("TERMINATE for docType"));
    }

    @Test
    @DisplayName("Should return empty list when no sequence number found")
    void testDoExecute_NoSequenceNumber_ReturnsEmptyList() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.remove("sequenceNumber");
        Message<?> messageWithoutSeq = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
        
        // Act
        List<Message<?>> result = strategy.doExecute(messageWithoutSeq, stepConfigRef, integrationDefinition);
        
        // Assert
        assertTrue(result.isEmpty());
        verify(metricRegistry).incrementMissing(integrationDefinition, "strictOrderStep");
        verify(wiretapService).tap(eq(messageWithoutSeq), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("error"), contains("No sequence number"));
    }

    @Test
    @DisplayName("Should process message in order when sequence matches expected")
    void testDoExecute_InOrderMessage_ProcessesSuccessfully() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn("1"); // Expected sequence is 1
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());
        
        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals(testMessage, result.get(0));
        verify(redisService).set(anyString(), eq("2")); // Next expected sequence
        verify(metricRegistry).incrementInOrder(integrationDefinition, "strictOrderStep");
        verify(wiretapService).tap(eq(testMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("info"), contains("Released 1 messages"));
    }

    @Test
    @DisplayName("Should buffer out-of-order message")
    void testDoExecute_OutOfOrderMessage_BuffersMessage() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn("1"); // Expected sequence is 1
        
        // Create message with sequence 3 (out of order)
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.put("sequenceNumber", 3L);
        Message<?> outOfOrderMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
        
        // Act
        List<Message<?>> result = strategy.doExecute(outOfOrderMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertTrue(result.isEmpty());
        verify(zSetOperations).add(anyString(), anyString(), eq(3.0));
        verify(metricRegistry).incrementOutOfOrder(integrationDefinition, "strictOrderStep");
        verify(wiretapService).tap(eq(outOfOrderMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("warn"), contains("Buffered out-of-order msg"));
    }

    @Test
    @DisplayName("Should handle duplicate/late message")
    void testDoExecute_DuplicateMessage_ReturnsEmptyList() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn("2"); // Expected sequence is 2
        
        // Message with sequence 1 (already processed)
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.put("sequenceNumber", 1L);
        Message<?> duplicateMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
        
        // Act
        List<Message<?>> result = strategy.doExecute(duplicateMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertTrue(result.isEmpty());
        verify(metricRegistry).incrementMissing(integrationDefinition, "strictOrderStep");
        verify(wiretapService).tap(eq(duplicateMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("warn"), contains("Late or duplicate message"));
    }

    @Test
    @DisplayName("Should process message when no expected sequence exists (first message)")
    void testDoExecute_FirstMessage_ProcessesSuccessfully() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn(null); // No expected sequence yet
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());
        
        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals(testMessage, result.get(0));
        verify(redisService).set(anyString(), eq("2")); // Next expected sequence
        verify(metricRegistry).incrementInOrder(integrationDefinition, "strictOrderStep");
    }

    @Test
    @DisplayName("Should use default config when no docType config matches")
    void testDoExecute_NoMatchingDocTypeConfig_UsesDefaultConfig() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>();
        headers.put("HIP.documentType", "unknownDoc:1.0");
        headers.put("hip.source.dataFormat", "XML");
        headers.put("partnerId", "PARTNER001");
        headers.put("documentId", "DOC001");
        headers.put("sequenceNumber", 1L);
        
        Message<?> unknownDocMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
        
        // Set default config to SKIP
        defaultConfig.setBehavior(StrictOrderBehavior.SKIP);
        
        // Act
        List<Message<?>> result = strategy.doExecute(unknownDocMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals(unknownDocMessage, result.get(0));
        verify(wiretapService).tap(eq(unknownDocMessage), eq(integrationDefinition), eq(stepConfigRef), 
                                  eq("info"), contains("SKIP for docType"));
    }

    @Test
    @DisplayName("Should handle string sequence number")
    void testDoExecute_StringSequenceNumber_ProcessesSuccessfully() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.put("sequenceNumber", "1"); // String instead of Long
        Message<?> stringSeqMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();
        
        when(redisService.get(anyString())).thenReturn("1");
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());
        
        // Act
        List<Message<?>> result = strategy.doExecute(stringSeqMessage, stepConfigRef, integrationDefinition);
        
        // Assert
        assertEquals(1, result.size());
        verify(metricRegistry).incrementInOrder(integrationDefinition, "strictOrderStep");
    }

    @Test
    @DisplayName("Should detect data format when not provided in headers")
    void testDoExecute_NoDataFormatHeader_DetectsFormat() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.remove("hip.source.dataFormat");
        Message<?> noFormatMessage = MessageBuilder.withPayload("{\"test\": \"json\"}").copyHeaders(headers).build();

        when(redisService.get(anyString())).thenReturn("1");
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());

        // Act
        List<Message<?>> result = strategy.doExecute(noFormatMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertEquals(1, result.size());
        verify(metricRegistry).incrementInOrder(integrationDefinition, "strictOrderStep");
    }

    @Test
    @DisplayName("Should process buffered messages in sequence")
    void testDoExecute_ProcessesBufferedMessages() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn("1"); // Expected sequence is 1

        // Mock no buffered messages for simplicity
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0)))
            .thenReturn(Collections.emptySet());

        // Act
        List<Message<?>> result = strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertEquals(1, result.size()); // Current message processed
        verify(redisService).set(anyString(), eq("2")); // Next expected sequence
        verify(metricRegistry).incrementInOrder(integrationDefinition, "strictOrderStep");
    }

    @Test
    @DisplayName("Should handle manual release with no upper limit")
    void testManualRelease_NoUpperLimit_ReleasesAllBuffered() throws Exception {
        // Arrange
        String serializedMsg1 = "msg1";
        String serializedMsg2 = "msg2";
        when(zSetOperations.range(anyString(), eq(0L), eq(-1L)))
            .thenReturn(Set.of(serializedMsg1, serializedMsg2));

        // First execute a normal message to set the stepConfigRef field
        when(redisService.get(anyString())).thenReturn("1");
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());
        strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Act
        List<Message<?>> result = strategy.manualRelease(integrationDefinition, defaultConfig,
                                                        Arrays.asList("key1", "key2"), null);

        // Assert
        assertEquals(2, result.size()); // Two messages should be returned
        verify(lock, atLeastOnce()).lock(10, TimeUnit.SECONDS);
        verify(lock, atLeastOnce()).unlock();
    }

    @Test
    @DisplayName("Should handle manual release with upper sequence limit")
    void testManualRelease_WithUpperLimit_ReleasesUpToSequence() throws Exception {
        // Arrange
        String serializedMsg = "msg1";
        when(zSetOperations.rangeByScore(anyString(), eq(0.0), eq(5.0)))
            .thenReturn(Set.of(serializedMsg));

        // First execute a normal message to set the stepConfigRef field
        when(redisService.get(anyString())).thenReturn("1");
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());
        strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Act
        List<Message<?>> result = strategy.manualRelease(integrationDefinition, defaultConfig,
                                                        Arrays.asList("key1", "key2"), 5L);

        // Assert
        assertEquals(1, result.size()); // One message should be returned
        verify(lock, atLeastOnce()).lock(10, TimeUnit.SECONDS);
        verify(lock, atLeastOnce()).unlock();
    }

    @Test
    @DisplayName("Should handle invalid sequence number gracefully")
    void testDoExecute_InvalidSequenceNumber_ReturnsEmptyList() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.put("sequenceNumber", "invalid-number");
        Message<?> invalidSeqMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();

        // Act
        List<Message<?>> result = strategy.doExecute(invalidSeqMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertTrue(result.isEmpty());
        verify(metricRegistry).incrementMissing(integrationDefinition, "strictOrderStep");
    }

    @Test
    @DisplayName("Should handle missing document type header")
    void testDoExecute_MissingDocumentTypeHeader_UsesDefaultConfig() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.remove("HIP.documentType");
        Message<?> noDocTypeMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();

        defaultConfig.setBehavior(StrictOrderBehavior.SKIP);

        // Act
        List<Message<?>> result = strategy.doExecute(noDocTypeMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertEquals(1, result.size());
        verify(wiretapService).tap(eq(noDocTypeMessage), eq(integrationDefinition), eq(stepConfigRef),
                                  eq("info"), contains("SKIP for docType"));
    }

    @Test
    @DisplayName("Should handle malformed document type header")
    void testDoExecute_MalformedDocumentTypeHeader_UsesDefaultConfig() throws Exception {
        // Arrange
        Map<String, Object> headers = new HashMap<>(testMessage.getHeaders());
        headers.put("HIP.documentType", "malformed-header-without-colon");
        Message<?> malformedDocTypeMessage = MessageBuilder.withPayload("test payload").copyHeaders(headers).build();

        defaultConfig.setBehavior(StrictOrderBehavior.SKIP);

        // Act
        List<Message<?>> result = strategy.doExecute(malformedDocTypeMessage, stepConfigRef, integrationDefinition);

        // Assert
        assertEquals(1, result.size());
        verify(wiretapService).tap(eq(malformedDocTypeMessage), eq(integrationDefinition), eq(stepConfigRef),
                                  eq("info"), contains("SKIP for docType"));
    }

    @Test
    @DisplayName("Should handle lock acquisition and release properly")
    void testDoExecute_LockHandling_AcquiresAndReleasesLock() throws Exception {
        // Arrange
        when(redisService.get(anyString())).thenReturn("1");
        when(zSetOperations.rangeByScore(anyString(), eq(2.0), eq(2.0))).thenReturn(Collections.emptySet());

        // Act
        strategy.doExecute(testMessage, stepConfigRef, integrationDefinition);

        // Assert
        verify(lock).lock(10, TimeUnit.SECONDS);
        verify(lock).unlock();
    }
}
