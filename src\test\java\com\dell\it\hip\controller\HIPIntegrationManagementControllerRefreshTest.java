package com.dell.it.hip.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.Tag;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.GlobalExceptionHandler;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Unit tests for HIPIntegrationManagementController refresh endpoint.
 * Tests the complete flow from API input to service layer interaction.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerRefreshTest {

    private MockMvc mockMvc;

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private HIPIntegrationRuntimeService runtimeService;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Mock
    private ServiceManager serviceManager;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        // Set up the app_config_name field using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", "test-service-manager");
        // Set up MockMvc with global exception handler
        mockMvc = MockMvcBuilders.standaloneSetup(controller)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    @Test
    void testRefreshEndpoint_SuccessfulRefresh() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doNothing().when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("HIPIntegration refreshed"));

        // Assert
        verify(hipIntegrationRegistry).exists(eq("test-service-manager"), eq(integrationName), eq(version));
        
        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).refreshHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        assertEquals(integrationName, capturedRequest.getHipIntegrationName());
        assertEquals(version, capturedRequest.getVersion());
        assertEquals("test-service-manager", capturedRequest.getServiceManagerName());
    }

    @Test
    void testRefreshEndpoint_IntegrationNotFound_Returns404() throws Exception {
        // Arrange
        String integrationName = "nonexistent-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(false);

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_NOT_FOUND"))
                .andExpect(jsonPath("$.message").value("Integration not found: nonexistent-integration:1.0"));

        // Verify that orchestration service was never called
        verify(orchestrationService, never()).refreshHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRefreshEndpoint_ValidationError_Returns400() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest incompleteRequest = new HIPIntegrationRequest();
        incompleteRequest.setVersion(version); // Missing required fields

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(incompleteRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"));

        // Verify that orchestration service was never called due to validation failure
        verify(orchestrationService, never()).refreshHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRefreshEndpoint_InvalidJsonFormat_Returns400() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String invalidJson = "{ \"hipIntegrationName\": \"test\", \"version\": }"; // Invalid JSON

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("INVALID_REQUEST_BODY"));
    }

    @Test
    void testRefreshEndpoint_ServiceException_Returns500() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doThrow(new IntegrationOperationException("refresh", integrationName, version, "Database connection failed"))
                .when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_REFRESH_ERROR"));
    }

    @Test
    void testRefreshEndpoint_PathParametersOverrideRequestBody() throws Exception {
        // Arrange
        String pathIntegrationName = "path-integration";
        String pathVersion = "2.0";
        HIPIntegrationRequest request = createTestRequest("body-integration", "1.0"); // Different values in body

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doNothing().when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", pathIntegrationName, pathVersion)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Assert - Path parameters should override request body values
        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).refreshHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        assertEquals(pathIntegrationName, capturedRequest.getHipIntegrationName());
        assertEquals(pathVersion, capturedRequest.getVersion());
        assertEquals("test-service-manager", capturedRequest.getServiceManagerName());
    }

    @Test
    void testRefreshEndpoint_UpdatesAllConfigurableFields() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createCompleteTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doNothing().when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Assert
        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).refreshHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        assertEquals("updated-business-flow", capturedRequest.getBusinessFlowName());
        assertEquals("updated-owner", capturedRequest.getOwner());
        assertEquals(2, capturedRequest.getTags().size());
        assertEquals("updated", capturedRequest.getTags().get(0).getKey());
        assertNotNull(capturedRequest.getAdapters());
        assertNotNull(capturedRequest.getHandlers());
        assertNotNull(capturedRequest.getFlowSteps());
    }

    /**
     * Helper method to create a basic test HIPIntegrationRequest
     */
    private HIPIntegrationRequest createTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName(integrationName);
        request.setVersion(version);
        request.setBusinessFlowName("test-business-flow");
        request.setOwner("test-owner");

        // Add basic tags
        Tag tag = new Tag();
        tag.setKey("environment");
        tag.setValue("test");
        request.setTags(Arrays.asList(tag));

        return request;
    }

    /**
     * Helper method to create a complete test HIPIntegrationRequest with all fields
     */
    private HIPIntegrationRequest createCompleteTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        
        // Update with more comprehensive data
        request.setBusinessFlowName("updated-business-flow");
        request.setOwner("updated-owner");
        request.setBusinessFlowType("updated-flow-type");
        request.setBusinessFlowVersion("2.0");
        request.setHipIntegrationType("updated-integration-type");

        // Add updated tags
        Tag tag1 = new Tag();
        tag1.setKey("updated");
        tag1.setValue("true");
        Tag tag2 = new Tag();
        tag2.setKey("version");
        tag2.setValue("2.0");
        request.setTags(Arrays.asList(tag1, tag2));

        // Add adapters (minimal for testing)
        AdapterConfigRef adapter = new AdapterConfigRef();
        adapter.setType("kafka");
        adapter.setId("updated-adapter");
        request.setAdapters(Arrays.asList(adapter));

        // Add handlers (minimal for testing)
        request.setHandlers(Arrays.asList());

        // Add flow steps (minimal for testing)
        request.setFlowSteps(Arrays.asList());

        return request;
    }

    @Test
    void testRefreshEndpoint_SpecialCharactersInPathParameters() throws Exception {
        // Arrange
        String integrationName = "test-integration-with-dashes";
        String version = "1.0.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doNothing().when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Assert
        verify(hipIntegrationRegistry).exists(eq("test-service-manager"), eq(integrationName), eq(version));
        verify(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRefreshEndpoint_NullVersion_ValidationError() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        request.setVersion(null); // Set version to null

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"));

        // Verify that orchestration service was never called due to validation failure
        verify(orchestrationService, never()).refreshHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRefreshEndpoint_EmptyRequestBody_ValidationError() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        String emptyJson = "{}";

        // Act & Assert
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(emptyJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"));

        // Verify that orchestration service was never called due to validation failure
        verify(orchestrationService, never()).refreshHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRefreshEndpoint_VerifyInputValidation() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);
        doNothing().when(orchestrationService).refreshHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(put("/hip/management/refresh/{integrationName}/{version}", integrationName, version)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Assert - Verify that input validation was applied to path parameters
        verify(hipIntegrationRegistry).exists(eq("test-service-manager"), eq(integrationName), eq(version));

        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).refreshHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        // Verify that path parameters were properly validated and set
        assertEquals(integrationName, capturedRequest.getHipIntegrationName());
        assertEquals(version, capturedRequest.getVersion());
        assertEquals("test-service-manager", capturedRequest.getServiceManagerName());
    }
}
