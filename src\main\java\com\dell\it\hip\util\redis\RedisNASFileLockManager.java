package com.dell.it.hip.util.redis;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.dell.it.hip.util.NASFileLockManager;
@Component
public class RedisNASFileLockManager implements NASFileLockManager {
    @Autowired
    private HIPRedisCompatibilityService redisService;
    @Value("${service.manager.name}")
    private String serviceManagerName;

    private String lockKey(String integrationName, String version, String adapterId, String fileName) {
        return String.format("hip:nas:lock:%s:%s:%s:%s:%s",
                serviceManagerName, integrationName, version, adapterId, fileName);
    }

    @Override
    public boolean acquireLock(String integrationName, String version, String adapterId, String fileName, String nodeId, int expireSeconds) {
        String key = lockKey(integrationName, version, adapterId, fileName);
        Boolean success = redisService.setIfAbsent(key, nodeId, Duration.ofSeconds(expireSeconds));
        return Boolean.TRUE.equals(success);
    }

    @Override
    public void releaseLock(String integrationName, String version, String adapterId, String fileName) {
        String key = lockKey(integrationName, version, adapterId, fileName);
        redisService.delete(key);
    }
}