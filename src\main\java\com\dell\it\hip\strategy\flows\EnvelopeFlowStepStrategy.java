package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.EnvelopeFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.StructuralValidator;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("envelopeProcessor")
public class EnvelopeFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired private TransactionLoggingUtil transactionLoggingUtil;
    @Autowired private WiretapService wiretapService;

    @Override
    public String getType() { return "envelopeProcessor"; }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        EnvelopeFlowStepConfig config = (EnvelopeFlowStepConfig) def.getConfigMap().get(stepRef.getPropertyRef());
        if (config == null) throw new IllegalStateException("No envelope config for: " + stepRef.getPropertyRef());

        // 1. DocType and DataFormat detection
        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType"); // e.g. "INVOICE:1.0"
        String docTypeVer = (String) message.getHeaders().get("HIP.documentTypeVersion"); // e.g. "INVOICE:1.0"
        String docTypeName = null, docTypeVersion = null;
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }else {
        	docTypeName = docTypeHeader;
        	docTypeVersion = docTypeVer;
        }
        String dataFormat = (String) message.getHeaders().getOrDefault("hip.source.dataFormat", config.getDataFormat());
        if (dataFormat == null) dataFormat = MessageFormatDetector.detect(Objects.toString(message.getPayload(), ""));

        // 2. Find config for docType/dataFormat
        EnvelopeFlowStepConfig.DocTypeEnvelopeConfig dtConfig = findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        EnvelopeFlowStepConfig.DefaultEnvelopeConfig defaultCfg = config.getDefaultConfig();

        EnvelopeFlowStepConfig.EnvelopeBehavior behavior = dtConfig != null
                ? dtConfig.getEnvelopeBehavior()
                : (defaultCfg != null ? defaultCfg.getEnvelopeBehavior() : EnvelopeFlowStepConfig.EnvelopeBehavior.ENVELOPE);

        String payload = Objects.toString(message.getPayload(), "");
        String envelopeHeader = dtConfig != null ? dtConfig.getEnvelopeHeader() : (defaultCfg != null ? defaultCfg.getEnvelopeHeader() : config.getEnvelopeHeader());
        String envelopeFooter = dtConfig != null ? dtConfig.getEnvelopeFooter() : (defaultCfg != null ? defaultCfg.getEnvelopeFooter() : config.getEnvelopeFooter());
        String separator = dtConfig != null ? dtConfig.getSeparator() : (defaultCfg != null ? defaultCfg.getSeparator() : config.getSeparator());
        boolean validateEnvelope = dtConfig != null ? dtConfig.isValidateEnvelope() : (defaultCfg != null ? defaultCfg.isValidateEnvelope() : config.isValidateEnvelope());
        Map<String, String> dynamicHeaders = dtConfig != null ? dtConfig.getDynamicHeaders() : (defaultCfg != null ? defaultCfg.getDynamicHeaders() : config.getDynamicHeaders());

        // Default separator by format if not set
        if (separator == null) {
            separator = switch (dataFormat.toUpperCase()) {
                case "EDI_X12" -> "~";
                case "EDI_EDIFACT" -> "'";
                case "CSV" -> "\n";
                case "XML", "JSON" -> "";
                default -> "";
            };
        }

        // === Envelope operation by behavior ===
        if (behavior == EnvelopeFlowStepConfig.EnvelopeBehavior.SKIP) {
            wiretapService.tap(message, def, stepRef, "info", "Envelope: SKIP for docType " + docTypeHeader);
            transactionLoggingUtil.logInfo(message, def, stepRef, "Envelope SKIP for docType");
            return Collections.singletonList(message);
        } else if (behavior == EnvelopeFlowStepConfig.EnvelopeBehavior.TERMINATE) {
            wiretapService.tap(message, def, stepRef, "terminated", "Envelope: TERMINATE for docType " + docTypeHeader);
            transactionLoggingUtil.logInfo(message, def, stepRef, "Envelope TERMINATE for docType");
            return Collections.emptyList();
        }

        // Handle dynamic header replacement
        if (dynamicHeaders != null && envelopeHeader != null) {
            for (Map.Entry<String, String> entry : dynamicHeaders.entrySet()) {
                envelopeHeader = envelopeHeader.replace("{{" + entry.getKey() + "}}", entry.getValue());
            }
        }

        String envelopedPayload;
        if (envelopeHeader != null && envelopeFooter != null) {
            envelopedPayload = envelopeHeader + separator + payload + separator + envelopeFooter;
        } else if (envelopeHeader != null) {
            envelopedPayload = envelopeHeader + separator + payload;
        } else if (envelopeFooter != null) {
            envelopedPayload = payload + separator + envelopeFooter;
        } else {
            envelopedPayload = payload;
        }

        // Structural validation
        boolean valid = true;
        if (validateEnvelope) {
            valid = StructuralValidator.validate(envelopedPayload, dataFormat);
        }

        MessageBuilder<String> builder = MessageBuilder
                .withPayload(envelopedPayload)
                .copyHeaders(message.getHeaders())
                .setHeader("HIP.payload.dataformat", dataFormat);

        Message<?> resultMsg = builder.build();

        if (!valid) {
            String err = "Envelope structural validation failed for dataFormat: " + dataFormat;
            log.warn(err);
            wiretapService.tap(resultMsg, def, stepRef, "error", err);
            transactionLoggingUtil.logError(resultMsg, def, stepRef, "EnvelopeFlowStep", err);
            return Collections.emptyList();
        } else {
            wiretapService.tap(resultMsg, def, stepRef, "info", "Envelope added and validated for dataFormat: " + dataFormat);
            transactionLoggingUtil.logInfo(resultMsg, def, stepRef, "Envelope added and validated for dataFormat: " + dataFormat);
            return Collections.singletonList(resultMsg);
        }
    }

    private EnvelopeFlowStepConfig.DocTypeEnvelopeConfig findMatchingDocTypeConfig(
            EnvelopeFlowStepConfig config,
            String docTypeName,
            String docTypeVersion,
            String dataFormat
    ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (EnvelopeFlowStepConfig.DocTypeEnvelopeConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }
}
