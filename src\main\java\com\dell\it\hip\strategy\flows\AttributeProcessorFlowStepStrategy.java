package com.dell.it.hip.strategy.flows;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.FlowSteps.AttributeProcessorFlowStepConfig;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AttributeMapping;

import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.dataformatUtils.CsvUtil;
import com.dell.it.hip.util.dataformatUtils.FlatFileUtil;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;
import com.dell.it.hip.util.dataformatUtils.RegexUtil;
import com.dell.it.hip.util.dataformatUtils.StaediUtil;
import com.dell.it.hip.util.dataformatUtils.XmlUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;

@Component("attributeProcessor")
public class AttributeProcessorFlowStepStrategy extends AbstractFlowStepStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AttributeProcessorFlowStepStrategy.class);

    @Autowired
    private WiretapService wiretapService;
    @Autowired private TransactionLoggingUtil transactionLoggingUtil;

    @Override
    public String getType() { return "attributeProcessor"; }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef ref,
            HIPIntegrationDefinition def
    ) throws Exception {
        AttributeProcessorFlowStepConfig config =
                (AttributeProcessorFlowStepConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (config == null) {
            String error = "AttributeProcessorConfig missing for ref: " + ref.getPropertyRef();
            logger.error(error);
            throw new IllegalStateException(error);
        }

        // ==== DocType/dataFormat detection ====
        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
		String docTypeVer = (String) message.getHeaders().get("HIP.documentTypeVersion");
        String docTypeName = null, docTypeVersion = null;
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }else {
        	docTypeName = docTypeHeader;
        	docTypeVersion = docTypeVer;
        }
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null) dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));

        // ==== Find the matching docType config or default ====
        AttributeProcessorFlowStepConfig.DocTypeAttributeProcessorConfig dtConfig =
                findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        AttributeProcessorFlowStepConfig.DefaultAttributeProcessorConfig defaultCfg = config.getDefaultConfig();

        AttributeProcessorFlowStepConfig.AttributeBehavior behavior = dtConfig != null
                ? dtConfig.getBehavior()
                : (defaultCfg != null ? defaultCfg.getBehavior() : AttributeProcessorFlowStepConfig.AttributeBehavior.EXTRACT_AND_SET);

        List<AttributeMapping> attributeMappings = dtConfig != null
                ? dtConfig.getAttributeMappings()
                : (defaultCfg != null ? defaultCfg.getAttributeMappings() : Collections.emptyList());

        // === Handle behaviors ===
        if (behavior == AttributeProcessorFlowStepConfig.AttributeBehavior.SKIP) {
            wiretapService.tap(message, def, ref, "info", "AttributeProcessor: SKIP for docType " + docTypeHeader);
            transactionLoggingUtil.logInfo(message, def, ref, "AttributeProcessor SKIP for docType");
            return Collections.singletonList(message);
        }
        if (behavior == AttributeProcessorFlowStepConfig.AttributeBehavior.TERMINATE) {
            wiretapService.tap(message, def, ref, "terminated", "AttributeProcessor: TERMINATE for docType " + docTypeHeader);
            transactionLoggingUtil.logInfo(message, def, ref, "AttributeProcessor TERMINATE for docType");
            return Collections.emptyList();
        }

        // === Perform attribute extraction and header enrichment ===
        String msgFormat = dataFormat;
        Map<String, Object> enrichedHeaders = new HashMap<>();
        boolean errorFound = false;
        StringBuilder errorLog = new StringBuilder();

        for (AttributeMapping mapping : attributeMappings) {
            String value = null;
            String derivedFrom = mapping.getDerivedFrom();
            if (derivedFrom == null) continue;

            switch (derivedFrom) {
                case "FILENAME":
                    value = (String) message.getHeaders().get("fileName");
                    if (mapping.getExpression() != null && value != null) {
                        value = RegexUtil.extract(value, mapping.getExpression());
                    }
                    break;
                case "TRANSACTION_ROOT_ELEMENT":
                    if ("xml".equalsIgnoreCase(msgFormat)) {
                        value = XmlUtil.getRootElement(String.valueOf(message.getPayload()));
                    } else if ("json".equalsIgnoreCase(msgFormat)) {
                        value = JsonUtil.getRootElement(String.valueOf(message.getPayload()));
                    }
                    break;
                case "ELEMENT_IN_PAYLOAD":
                    if ("xml".equalsIgnoreCase(msgFormat)) {
                        value = XmlUtil.extractFieldFlexible(String.valueOf(message.getPayload()), mapping.getExpression(), null, derivedFrom);
                    } else if ("json".equalsIgnoreCase(msgFormat)) {
                        value = JsonUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("edi-x12".equalsIgnoreCase(msgFormat)) {
                        value = StaediUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("csv".equalsIgnoreCase(msgFormat)) {
                        value = CsvUtil.extractField(String.valueOf(message.getPayload()), mapping.getExpression());
                    } else if ("flat_file".equalsIgnoreCase(msgFormat)) {
                        value = FlatFileUtil.extractFromPayload(String.valueOf(message.getPayload()), mapping.getExpression());
                    }
                    break;
                default:
                    logger.warn("Unknown DerivedFromType: {}", derivedFrom);
            }

            if (value == null && mapping.isRequired()) {
                String err = String.format("Missing required attribute '%s' for mapping '%s'", mapping.getAttributeName(), mapping);
                logger.error(err);
                errorFound = true;
                errorLog.append(err).append("; ");
                // break; // Optionally break or collect all errors
            }

            if (!CollectionUtils.isEmpty(mapping.getUsage())) {
                for (String usedIn : mapping.getUsage()) {
                    String key = "HIP."+docTypeName+":"+docTypeVersion+"." + usedIn + "." + mapping.getAttributeName();
                    enrichedHeaders.put(key, value);
                }
            } else {
                enrichedHeaders.put(mapping.getAttributeName(), value);
            }
        }

        // === Error Handling ===
        if (errorFound) {
            wiretapService.tap(
                    message, def, ref, "error", "Required attribute(s) missing: " + errorLog
            );
            transactionLoggingUtil.logError(message, def, ref, "AttributeProcessor", "Required attribute(s) missing: " + errorLog);
            return Collections.emptyList();
        }

        // === Build enriched message ===
        Message<?> enriched = MessageBuilder
                .fromMessage(message)
                .copyHeaders(enrichedHeaders)
                .setHeader("HIP.messageFormat", msgFormat)
                .build();

        wiretapService.tap(enriched, def, ref, "info", "AttributeProcessor: Extracted and set " + attributeMappings.size() + " attributes");
        transactionLoggingUtil.logInfo(enriched, def, ref, "AttributeProcessor: Extracted and set " + attributeMappings.size() + " attributes");

        return Collections.singletonList(enriched);
    }

    private AttributeProcessorFlowStepConfig.DocTypeAttributeProcessorConfig findMatchingDocTypeConfig(
            AttributeProcessorFlowStepConfig config,
            String docTypeName,
            String docTypeVersion,
            String dataFormat
    ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (AttributeProcessorFlowStepConfig.DocTypeAttributeProcessorConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }
}