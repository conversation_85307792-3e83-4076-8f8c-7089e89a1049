package com.dell.it.hip.config.rules;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleRef {
    private String ruleName;
    private String ruleVersion;
    private Integer id;
    public RuleRef() {}

    public RuleRef(String ruleName, String ruleVersion, Integer id) {
        this.ruleName = ruleName;
        this.ruleVersion = ruleVersion;
        this.id = id;
    }
    // getters/setters
}