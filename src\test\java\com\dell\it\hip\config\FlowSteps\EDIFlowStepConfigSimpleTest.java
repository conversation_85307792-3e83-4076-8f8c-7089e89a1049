package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Simple test to verify EDIFlowStepConfig deserialization works
 */
public class EDIFlowStepConfigSimpleTest {

    public static void main(String[] args) {
        try {
            testBasicDeserialization();
            System.out.println("✅ Basic deserialization test passed!");
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testBasicDeserialization() throws Exception {
        System.out.println("🧪 Testing basic deserialization...");
        
        String json = """
            {
                "configurationList[0].configuration.X12.isaHeader.authQualifier": "00",
                "configurationList[0].configuration.X12.isaHeader.senderId": "IECIRL"
            }
        """;

        ObjectMapper objectMapper = new ObjectMapper();
        EDIFlowStepConfig config = objectMapper.readValue(json, EDIFlowStepConfig.class);

        if (config == null) {
            throw new AssertionError("Config should not be null");
        }
        
        if (config.getEdiTypeConfigs() == null) {
            throw new AssertionError("EdiTypeConfigs should not be null");
        }
        
        if (config.getEdiTypeConfigs().size() != 1) {
            throw new AssertionError("Should have 1 ediTypeConfig, but got: " + config.getEdiTypeConfigs().size());
        }

        EDIFlowStepConfig.Edix12Config ediConfig = config.getEdiTypeConfigs().get(0);
        if (ediConfig.getConfiguration() == null) {
            throw new AssertionError("Configuration should not be null");
        }
        
        if (ediConfig.getConfiguration().getX12() == null) {
            throw new AssertionError("X12 should not be null");
        }

        // Test ISA Header
        EDIFlowStepConfig.Edix12IsaHeader isaHeader = ediConfig.getConfiguration().getX12().getIsaHeader();
        if (isaHeader == null) {
            throw new AssertionError("ISA Header should not be null");
        }
        
        if (!"00".equals(isaHeader.getAuthQualifier())) {
            throw new AssertionError("Auth qualifier should be '00', but got: " + isaHeader.getAuthQualifier());
        }
        
        if (!"IECIRL".equals(isaHeader.getSenderId())) {
            throw new AssertionError("Sender ID should be 'IECIRL', but got: " + isaHeader.getSenderId());
        }

        System.out.println("✅ Basic deserialization test passed!");
    }
}
