package com.dell.it.hip.controller;

import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Security-focused integration tests for HIPIntegrationManagementController
 * to verify protection against regex injection attacks in the refreshIntegrationRules endpoint.
 */
@WebMvcTest(HIPIntegrationManagementController.class)
public class HIPIntegrationManagementControllerSecurityTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HIPIntegrationOrchestrationService orchestrationService;

    @MockBean
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should reject regex metacharacters in integrationName parameter")
    @WithMockUser(roles = "ADMIN")
    public void testRegexMetacharactersRejectedInIntegrationName() throws Exception {
        String[] maliciousIntegrationNames = {
            "test.*integration",     // Dot and asterisk
            "test+integration",      // Plus
            "test?integration",      // Question mark
            "test^integration",      // Caret
            "test$integration",      // Dollar
            "test\\integration",     // Backslash
            "test|integration",      // Pipe
            "test(integration)",     // Parentheses
            "test[integration]",     // Square brackets
            "test{integration}",     // Curly braces
        };

        for (String maliciousName : maliciousIntegrationNames) {
            mockMvc.perform(post("/hip/management/refresh/integration")
                    .param("integrationName", maliciousName)
                    .param("version", "1.0")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }

    @Test
    @DisplayName("Should reject regex metacharacters in version parameter")
    @WithMockUser(roles = "ADMIN")
    public void testRegexMetacharactersRejectedInVersion() throws Exception {
        String[] maliciousVersions = {
            "1.*0",      // Dot and asterisk
            "1+0",       // Plus
            "1?0",       // Question mark
            "1^0",       // Caret
            "1$0",       // Dollar
            "1\\0",      // Backslash
            "1|0",       // Pipe
            "1(0)",      // Parentheses
            "1[0]",      // Square brackets
            "1{0}",      // Curly braces
        };

        for (String maliciousVersion : maliciousVersions) {
            mockMvc.perform(post("/hip/management/refresh/integration")
                    .param("integrationName", "test-integration")
                    .param("version", maliciousVersion)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }

    @Test
    @DisplayName("Should reject ReDoS attack patterns")
    @WithMockUser(roles = "ADMIN")
    public void testReDoSAttackPatternsRejected() throws Exception {
        // Common ReDoS (Regular Expression Denial of Service) attack patterns
        String[] redosPatterns = {
            "(a+)+b",           // Nested quantifiers
            "(a|a)*b",          // Alternation with overlap
            "a*a*a*a*b",        // Multiple quantifiers
            "^(a+)+$",          // Nested quantifiers with anchors
        };

        for (String redosPattern : redosPatterns) {
            // Test as integration name
            mockMvc.perform(post("/hip/management/refresh/integration")
                    .param("integrationName", redosPattern)
                    .param("version", "1.0")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());

            // Test as version (truncated to fit version length limits)
            String shortRedosPattern = redosPattern.length() > 32 ? redosPattern.substring(0, 32) : redosPattern;
            mockMvc.perform(post("/hip/management/refresh/integration")
                    .param("integrationName", "test-integration")
                    .param("version", shortRedosPattern)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }

    @Test
    @DisplayName("Should accept valid integration names and versions")
    @WithMockUser(roles = "ADMIN")
    public void testValidParametersAccepted() throws Exception {
        String[] validIntegrationNames = {
            "test-integration",
            "integration_name",
            "integration.name",
            "Integration123",
            "test-integration-v2"
        };

        String[] validVersions = {
            "1.0",
            "2.1.0",
            "1.0-SNAPSHOT",
            "v1.2.3",
            "1.0_beta"
        };

        for (String validName : validIntegrationNames) {
            for (String validVersion : validVersions) {
                // Note: This will still fail due to missing mocks, but should pass validation
                // and fail with 500 (internal error) rather than 400 (bad request)
                mockMvc.perform(post("/hip/management/refresh/integration")
                        .param("integrationName", validName)
                        .param("version", validVersion)
                        .contentType(MediaType.APPLICATION_JSON))
                        .andExpect(status().isInternalServerError()); // Expecting 500 due to missing service mocks
            }
        }
    }

    @Test
    @DisplayName("Should require ADMIN role for refresh endpoint")
    public void testAdminRoleRequired() throws Exception {
        // Test without authentication
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("Should reject requests with non-ADMIN role")
    @WithMockUser(roles = "USER")
    public void testNonAdminRoleRejected() throws Exception {
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("Should reject missing parameters")
    @WithMockUser(roles = "ADMIN")
    public void testMissingParametersRejected() throws Exception {
        // Missing integrationName
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Missing version
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Missing both parameters
        mockMvc.perform(post("/hip/management/refresh/integration")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("Should reject empty parameters")
    @WithMockUser(roles = "ADMIN")
    public void testEmptyParametersRejected() throws Exception {
        // Empty integrationName
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Empty version
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .param("version", "")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Both empty
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "")
                .param("version", "")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
