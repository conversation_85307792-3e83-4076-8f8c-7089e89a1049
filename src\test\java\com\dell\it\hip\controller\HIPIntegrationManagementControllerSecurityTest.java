package com.dell.it.hip.controller;

import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.config.HIPIntegrationRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Security-focused unit tests for HIPIntegrationManagementController
 * to verify protection against regex injection attacks.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerSecurityTest {

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    @BeforeEach
    void setUp() {
        // Set the service manager name using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", "test-service-manager");
    }

    @Test
    @DisplayName("Should reject regex metacharacters in integrationName parameter")
    public void testRegexMetacharactersRejectedInIntegrationName() throws Exception {
        String[] maliciousIntegrationNames = {
            "test*integration",      // Asterisk (dots removed since they're allowed)
            "test+integration",      // Plus
            "test?integration",      // Question mark
            "test^integration",      // Caret
            "test$integration",      // Dollar
            "test\\integration",     // Backslash
            "test|integration",      // Pipe
            "test(integration)",     // Parentheses
            "test[integration]",     // Square brackets
            "test{integration}",     // Curly braces
        };

        for (String maliciousName : maliciousIntegrationNames) {
            // Act & Assert - should throw IllegalArgumentException due to regex metacharacters
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
                controller.refreshIntegrationRules(maliciousName, "1.0");
            });

            assertTrue(exception.getMessage().contains("regex metacharacters"),
                "Expected regex metacharacters error for: " + maliciousName);
        }
    }

    @Test
    @DisplayName("Should reject regex metacharacters in version parameter")
    public void testRegexMetacharactersRejectedInVersion() throws Exception {
        String[] maliciousVersions = {
            "1*0",       // Asterisk (dots removed since they're allowed)
            "1+0",       // Plus
            "1?0",       // Question mark
            "1^0",       // Caret
            "1$0",       // Dollar
            "1\\0",      // Backslash
            "1|0",       // Pipe
            "1(0)",      // Parentheses
            "1[0]",      // Square brackets
            "1{0}",      // Curly braces
        };

        for (String maliciousVersion : maliciousVersions) {
            // Act & Assert - should throw IllegalArgumentException due to regex metacharacters
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
                controller.refreshIntegrationRules("test-integration", maliciousVersion);
            });

            assertTrue(exception.getMessage().contains("regex metacharacters"),
                "Expected regex metacharacters error for version: " + maliciousVersion);
        }
    }

    @Test
    @DisplayName("Should reject ReDoS attack patterns")
    public void testReDoSAttackPatternsRejected() throws Exception {
        // Common ReDoS (Regular Expression Denial of Service) attack patterns
        String[] redosPatterns = {
            "(a+)+b",           // Nested quantifiers
            "(a|a)*b",          // Alternation with overlap
            "a*a*a*a*b",        // Multiple quantifiers
            "^(a+)+$",          // Nested quantifiers with anchors
        };

        for (String redosPattern : redosPatterns) {
            // Test as integration name - should throw IllegalArgumentException due to regex metacharacters
            IllegalArgumentException exception1 = assertThrows(IllegalArgumentException.class, () -> {
                controller.refreshIntegrationRules(redosPattern, "1.0");
            });
            assertTrue(exception1.getMessage().contains("regex metacharacters"),
                "Expected regex metacharacters error for integration name: " + redosPattern);

            // Test as version - should throw IllegalArgumentException due to regex metacharacters
            String shortRedosPattern = redosPattern.length() > 32 ? redosPattern.substring(0, 32) : redosPattern;
            IllegalArgumentException exception2 = assertThrows(IllegalArgumentException.class, () -> {
                controller.refreshIntegrationRules("test-integration", shortRedosPattern);
            });
            assertTrue(exception2.getMessage().contains("regex metacharacters"),
                "Expected regex metacharacters error for version: " + shortRedosPattern);
        }
    }

    @Test
    @DisplayName("Should accept valid integration names and versions")
    public void testValidParametersAccepted() throws Exception {
        String[] validIntegrationNames = {
            "test-integration",
            "integration_name",
            "integration.name",
            "Integration123",
            "test-integration-v2"
        };

        String[] validVersions = {
            "1.0",
            "2.1.0",
            "1.0-SNAPSHOT",
            "v1.2.3",
            "1.0_beta"
        };

        for (String validName : validIntegrationNames) {
            for (String validVersion : validVersions) {
                // These should pass validation and not throw IllegalArgumentException
                // They may throw other exceptions due to missing service logic, but not validation errors
                try {
                    controller.refreshIntegrationRules(validName, validVersion);
                    // If no exception is thrown, that's fine too (means mocks worked)
                } catch (IllegalArgumentException e) {
                    // This should not happen for valid inputs
                    fail("Valid parameters should not throw IllegalArgumentException: " +
                         "name=" + validName + ", version=" + validVersion + ", error=" + e.getMessage());
                } catch (Exception e) {
                    // Other exceptions are acceptable (e.g., NullPointerException from missing service logic)
                    // We're only testing that validation passes
                }
            }
        }
    }

    // Note: Role-based security tests are handled by Spring Security integration tests
    // This unit test focuses on input validation security

    @Test
    @DisplayName("Should reject missing parameters")
    @WithMockUser(roles = "ADMIN")
    public void testMissingParametersRejected() throws Exception {
        // Missing integrationName
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Missing version
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Missing both parameters
        mockMvc.perform(post("/hip/management/refresh/integration")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("Should reject empty parameters")
    @WithMockUser(roles = "ADMIN")
    public void testEmptyParametersRejected() throws Exception {
        // Empty integrationName
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "")
                .param("version", "1.0")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Empty version
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "test-integration")
                .param("version", "")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        // Both empty
        mockMvc.perform(post("/hip/management/refresh/integration")
                .param("integrationName", "")
                .param("version", "")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
