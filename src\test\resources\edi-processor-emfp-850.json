{"configurationList[0].configuration.X12.isaHeader.authQualifier": "00", "configurationList[0].configuration.X12.isaHeader.authInfo": "          ", "configurationList[0].configuration.X12.isaHeader.securityQualifier": "00", "configurationList[0].configuration.X12.isaHeader.securityInfo": "          ", "configurationList[0].configuration.X12.isaHeader.senderIdQuailifier": "ZZ", "configurationList[0].configuration.X12.isaHeader.senderId": "IECIRL", "configurationList[0].configuration.X12.isaHeader.receiverIdQuailifier": "14", "configurationList[0].configuration.X12.isaHeader.receiverId": "114315195HUB", "configurationList[0].configuration.X12.isaHeader.seperator": "~", "configurationList[0].configuration.X12.isaHeader.controlVersion": "00200", "configurationList[0].configuration.X12.isaHeader.controlNumberStart": "002216035", "configurationList[0].configuration.X12.isaHeader.controlNumberEnd": "002216035", "configurationList[0].configuration.X12.isaHeader.acknowledgementCode": "0", "configurationList[0].configuration.X12.isaHeader.usageIndicatorCode": "P", "configurationList[0].configuration.X12.isaHeader.elementSeperator": "*", "configurationList[0].configuration.X12.gsHeaders[0].documentTypeId": 10083, "configurationList[0].configuration.X12.gsHeaders[0].documentTypeName": "test", "configurationList[0].configuration.X12.gsHeaders[0].documentTypeVersion": "1.0", "configurationList[0].configuration.X12.gsHeaders[0].action": "PROCESS", "configurationList[0].configuration.X12.gsHeaders[0].identifierCode": "QM", "configurationList[0].configuration.X12.gsHeaders[0].senderCode": "IECIRL", "configurationList[0].configuration.X12.gsHeaders[0].receiverCode": "114315195HUB", "configurationList[0].configuration.X12.gsHeaders[0].controlNumberStart": "000805189", "configurationList[0].configuration.X12.gsHeaders[0].controlNumberEnd": "000805189", "configurationList[0].configuration.X12.gsHeaders[0].agencyCode": "T", "configurationList[0].configuration.X12.gsHeaders[0].indentifierCode": "004010", "configurationList[0].configuration.X12.gsHeaders[1].documentTypeId": 10099, "configurationList[0].configuration.X12.gsHeaders[1].documentTypeName": "test-1", "configurationList[0].configuration.X12.gsHeaders[1].documentTypeVersion": "1.0", "configurationList[0].configuration.X12.gsHeaders[1].action": "SKIP", "configurationList[0].configuration.X12.gsHeaders[1].identifierCode": "", "configurationList[0].configuration.X12.gsHeaders[1].senderCode": "", "configurationList[0].configuration.X12.gsHeaders[1].receiverCode": "", "configurationList[0].configuration.X12.gsHeaders[1].controlNumberStart": "", "configurationList[0].configuration.X12.gsHeaders[1].controlNumberEnd": "", "configurationList[0].configuration.X12.gsHeaders[1].agencyCode": "", "configurationList[0].configuration.X12.gsHeaders[1].indentifierCode": "", "defaultConfig.X12.isaHeader.authQualifier": "00"}