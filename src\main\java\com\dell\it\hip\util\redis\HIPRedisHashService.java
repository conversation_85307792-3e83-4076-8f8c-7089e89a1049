package com.dell.it.hip.util.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Hash-based Redis service that provides a unified interface for Redis operations
 * using a single hash key defined by redis.cache.name property.
 * 
 * This service abstracts the complexity of hash-based storage while maintaining
 * backward compatibility with existing Redis operations.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class HIPRedisHashService {

    private static final Logger logger = LoggerFactory.getLogger(HIPRedisHashService.class);

    private final StringRedisTemplate stringRedisTemplate;
    private final RedisTemplate<String, byte[]> byteRedisTemplate;
    private final String cacheHashKey;

    public HIPRedisHashService(
            StringRedisTemplate stringRedisTemplate,
            RedisTemplate<String, byte[]> byteRedisTemplate,
            @Value("${redis.cache.name:HIP-DEFAULT-CACHE}") String cacheHashKey) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.byteRedisTemplate = byteRedisTemplate;
        this.cacheHashKey = cacheHashKey;
        logger.info("HIPRedisHashService initialized with cache hash key: {}", cacheHashKey);
    }

    // ========== STRING VALUE OPERATIONS ==========

    /**
     * Set a string value in the hash cache
     */
    public void set(String field, String value) {
        try {
            stringRedisTemplate.opsForHash().put(cacheHashKey, field, value);
            logger.debug("Set hash field: {} = {}", field, value);
        } catch (Exception e) {
            logger.error("Failed to set hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to set hash field", e);
        }
    }

    /**
     * Set a string value with expiration (using separate key for expiration tracking)
     */
    public void set(String field, String value, Duration timeout) {
        set(field, value);
        // For hash fields with expiration, we need to track expiration separately
        String expirationField = field + ":expires";
        long expirationTime = System.currentTimeMillis() + timeout.toMillis();
        stringRedisTemplate.opsForHash().put(cacheHashKey, expirationField, String.valueOf(expirationTime));
        logger.debug("Set hash field with expiration: {} = {} (expires at {})", field, value, expirationTime);
    }

    /**
     * Set a string value with expiration in seconds
     */
    public void set(String field, String value, long timeout, TimeUnit unit) {
        set(field, value, Duration.ofMillis(unit.toMillis(timeout)));
    }

    /**
     * Get a string value from the hash cache
     */
    public String get(String field) {
        try {
            // Check if field has expiration
            String expirationField = field + ":expires";
            Object expirationObj = stringRedisTemplate.opsForHash().get(cacheHashKey, expirationField);
            
            if (expirationObj != null) {
                long expirationTime = Long.parseLong(expirationObj.toString());
                if (System.currentTimeMillis() > expirationTime) {
                    // Field has expired, remove it
                    stringRedisTemplate.opsForHash().delete(cacheHashKey, field, expirationField);
                    logger.debug("Hash field expired and removed: {}", field);
                    return null;
                }
            }
            
            Object value = stringRedisTemplate.opsForHash().get(cacheHashKey, field);
            String result = value != null ? value.toString() : null;
            logger.debug("Get hash field: {} = {}", field, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to get hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to get hash field", e);
        }
    }

    /**
     * Increment a numeric value in the hash cache
     */
    public Long increment(String field) {
        try {
            Long result = stringRedisTemplate.opsForHash().increment(cacheHashKey, field, 1);
            logger.debug("Incremented hash field: {} = {}", field, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to increment hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to increment hash field", e);
        }
    }

    /**
     * Increment a numeric value by a specific amount
     */
    public Long increment(String field, long delta) {
        try {
            Long result = stringRedisTemplate.opsForHash().increment(cacheHashKey, field, delta);
            logger.debug("Incremented hash field by {}: {} = {}", delta, field, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to increment hash field by {}: {} - {}", delta, field, e.getMessage(), e);
            throw new RuntimeException("Failed to increment hash field", e);
        }
    }

    /**
     * Set if absent (atomic operation for hash fields)
     */
    public Boolean setIfAbsent(String field, String value) {
        try {
            Boolean result = stringRedisTemplate.opsForHash().putIfAbsent(cacheHashKey, field, value);
            logger.debug("Set if absent hash field: {} = {} (result: {})", field, value, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to set if absent hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to set if absent hash field", e);
        }
    }

    /**
     * Set if absent with expiration
     */
    public Boolean setIfAbsent(String field, String value, Duration timeout) {
        Boolean result = setIfAbsent(field, value);
        if (Boolean.TRUE.equals(result)) {
            String expirationField = field + ":expires";
            long expirationTime = System.currentTimeMillis() + timeout.toMillis();
            stringRedisTemplate.opsForHash().put(cacheHashKey, expirationField, String.valueOf(expirationTime));
        }
        return result;
    }

    /**
     * Set if absent with expiration in time units
     */
    public Boolean setIfAbsent(String field, String value, long timeout, TimeUnit unit) {
        return setIfAbsent(field, value, Duration.ofMillis(unit.toMillis(timeout)));
    }

    // ========== BYTE ARRAY OPERATIONS ==========

    /**
     * Set byte array value in the hash cache
     */
    public void setBytes(String field, byte[] value) {
        try {
            byteRedisTemplate.opsForHash().put(cacheHashKey, field, value);
            logger.debug("Set byte hash field: {} (length: {})", field, value != null ? value.length : 0);
        } catch (Exception e) {
            logger.error("Failed to set byte hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to set byte hash field", e);
        }
    }

    /**
     * Get byte array value from the hash cache
     */
    public byte[] getBytes(String field) {
        try {
            Object value = byteRedisTemplate.opsForHash().get(cacheHashKey, field);
            byte[] result = value != null ? (byte[]) value : null;
            logger.debug("Get byte hash field: {} (length: {})", field, result != null ? result.length : 0);
            return result;
        } catch (Exception e) {
            logger.error("Failed to get byte hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to get byte hash field", e);
        }
    }

    // ========== HASH OPERATIONS ==========

    /**
     * Check if a field exists in the hash cache
     */
    public Boolean hasField(String field) {
        try {
            Boolean result = stringRedisTemplate.opsForHash().hasKey(cacheHashKey, field);
            logger.debug("Hash has field: {} = {}", field, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to check hash field existence: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to check hash field existence", e);
        }
    }

    /**
     * Delete a field from the hash cache
     */
    public Long delete(String field) {
        try {
            // Also delete expiration field if it exists
            String expirationField = field + ":expires";
            Long result = stringRedisTemplate.opsForHash().delete(cacheHashKey, field, expirationField);
            logger.debug("Deleted hash field: {} (result: {})", field, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to delete hash field: {} - {}", field, e.getMessage(), e);
            throw new RuntimeException("Failed to delete hash field", e);
        }
    }

    /**
     * Delete multiple fields from the hash cache
     */
    public Long delete(String... fields) {
        try {
            // Create array with fields and their expiration fields
            String[] allFields = new String[fields.length * 2];
            for (int i = 0; i < fields.length; i++) {
                allFields[i * 2] = fields[i];
                allFields[i * 2 + 1] = fields[i] + ":expires";
            }
            
            Long result = stringRedisTemplate.opsForHash().delete(cacheHashKey, (Object[]) allFields);
            logger.debug("Deleted {} hash fields (result: {})", fields.length, result);
            return result;
        } catch (Exception e) {
            logger.error("Failed to delete hash fields - {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete hash fields", e);
        }
    }

    /**
     * Get all fields in the hash cache
     */
    public Set<Object> getFields() {
        try {
            Set<Object> fields = stringRedisTemplate.opsForHash().keys(cacheHashKey);
            logger.debug("Retrieved {} hash fields", fields.size());
            return fields;
        } catch (Exception e) {
            logger.error("Failed to get hash fields - {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get hash fields", e);
        }
    }

    /**
     * Get all field-value pairs in the hash cache
     */
    public Map<Object, Object> getAll() {
        try {
            Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(cacheHashKey);
            logger.debug("Retrieved {} hash entries", entries.size());
            return entries;
        } catch (Exception e) {
            logger.error("Failed to get all hash entries - {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get all hash entries", e);
        }
    }

    /**
     * Get the size of the hash cache
     */
    public Long size() {
        try {
            Long size = stringRedisTemplate.opsForHash().size(cacheHashKey);
            logger.debug("Hash cache size: {}", size);
            return size;
        } catch (Exception e) {
            logger.error("Failed to get hash cache size - {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get hash cache size", e);
        }
    }

    // ========== UTILITY METHODS ==========

    /**
     * Get the cache hash key being used
     */
    public String getCacheHashKey() {
        return cacheHashKey;
    }

    /**
     * Clear all expired fields from the hash cache
     */
    public void cleanupExpiredFields() {
        try {
            Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(cacheHashKey);
            long currentTime = System.currentTimeMillis();
            int cleanedCount = 0;
            
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                String field = entry.getKey().toString();
                if (field.endsWith(":expires")) {
                    long expirationTime = Long.parseLong(entry.getValue().toString());
                    if (currentTime > expirationTime) {
                        String originalField = field.substring(0, field.length() - ":expires".length());
                        stringRedisTemplate.opsForHash().delete(cacheHashKey, originalField, field);
                        cleanedCount++;
                    }
                }
            }
            
            if (cleanedCount > 0) {
                logger.info("Cleaned up {} expired hash fields", cleanedCount);
            }
        } catch (Exception e) {
            logger.error("Failed to cleanup expired fields - {}", e.getMessage(), e);
        }
    }
}
