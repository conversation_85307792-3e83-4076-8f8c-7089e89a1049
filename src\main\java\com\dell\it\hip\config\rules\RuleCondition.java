package com.dell.it.hip.config.rules;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleCondition {
    private String propertyName;
    private String operation; // EQUALS, STARTS_WITH, CONTAINS
    private String value;
    public enum Operator { EQUALS, STARTS_WITH, CONTAINS }
    // Getters/setters
}