package com.dell.it.hip.util.redis;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Component;

import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;

@Component
public class ContivoMapCache {
    private final HIPRedisHashService hashService;
    private final Map<String, ContivoMapConfig> cache = new ConcurrentHashMap<>();

    public ContivoMapCache(HIPRedisHashService hashService) {
        this.hashService = hashService;
    }
    
    public byte[] loadMapData(String compositeKey) {
    	byte[] mapData = hashService.getBytes(compositeKey);
		return mapData;
    }

    public ContivoMapConfig getOrLoad(String mapIdentifier, String mapIdentifierVersion) {
        String compositeKey = mapIdentifier + ":" + mapIdentifierVersion;
        return cache.computeIfAbsent(compositeKey, key -> {
            String json = hashService.get(key);
            if (json == null)
                throw new RuntimeException("ContivoMap not found in Redis for key: " + key);
            return JsonUtil.fromJson(json, ContivoMapConfig.class);
        });
    }

    // Explicit refresh for a single map
    public void refresh(String mapIdentifier, String mapIdentifierVersion) {
        String compositeKey = mapIdentifier + ":" + mapIdentifierVersion;
        String json = hashService.get(compositeKey);
        if (json != null) {
            cache.put(compositeKey, JsonUtil.fromJson(json, ContivoMapConfig.class));
        } else {
            cache.remove(compositeKey);
        }
    }

    // Clear all cache if needed
    public void clear() {
        cache.clear();
    }
}