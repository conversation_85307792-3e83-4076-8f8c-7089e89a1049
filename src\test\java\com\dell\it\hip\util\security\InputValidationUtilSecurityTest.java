package com.dell.it.hip.util.security;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Security-focused tests for InputValidationUtil to verify XSS protection
 * and proper input sanitization.
 */
@DisplayName("InputValidationUtil Security Tests")
class InputValidationUtilSecurityTest {

    // === Tests for sanitizeForOutput method ===

    @Test
    @DisplayName("Test sanitizeForOutput with null input")
    void testSanitizeForOutput_NullInput() {
        // Act
        String result = InputValidationUtil.sanitizeForOutput(null);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test sanitizeForOutput with safe enum values")
    void testSanitizeForOutput_SafeEnumValues() {
        // Arrange
        String[] safeValues = {"RUNNING", "PAUSED", "ERROR", "UNREGISTERED"};

        // Act & Assert
        for (String safeValue : safeValues) {
            String result = InputValidationUtil.sanitizeForOutput(safeValue);
            assertEquals(safeValue, result);
        }
    }

    @Test
    @DisplayName("Test sanitizeForOutput with whitespace")
    void testSanitizeForOutput_WithWhitespace() {
        // Arrange
        String input = "  RUNNING  ";

        // Act
        String result = InputValidationUtil.sanitizeForOutput(input);

        // Assert
        assertEquals("RUNNING", result);
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "<script>alert('xss')</script>",
        "<img src=x onerror=alert('xss')>",
        "javascript:alert('xss')",
        "<svg onload=alert('xss')>",
        "RUNNING<script>alert('xss')</script>",
        "<script>RUNNING</script>",
        "RUN<script>NING"
    })
    @DisplayName("Test sanitizeForOutput with malicious input returns ERROR")
    void testSanitizeForOutput_MaliciousInput(String maliciousInput) {
        // Act
        String result = InputValidationUtil.sanitizeForOutput(maliciousInput);

        // Assert
        assertEquals("ERROR", result);
    }

    // === Tests for containsDangerousCharacters method ===

    @Test
    @DisplayName("Test containsDangerousCharacters with null input")
    void testContainsDangerousCharacters_NullInput() {
        // Act
        boolean result = InputValidationUtil.containsDangerousCharacters(null);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Test containsDangerousCharacters with safe input")
    void testContainsDangerousCharacters_SafeInput() {
        // Arrange
        String[] safeInputs = {
            "test-integration",
            "1.0.0",
            "RUNNING",
            "valid_name_123",
            "integration-v2"
        };

        // Act & Assert
        for (String safeInput : safeInputs) {
            boolean result = InputValidationUtil.containsDangerousCharacters(safeInput);
            assertFalse(result, "Input should be safe: " + safeInput);
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "<script>",
        "</script>",
        "<img>",
        "javascript:",
        "vbscript:",
        "onload=",
        "onerror=",
        "onclick=",
        "onmouseover=",
        "alert(",
        "eval(",
        "document.",
        "window.",
        "<svg",
        "<iframe",
        "<object",
        "<embed"
    })
    @DisplayName("Test containsDangerousCharacters detects XSS vectors")
    void testContainsDangerousCharacters_DetectsXSSVectors(String dangerousInput) {
        // Act
        boolean result = InputValidationUtil.containsDangerousCharacters(dangerousInput);

        // Assert
        assertTrue(result, "Should detect dangerous input: " + dangerousInput);
    }

    // === Integration tests for XSS protection workflow ===

    @Test
    @DisplayName("Test complete XSS protection workflow")
    void testCompleteXSSProtectionWorkflow() {
        // Arrange
        String maliciousName = "<script>alert('xss')</script>";
        String maliciousVersion = "<img src=x onerror=alert('xss')>";

        // Act & Assert - Input validation should catch these
        assertThrows(IllegalArgumentException.class, () -> {
            InputValidationUtil.validateAndSanitizeIntegrationName(maliciousName);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            InputValidationUtil.validateAndSanitizeVersion(maliciousVersion);
        });
    }

    @Test
    @DisplayName("Test output sanitization as defense in depth")
    void testOutputSanitizationDefenseInDepth() {
        // Arrange - Even if somehow malicious content gets through input validation
        String potentiallyDangerousOutput = "RUNNING<script>alert('xss')</script>";

        // Act
        String result = InputValidationUtil.sanitizeForOutput(potentiallyDangerousOutput);

        // Assert - Output sanitization should catch it
        assertEquals("ERROR", result);
    }

    @Test
    @DisplayName("Test edge cases for output sanitization")
    void testOutputSanitizationEdgeCases() {
        // Test empty string
        String result1 = InputValidationUtil.sanitizeForOutput("");
        assertEquals("", result1);

        // Test string with only whitespace
        String result2 = InputValidationUtil.sanitizeForOutput("   ");
        assertEquals("", result2);

        // Test valid status with extra whitespace
        String result3 = InputValidationUtil.sanitizeForOutput("\t\nRUNNING\r\n  ");
        assertEquals("RUNNING", result3);
    }

    // === Performance and robustness tests ===

    @Test
    @DisplayName("Test sanitizeForOutput with very long input")
    void testSanitizeForOutput_LongInput() {
        // Arrange - Create a very long string with dangerous content
        StringBuilder longInput = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longInput.append("RUNNING");
        }
        longInput.append("<script>alert('xss')</script>");

        // Act
        String result = InputValidationUtil.sanitizeForOutput(longInput.toString());

        // Assert
        assertEquals("ERROR", result);
    }

    @Test
    @DisplayName("Test containsDangerousCharacters with case variations")
    void testContainsDangerousCharacters_CaseVariations() {
        // Arrange
        String[] dangerousInputs = {
            "<SCRIPT>",
            "<Script>",
            "JAVASCRIPT:",
            "JavaScript:",
            "ALERT(",
            "Alert("
        };

        // Act & Assert
        for (String dangerousInput : dangerousInputs) {
            boolean result = InputValidationUtil.containsDangerousCharacters(dangerousInput);
            assertTrue(result, "Should detect dangerous input regardless of case: " + dangerousInput);
        }
    }
}
