package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * Manual test class to verify EDIFlowStepConfig deserialization
 * This can be run independently to test the deserializer functionality
 */
public class EDIFlowStepConfigManualTest {

    public static void main(String[] args) {
        try {
            testBasicDeserialization();
            testActualEdiProcessorFile();
            testComplexNestedStructure();
            testDefaultConfigOnly();
            System.out.println("✅ All manual tests passed!");
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testBasicDeserialization() throws Exception {
        System.out.println("🧪 Testing basic deserialization...");
        
        String json = """
            {
                "configurationList[0].configuration.X12.isaHeader.authQualifier": "00",
                "configurationList[0].configuration.X12.isaHeader.senderId": "IECIRL",
                "configurationList[0].configuration.X12.gsHeaders[0].documentTypeId": 10083,
                "configurationList[0].configuration.X12.gsHeaders[0].documentTypeName": "test",
                "configurationList[0].configuration.X12.gsHeaders[0].action": "PROCESS"
            }
        """;

        ObjectMapper objectMapper = new ObjectMapper();
        EDIFlowStepConfig config = objectMapper.readValue(json, EDIFlowStepConfig.class);

        assert config != null : "Config should not be null";
        assert config.getEdiTypeConfigs() != null : "EdiTypeConfigs should not be null";
        assert config.getEdiTypeConfigs().size() == 1 : "Should have 1 ediTypeConfig";

        EDIFlowStepConfig.Edix12Config ediConfig = config.getEdiTypeConfigs().get(0);
        assert ediConfig.getConfiguration() != null : "Configuration should not be null";
        assert ediConfig.getConfiguration().getX12() != null : "X12 should not be null";

        // Test ISA Header
        EDIFlowStepConfig.Edix12IsaHeader isaHeader = ediConfig.getConfiguration().getX12().getIsaHeader();
        assert isaHeader != null : "ISA Header should not be null";
        assert "00".equals(isaHeader.getAuthQualifier()) : "Auth qualifier should be '00'";
        assert "IECIRL".equals(isaHeader.getSenderId()) : "Sender ID should be 'IECIRL'";

        // Test GS Headers
        assert ediConfig.getConfiguration().getX12().getGsHeaders() != null : "GS Headers should not be null";
        assert ediConfig.getConfiguration().getX12().getGsHeaders().size() == 1 : "Should have 1 GS header";

        EDIFlowStepConfig.Edix12GsHeader gsHeader = ediConfig.getConfiguration().getX12().getGsHeaders().get(0);
        assert gsHeader.getDocumentTypeId() == 10083 : "Document type ID should be 10083";
        assert "test".equals(gsHeader.getName()) : "Document name should be 'test'";
        assert gsHeader.getAction() == EDIFlowStepConfig.EDIBehavior.PROCESS : "Action should be PROCESS";

        System.out.println("✅ Basic deserialization test passed!");
    }

    private static void testActualEdiProcessorFile() throws Exception {
        System.out.println("🧪 Testing actual edi-processor-emfp-850.json file...");
        
        // Load the actual JSON file from test resources
        InputStream inputStream = EDIFlowStepConfigManualTest.class.getClassLoader()
                .getResourceAsStream("edi-processor-emfp-850.json");
        assert inputStream != null : "edi-processor-emfp-850.json file should exist in test resources";
        
        String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        assert jsonContent != null && !jsonContent.trim().isEmpty() : "JSON content should not be empty";
        
        ObjectMapper objectMapper = new ObjectMapper();
        EDIFlowStepConfig config = objectMapper.readValue(jsonContent, EDIFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assert config != null : "Config should not be null";
        assert config.getEdiTypeConfigs() != null : "EdiTypeConfigs should not be null";
        assert config.getEdiTypeConfigs().size() == 1 : "Should have 1 ediTypeConfig as per edi-processor-emfp-850.json";
        
        // Verify the first configuration
        EDIFlowStepConfig.Edix12Config ediConfig = config.getEdiTypeConfigs().get(0);
        assert ediConfig.getConfiguration() != null : "Configuration should not be null";
        assert ediConfig.getConfiguration().getX12() != null : "X12 should not be null";
        
        // Verify ISA Header from the JSON file
        EDIFlowStepConfig.Edix12IsaHeader isaHeader = ediConfig.getConfiguration().getX12().getIsaHeader();
        assert isaHeader != null : "ISA Header should not be null";
        assert "00".equals(isaHeader.getAuthQualifier()) : "Auth qualifier should be '00'";
        assert "IECIRL".equals(isaHeader.getSenderId()) : "Sender ID should be 'IECIRL'";
        assert "114315195HUB".equals(isaHeader.getReceiverId()) : "Receiver ID should be '114315195HUB'";
        
        // Verify GS Headers from the JSON file
        assert ediConfig.getConfiguration().getX12().getGsHeaders() != null : "GS Headers should not be null";
        assert ediConfig.getConfiguration().getX12().getGsHeaders().size() == 2 : "Should have 2 GS headers as per JSON file";
        
        // First GS Header
        EDIFlowStepConfig.Edix12GsHeader gsHeader1 = ediConfig.getConfiguration().getX12().getGsHeaders().get(0);
        assert gsHeader1.getDocumentTypeId() == 10083 : "First GS header document type ID should be 10083";
        assert "test".equals(gsHeader1.getName()) : "First GS header name should be 'test'";
        assert gsHeader1.getAction() == EDIFlowStepConfig.EDIBehavior.PROCESS : "First GS header action should be PROCESS";
        
        // Second GS Header
        EDIFlowStepConfig.Edix12GsHeader gsHeader2 = ediConfig.getConfiguration().getX12().getGsHeaders().get(1);
        assert gsHeader2.getDocumentTypeId() == 10099 : "Second GS header document type ID should be 10099";
        assert "test-1".equals(gsHeader2.getName()) : "Second GS header name should be 'test-1'";
        assert gsHeader2.getAction() == EDIFlowStepConfig.EDIBehavior.SKIP : "Second GS header action should be SKIP";
        
        // Verify default config
        assert config.getDefaultConfig() != null : "Default config should not be null";
        assert config.getDefaultConfig().getX12() != null : "Default config X12 should not be null";
        assert config.getDefaultConfig().getX12().getIsaHeader() != null : "Default config ISA header should not be null";
        assert "00".equals(config.getDefaultConfig().getX12().getIsaHeader().getAuthQualifier()) : "Default config auth qualifier should be '00'";
        
        System.out.println("✅ Actual edi-processor-emfp-850.json file test passed!");
    }

    private static void testComplexNestedStructure() throws Exception {
        System.out.println("🧪 Testing complex nested structure...");
        
        String json = """
            {
                "propertyRef": "edi-step-complex",
                "type": "edi-processor",
                "configurationList[0].configuration.X12.isaHeader.authQualifier": "00",
                "configurationList[0].configuration.X12.isaHeader.authInfo": "          ",
                "configurationList[0].configuration.X12.isaHeader.securityQualifier": "00",
                "configurationList[0].configuration.X12.isaHeader.securityInfo": "          ",
                "configurationList[0].configuration.X12.isaHeader.senderIdQuailifier": "ZZ",
                "configurationList[0].configuration.X12.isaHeader.senderId": "SENDER123",
                "configurationList[0].configuration.X12.gsHeaders[0].documentTypeId": 850,
                "configurationList[0].configuration.X12.gsHeaders[0].documentTypeName": "Purchase Order",
                "configurationList[0].configuration.X12.gsHeaders[0].action": "PROCESS",
                "configurationList[0].configuration.X12.gsHeaders[1].documentTypeId": 855,
                "configurationList[0].configuration.X12.gsHeaders[1].documentTypeName": "Purchase Order Acknowledgment",
                "configurationList[0].configuration.X12.gsHeaders[1].action": "SKIP",
                "configurationList[1].configuration.X12.isaHeader.senderId": "SENDER456",
                "configurationList[1].configuration.X12.gsHeaders[0].documentTypeId": 810,
                "configurationList[1].configuration.X12.gsHeaders[0].documentTypeName": "Invoice",
                "defaultConfig.X12.isaHeader.authQualifier": "01"
            }
        """;

        ObjectMapper objectMapper = new ObjectMapper();
        EDIFlowStepConfig config = objectMapper.readValue(json, EDIFlowStepConfig.class);

        // Verify FlowStepConfig properties
        assert "edi-step-complex".equals(config.getPropertyRef()) : "PropertyRef should be 'edi-step-complex'";
        assert "edi-processor".equals(config.getType()) : "Type should be 'edi-processor'";

        // Verify multiple configuration list entries
        assert config.getEdiTypeConfigs().size() == 2 : "Should have 2 ediTypeConfigs";

        // First config
        EDIFlowStepConfig.Edix12Config config1 = config.getEdiTypeConfigs().get(0);
        assert "SENDER123".equals(config1.getConfiguration().getX12().getIsaHeader().getSenderId()) : "First config sender should be 'SENDER123'";
        assert config1.getConfiguration().getX12().getGsHeaders().size() == 2 : "First config should have 2 GS headers";

        // Second config
        EDIFlowStepConfig.Edix12Config config2 = config.getEdiTypeConfigs().get(1);
        assert "SENDER456".equals(config2.getConfiguration().getX12().getIsaHeader().getSenderId()) : "Second config sender should be 'SENDER456'";
        assert config2.getConfiguration().getX12().getGsHeaders().size() == 1 : "Second config should have 1 GS header";

        // Default config
        assert config.getDefaultConfig() != null : "Default config should not be null";
        assert "01".equals(config.getDefaultConfig().getX12().getIsaHeader().getAuthQualifier()) : "Default config auth qualifier should be '01'";

        System.out.println("✅ Complex nested structure test passed!");
    }

    private static void testDefaultConfigOnly() throws Exception {
        System.out.println("🧪 Testing default config only...");
        
        String json = """
            {
                "defaultConfig.X12.isaHeader.authQualifier": "00",
                "defaultConfig.X12.isaHeader.senderId": "DEFAULT_SENDER",
                "defaultConfig.X12.isaHeader.receiverId": "DEFAULT_RECEIVER"
            }
        """;

        ObjectMapper objectMapper = new ObjectMapper();
        EDIFlowStepConfig config = objectMapper.readValue(json, EDIFlowStepConfig.class);

        assert config != null : "Config should not be null";
        assert config.getEdiTypeConfigs() == null || config.getEdiTypeConfigs().isEmpty() : "EdiTypeConfigs should be empty";
        assert config.getDefaultConfig() != null : "Default config should not be null";

        EDIFlowStepConfig.Edix12IsaHeader defaultIsaHeader = config.getDefaultConfig().getX12().getIsaHeader();
        assert "00".equals(defaultIsaHeader.getAuthQualifier()) : "Default auth qualifier should be '00'";
        assert "DEFAULT_SENDER".equals(defaultIsaHeader.getSenderId()) : "Default sender should be 'DEFAULT_SENDER'";
        assert "DEFAULT_RECEIVER".equals(defaultIsaHeader.getReceiverId()) : "Default receiver should be 'DEFAULT_RECEIVER'";

        System.out.println("✅ Default config only test passed!");
    }
}
