package com.dell.it.hip.util.security;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class DebugValidationTest {

    @Test
    public void debugValidationMessages() {
        String[] testInputs = {
            "test\\integration",     // Backslash
            "test^integration",      // Caret
            "test$integration",      // Dollar
            "test|integration",      // Pipe
        };

        for (String input : testInputs) {
            try {
                InputValidationUtil.validateAndSanitizeIntegrationName(input);
                System.out.println("Input '" + input + "' passed validation - this should not happen");
            } catch (IllegalArgumentException e) {
                System.out.println("Input '" + input + "' failed with message: " + e.getMessage());
            }
        }
    }
}
