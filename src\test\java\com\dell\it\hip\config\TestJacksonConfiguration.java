package com.dell.it.hip.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Test configuration to disable Jackson module auto-discovery to avoid Scala version conflicts.
 * This prevents the Jackson Scala module from being auto-discovered and causing NoSuchMethodError
 * due to version mismatches between Scala 2.12 and 2.13 libraries in the classpath.
 */
@TestConfiguration
public class TestJacksonConfiguration {

    @Bean
    @Primary
    public Jackson2ObjectMapperBuilder jackson2ObjectMapperBuilder() {
        return new Jackson2ObjectMapperBuilder()
                .findModulesViaServiceLoader(false); // Disable module auto-discovery
    }

    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        // Create ObjectMapper using the builder that has auto-discovery disabled
        return builder.build();
    }
}
