package com.dell.it.hip.strategy.adapters;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

import com.dell.it.hip.util.ThrottleSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicNASAdapterConfig;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.NASFileLockManager;
import com.dell.it.hip.util.logging.WiretapService;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import jcifs.CIFSContext;
import jcifs.config.PropertyConfiguration;
import jcifs.context.BaseContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbFile;

@Component("nasAdapter")
public class DynamicNasInputAdapter extends AbstractDynamicInputAdapter {

    private static final Logger logger = LoggerFactory.getLogger(DynamicNasInputAdapter.class);

    @Autowired private WiretapService wiretapService;
    @Autowired private ThreadPoolTaskExecutor taskExecutor;
    @Autowired private NASFileLockManager nasFileLockManager;

    private final Map<String, Future<?>> pollingTasks = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> pausedMap = new ConcurrentHashMap<>();
    private final String nodeId = UUID.randomUUID().toString();

    private String getNodeId() { return nodeId; }
    
    @Autowired
    private Tracer tracer;

    @Override
    public String getType() { return "nasAdapter"; }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        if (!getType().equals(ref.getType())) return;
        DynamicNASAdapterConfig cfg = (DynamicNASAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) throw new IllegalStateException("No config found for NAS adapter ref: " + ref.getPropertyRef());

        String adapterKey = key(def, ref);

        pausedMap.put(adapterKey, new AtomicBoolean(false));
        Runnable poller = () -> pollLoop(def, ref, cfg, adapterKey);

        Future<?> future = taskExecutor.submit(poller);
        registerAdapterInstance(def, ref, new NASAdapterInstance(adapterKey, future));
        pollingTasks.put(adapterKey, future);

        logger.info("Started NAS poller for {}", adapterKey);
    }

    private void pollLoop(HIPIntegrationDefinition def, AdapterConfigRef ref, DynamicNASAdapterConfig cfg, String adapterKey) {
        long lastPollTime = 0;
        while (!Thread.currentThread().isInterrupted()) {
            try {
                if (pausedMap.getOrDefault(adapterKey, new AtomicBoolean(false)).get()) {
                    logger.info("NAS adapter paused: {}", adapterKey);
                    Thread.sleep(2000L);
                    continue;
                }

                long now = System.currentTimeMillis();
                boolean intervalDue = (now - lastPollTime) >= (cfg.getPollingIntervalMs() != null ? cfg.getPollingIntervalMs() : 60000L);

                if (intervalDue) {
                    pollNasForFiles(def, ref, cfg);
                    lastPollTime = now;
                }

                Thread.sleep(cfg.getPollingIntervalMs() != null ? cfg.getPollingIntervalMs() : 60000L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("NAS poller thread interrupted for {}", adapterKey);
                break;
            } catch (Exception ex) {
                logger.error("Exception in NAS polling for {}: {}", adapterKey, ex.getMessage(), ex);
            }
        }
        logger.info("NAS poller loop exited for {}", adapterKey);
    }

    private void pollNasForFiles(HIPIntegrationDefinition def, AdapterConfigRef ref, DynamicNASAdapterConfig cfg) {
        String protocol = cfg.getProtocol();
        String filePattern = cfg.getParameters() != null && cfg.getParameters().containsKey("fileNamePattern")
                ? (String) cfg.getParameters().get("fileNamePattern")
                : cfg.getFileNamePattern() != null ? cfg.getFileNamePattern() : ".*";
        Pattern pattern = Pattern.compile(filePattern);

        try {
            if ("smb".equalsIgnoreCase(protocol)) {
                if (cfg.getMountPath() != null && new File(cfg.getMountPath()).exists()) {
                    pollLocalFiles(cfg.getMountPath(), pattern, def, ref, cfg);
                } else {
                    pollRemoteSmbFilesWithJCIFS(def, ref, cfg, pattern);
                }
            } else if ("nfs".equalsIgnoreCase(protocol)) {
                pollLocalFiles(cfg.getRemoteDirectory(), pattern, def, ref, cfg);
            } else {
                logger.warn("Unsupported NAS protocol: {}", protocol);
            }
        } catch (Exception ex) {
            logger.error("Error polling NAS files: {}", ex.getMessage(), ex);
        }
    }

    // Local OS-mounted polling (for NFS or mounted SMB)
    private void pollLocalFiles(String dirPath, Pattern filePattern, HIPIntegrationDefinition def,
                                AdapterConfigRef ref, DynamicNASAdapterConfig cfg) {
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("NAS directory not found or not a directory: {}", dirPath);
            return;
        }

        File[] files = dir.listFiles((f) -> f.isFile() && filePattern.matcher(f.getName()).matches());
        if (files == null || files.length == 0) return;

        // Sort files as needed
        List<File> fileList = Arrays.asList(files);
        if ("OLDEST".equalsIgnoreCase(cfg.getFileSortOrder())) {
            fileList.sort(Comparator.comparingLong(File::lastModified));
        } else if ("NEWEST".equalsIgnoreCase(cfg.getFileSortOrder())) {
            fileList.sort((a, b) -> Long.compare(b.lastModified(), a.lastModified()));
        }

        int processed = 0;
        int maxFilesPerPoll = cfg.getMaxFilesPerPoll() != null ? cfg.getMaxFilesPerPoll() : 100;
        for (File file : fileList) {
            if (processed >= maxFilesPerPoll) break;
            if (cfg.getIgnoreHiddenFiles() != null && cfg.getIgnoreHiddenFiles() && file.isHidden()) continue;
            if (cfg.getFileAgeMs() != null) {
                long fileAge = System.currentTimeMillis() - file.lastModified();
                if (fileAge < cfg.getFileAgeMs()) continue;
            }

            boolean locked = nasFileLockManager.acquireLock(
                    def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName(), getNodeId(), 600
            );
            if (!locked) continue;
            
         // 1. Start a new span since SFTP is always a source (no upstream tracecontext)
            Span span = tracer.spanBuilder("sftp.receive")
                    .setSpanKind(SpanKind.CONSUMER)
                    .startSpan();

            try(Scope scope = span.makeCurrent()) {
                byte[] fileContent = CompressionUtil.decompress(Files.readAllBytes(file.toPath()));

                // ==== Throttling: Only process if token is available ====
                ThrottleSettings settings = getThrottleSettings(def, ref);
                boolean allowed = throttlingService.tryConsumeToken(
                        serviceManagerName,
                        def.getHipIntegrationName(),
                        def.getVersion(),
                        ref.getPropertyRef(),
                        settings
                );
                if (!allowed) {
                    nasFileLockManager.releaseLock(def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName());
                    logger.info("Throttling: Not processing file {} this interval, will retry in next poll", file.getName());
                    break; // Exit loop: no further files this cycle (guarantee per-poll limit)
                }

                Message<?> msg = buildMessage(def, ref, file.getName(), fileContent, cfg, file);
                

                logger.debug("SFTP tracing span started: traceId={}, spanId={}", 
                        span.getSpanContext().getTraceId(), span.getSpanContext().getSpanId());

                // Inject OTel context if not present
                Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(msg);

                // Wiretap: started event as soon as message enters HIP
                wiretapService.tap(
                        msgWithTrace,
                        def,
                        ref,
                        "started",
                        "HIPIntegration received file from NAS: " + file.getName()
                );

                processInboundMessage(def, ref, msgWithTrace, getInputChannel(def));
                handlePostProcess(file, cfg);

                processed++;
            } catch (Exception ex) {
                logger.error("Failed to process NAS file {}", file.getName(), ex);
                wiretapService.tap(null, def, ref, "error", "Failed to process file: " + file.getName());
            } finally {
            	span.end();
                nasFileLockManager.releaseLock(def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName());
            }
        }
    }

    // SMB polling using JCIFS-NG (for remote/unmounted SMB)
    private void pollRemoteSmbFilesWithJCIFS(HIPIntegrationDefinition def, AdapterConfigRef ref, DynamicNASAdapterConfig cfg, Pattern filePattern) throws Exception {
        Properties prop = new Properties();
        prop.setProperty("jcifs.smb.client.enableSMB2", "true");
        CIFSContext base = new BaseContext(new PropertyConfiguration(prop));
        CIFSContext context = base.withCredentials(
                new NtlmPasswordAuthenticator(
                        cfg.getDomain() != null ? cfg.getDomain() : "",
                        cfg.getUsername(),
                        cfg.getPassword() != null ? cfg.getPassword() : ""
                )
        );
        String smbPath = "smb://" + cfg.getHost() + "/" + cfg.getShareName() + "/" +
                (cfg.getRemoteDirectory() != null ? cfg.getRemoteDirectory() + "/" : "");
        SmbFile dir = new SmbFile(smbPath, context);

        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("NAS SMB directory does not exist: {}", smbPath);
            return;
        }

        SmbFile[] files = dir.listFiles();
        if (files == null || files.length == 0) return;

        List<SmbFile> fileList = Arrays.asList(files);
        if ("OLDEST".equalsIgnoreCase(cfg.getFileSortOrder())) {
            fileList.sort(Comparator.comparingLong(f -> {
                try { return f.getLastModified(); } catch (Exception e) { return 0; }
            }));
        } else if ("NEWEST".equalsIgnoreCase(cfg.getFileSortOrder())) {
            fileList.sort((a, b) -> {
                try {
                    return Long.compare(b.getLastModified(), a.getLastModified());
                } catch (Exception e) { return 0; }
            });
        }

        int processed = 0;
        int maxFilesPerPoll = cfg.getMaxFilesPerPoll() != null ? cfg.getMaxFilesPerPoll() : 100;
        for (SmbFile file : fileList) {
            if (!file.isFile() || !filePattern.matcher(file.getName()).matches()) continue;
            if (processed >= maxFilesPerPoll) break;
            if (cfg.getIgnoreHiddenFiles() != null && cfg.getIgnoreHiddenFiles() && file.isHidden()) continue;
            if (cfg.getFileAgeMs() != null) {
                long fileAge = System.currentTimeMillis() - file.getLastModified();
                if (fileAge < cfg.getFileAgeMs()) continue;
            }

            boolean locked = nasFileLockManager.acquireLock(
                    def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName(), getNodeId(), 600
            );
            if (!locked) continue;

            try (InputStream is = file.getInputStream()) {
                byte[] content = CompressionUtil.decompress(is.readAllBytes());

                // ==== Throttling: Only process if token is available ====
                ThrottleSettings settings = getThrottleSettings(def, ref);
                boolean allowed = throttlingService.tryConsumeToken(
                        serviceManagerName,
                        def.getHipIntegrationName(),
                        def.getVersion(),
                        ref.getPropertyRef(),
                        settings
                );
                if (!allowed) {
                    nasFileLockManager.releaseLock(def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName());
                    logger.info("Throttling: Not processing SMB file {} this interval, will retry in next poll", file.getName());
                    break;
                }

                Message<?> msg = buildMessage(def, ref, file.getName(), content, cfg, file);

                // Inject OTel context if missing
                Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(msg);

                // Wiretap: started event as soon as message enters HIP
                wiretapService.tap(
                        msgWithTrace,
                        def,
                        ref,
                        "started",
                        "HIPIntegration received file from NAS (SMB): " + file.getName()
                );

                processInboundMessage(def, ref, msgWithTrace, getInputChannel(def));
                handlePostProcessSmb(file, cfg);

                processed++;
            } catch (Exception ex) {
                logger.error("Failed to process SMB file {}", file.getName(), ex);
                wiretapService.tap(null, def, ref, "error", "Failed to process SMB file: " + file.getName());
            } finally {
                nasFileLockManager.releaseLock(def.getHipIntegrationName(), def.getVersion(), ref.getPropertyRef(), file.getName());
            }
        }
    }

    private Message<?> buildMessage(
            HIPIntegrationDefinition def, AdapterConfigRef ref, String fileName, byte[] fileContent,
            DynamicNASAdapterConfig cfg, Object fileObj
    ) {
        MessageBuilder<byte[]> mb = MessageBuilder.withPayload(fileContent);

        Map<String, Object> nasHeaders = new HashMap<>();
        nasHeaders.put("file_name", fileName);
        nasHeaders.put("charset", cfg.getCharset() != null ? cfg.getCharset() : "UTF-8");
        nasHeaders.put("protocol", cfg.getProtocol());
        nasHeaders.put("adapterRef", ref.getPropertyRef());

        if (fileObj instanceof File file) {
            nasHeaders.put("file_size", file.length());
            nasHeaders.put("file_mod_time", file.lastModified());
            nasHeaders.put("file_path", file.getAbsolutePath());
            nasHeaders.put("file_is_dir", file.isDirectory());
            try {
                BasicFileAttributes attrs = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                nasHeaders.put("file_creation_time", attrs.creationTime().toMillis());
            } catch (IOException ignored) {}
        }
        if (fileObj instanceof SmbFile smbFile) {
            try {
                nasHeaders.put("file_size", smbFile.length());
                nasHeaders.put("file_mod_time", smbFile.getLastModified());
                nasHeaders.put("file_path", smbFile.getPath());
                nasHeaders.put("file_is_dir", smbFile.isDirectory());
            } catch (Exception ignored) {}
        }

        mb.setHeader("hip.adapter.nas", nasHeaders);
        return mb.build();
    }

    private void handlePostProcess(File file, DynamicNASAdapterConfig cfg) {
        try {
            if ("delete".equalsIgnoreCase(cfg.getPostProcessAction())) {
                file.delete();
            } else if ("rename".equalsIgnoreCase(cfg.getPostProcessAction())) {
                String newFileName = (cfg.getRenamePattern() != null)
                        ? cfg.getRenamePattern().replace("{file}", file.getName())
                        : file.getName() + ".processed";
                File newFile = new File(file.getParent(), newFileName);
                file.renameTo(newFile);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for file {}: {}", file.getName(), ex.getMessage());
        }
    }

    private void handlePostProcessSmb(SmbFile file, DynamicNASAdapterConfig cfg) {
        try {
            if ("delete".equalsIgnoreCase(cfg.getPostProcessAction())) {
                file.delete();
            } else if ("rename".equalsIgnoreCase(cfg.getPostProcessAction())) {
                String newFileName = (cfg.getRenamePattern() != null)
                        ? cfg.getRenamePattern().replace("{file}", file.getName())
                        : file.getName() + ".processed";
                String parentUrl = file.getParent();
                SmbFile parent = new SmbFile(parentUrl, file.getContext());
                SmbFile newFile = new SmbFile(parent, newFileName);
                file.renameTo(newFile);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for SMB file {}: {}", file.getName(), ex.getMessage());
        }
    }

    @Override
    protected Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        if (raw instanceof Message<?> msg) return msg;
        if (raw instanceof byte[] data) {
            return MessageBuilder.withPayload(data).build();
        }
        return MessageBuilder.withPayload(String.valueOf(raw).getBytes(StandardCharsets.UTF_8)).build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof NASAdapterInstance nasInstance) {
            if (nasInstance.pollerTask != null && !nasInstance.pollerTask.isDone()) {
                nasInstance.pollerTask.cancel(true);
            }
            logger.info("NAS polling stopped for {}", nasInstance.adapterKey);
            pausedMap.remove(nasInstance.adapterKey);
            pollingTasks.remove(nasInstance.adapterKey);
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        // No-op: polling starts on buildProducer; can be enhanced if needed
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        shutdownAdapterInstance(null, null, instance);
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        String adapterKey = key(def, ref);
        pausedMap.computeIfAbsent(adapterKey, k -> new AtomicBoolean(false)).set(true);
        if (clusterCoordinationService != null) clusterCoordinationService.pause(def, ref);
        if (instance instanceof NASAdapterInstance nasInstance) {
            if (nasInstance.pollerTask != null && !nasInstance.pollerTask.isDone()) {
                nasInstance.pollerTask.cancel(true);
            }
            logger.info("NAS polling paused for {}", adapterKey);
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        String adapterKey = key(def, ref);
        pausedMap.computeIfAbsent(adapterKey, k -> new AtomicBoolean(false)).set(false);

        DynamicNASAdapterConfig cfg = (DynamicNASAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());

        Runnable poller = () -> pollLoop(def, ref, cfg, adapterKey);
        Future<?> future = taskExecutor.submit(poller);
        registerAdapterInstance(def, ref, new NASAdapterInstance(adapterKey, future));
        pollingTasks.put(adapterKey, future);

        if (clusterCoordinationService != null) clusterCoordinationService.resume(def, ref);

        logger.info("NAS polling resumed for {}", adapterKey);
    }

    public static class NASAdapterInstance extends AdapterInstance {
        final String adapterKey;
        final Future<?> pollerTask;

        public NASAdapterInstance(String adapterKey, Future<?> pollerTask) {
            this.adapterKey = adapterKey;
            this.pollerTask = pollerTask;
        }
    }
}
