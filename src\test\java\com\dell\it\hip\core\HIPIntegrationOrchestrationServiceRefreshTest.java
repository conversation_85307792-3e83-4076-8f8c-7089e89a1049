package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.Tag;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.util.logging.WiretapService;
import org.springframework.core.task.TaskExecutor;
import org.springframework.messaging.MessageChannel;

/**
 * Unit tests for HIPIntegrationOrchestrationService refresh functionality.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationOrchestrationServiceRefreshTest {

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Mock
    private HIPIntegrationMapper hipIntegrationMapper;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @Mock
    private HIPClusterCoordinationService clusterCoordinationService;

    @Mock
    private WiretapService wiretapService;

    @Mock
    private InputAdapterStrategy inputAdapterStrategy;

    @Mock
    private HandlerStrategy handlerStrategy;

    @Mock
    private TaskExecutor taskExecutor;

    @Mock
    private MessageChannel deadLetterChannel;

    @InjectMocks
    private HIPIntegrationOrchestrationService orchestrationService;

    private static final String SERVICE_MANAGER_NAME = "test-service-manager";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(orchestrationService, "serviceManagerName", SERVICE_MANAGER_NAME);
    }

    @Test
    void testRefreshHIPIntegration_SuccessfulRefresh() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        HIPIntegrationRequestEntity existingEntity = createTestEntity(integrationName, version);
        HIPIntegrationDefinition definition = createTestDefinition(integrationName, version);

        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(existingEntity);
        when(serviceManager.getIntegrationDefinition(integrationName, version)).thenReturn(definition);
        when(hipIntegrationMapper.saveJsonFields(any())).thenReturn("{}");
        doNothing().when(hipIntegrationRegistry).save(any(HIPIntegrationRequestEntity.class));

        // Mock the registerInternal method dependencies
        when(hipIntegrationMapper.mapToDefinition(any(HIPIntegrationRequest.class))).thenReturn(definition);
        when(serviceManager.getInputAdapterStrategyMap()).thenReturn(java.util.Map.of("test", inputAdapterStrategy));
        when(serviceManager.getHandlerStrategyMap()).thenReturn(java.util.Map.of("test", handlerStrategy));
        when(serviceManager.getFlowExecutor()).thenReturn(taskExecutor);
        doNothing().when(inputAdapterStrategy).buildProducers(any(HIPIntegrationDefinition.class));
        doNothing().when(inputAdapterStrategy).startAll();
        doNothing().when(serviceManager).registerIntegration(any(HIPIntegrationDefinition.class), any(List.class));
        doNothing().when(clusterCoordinationService).broadcastRegistration(any(HIPIntegrationDefinition.class));
        doNothing().when(wiretapService).tapIntegrationLifecycleEvent(any(HIPIntegrationDefinition.class), anyString(), anyString());

        // Act
        orchestrationService.refreshHIPIntegration(request);

        // Assert
        verify(hipIntegrationRegistry).find(SERVICE_MANAGER_NAME, integrationName, version);
        verify(hipIntegrationRegistry).save(any(HIPIntegrationRequestEntity.class));
        verify(serviceManager).getIntegrationDefinition(integrationName, version);
        verify(serviceManager).unregisterIntegration(integrationName, version);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(SERVICE_MANAGER_NAME, integrationName, version, IntegrationStatus.PAUSED);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(SERVICE_MANAGER_NAME, integrationName, version, IntegrationStatus.RUNNING);
    }

    @Test
    void testRefreshHIPIntegration_IntegrationNotFound_ThrowsException() {
        // Arrange
        String integrationName = "nonexistent-integration";
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(integrationName, version);

        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(null);

        // Act & Assert
        IntegrationNotFoundException exception = assertThrows(IntegrationNotFoundException.class, () -> {
            orchestrationService.refreshHIPIntegration(request);
        });

        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        
        // Verify that no update operations were attempted
        verify(hipIntegrationRegistry, never()).save(any(HIPIntegrationRequestEntity.class));
        verify(serviceManager, never()).unregisterIntegration(anyString(), anyString());
    }

    @Test
    void testRefreshHIPIntegration_ShutdownFailure_ContinuesWithRefresh() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        HIPIntegrationRequestEntity existingEntity = createTestEntity(integrationName, version);
        HIPIntegrationDefinition definition = createTestDefinition(integrationName, version);

        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(existingEntity);
        when(serviceManager.getIntegrationDefinition(integrationName, version)).thenReturn(definition);
        when(serviceManager.getInputAdapterStrategyMap()).thenReturn(java.util.Map.of("test", inputAdapterStrategy));
        lenient().when(hipIntegrationMapper.saveJsonFields(any())).thenReturn("{}");
        when(hipIntegrationMapper.mapToDefinition(any(HIPIntegrationRequest.class))).thenReturn(definition);
        when(serviceManager.getFlowExecutor()).thenReturn(taskExecutor);
        doNothing().when(inputAdapterStrategy).buildProducers(any(HIPIntegrationDefinition.class));
        doNothing().when(inputAdapterStrategy).startAll();
        doNothing().when(serviceManager).registerIntegration(any(HIPIntegrationDefinition.class), any(List.class));
        doNothing().when(clusterCoordinationService).broadcastRegistration(any(HIPIntegrationDefinition.class));
        doNothing().when(wiretapService).tapIntegrationLifecycleEvent(any(HIPIntegrationDefinition.class), anyString(), anyString());

        // Simulate shutdown failure
        doThrow(new RuntimeException("Shutdown failed")).when(inputAdapterStrategy).shutdown(any(HIPIntegrationDefinition.class));

        // Act - Should not throw exception despite shutdown failure
        assertDoesNotThrow(() -> {
            orchestrationService.refreshHIPIntegration(request);
        });

        // Assert - Refresh should continue despite shutdown failure
        verify(hipIntegrationRegistry).save(any(HIPIntegrationRequestEntity.class));
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(SERVICE_MANAGER_NAME, integrationName, version, IntegrationStatus.RUNNING);
    }

    @Test
    void testRefreshHIPIntegration_DatabaseSaveFailure_ThrowsOperationException() {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        HIPIntegrationRequestEntity existingEntity = createTestEntity(integrationName, version);
        HIPIntegrationDefinition definition = createTestDefinition(integrationName, version);

        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(existingEntity);
        when(serviceManager.getIntegrationDefinition(integrationName, version)).thenReturn(definition);
        when(hipIntegrationMapper.saveJsonFields(any())).thenReturn("{}");
        
        // Simulate database save failure
        doThrow(new RuntimeException("Database connection failed")).when(hipIntegrationRegistry).save(any(HIPIntegrationRequestEntity.class));

        // Act & Assert
        IntegrationOperationException exception = assertThrows(IntegrationOperationException.class, () -> {
            orchestrationService.refreshHIPIntegration(request);
        });

        assertTrue(exception.getMessage().contains("refresh"));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertEquals("refresh", exception.getOperation());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
    }

    @Test
    void testUpdateEntityFromRequest_UpdatesAllMutableFields() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        
        HIPIntegrationRequestEntity existingEntity = createTestEntity(integrationName, version);
        LocalDateTime originalCreatedAt = existingEntity.getCreatedAt();
        
        HIPIntegrationRequest request = createCompleteTestRequest(integrationName, version);
        
        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(existingEntity);
        when(hipIntegrationMapper.saveJsonFields(any())).thenReturn("{\"updated\":true}");
        when(serviceManager.getIntegrationDefinition(integrationName, version)).thenReturn(createTestDefinition(integrationName, version));
        when(hipIntegrationMapper.mapToDefinition(any(HIPIntegrationRequest.class))).thenReturn(createTestDefinition(integrationName, version));
        when(serviceManager.getInputAdapterStrategyMap()).thenReturn(java.util.Map.of("test", inputAdapterStrategy));
        when(serviceManager.getHandlerStrategyMap()).thenReturn(java.util.Map.of("test", handlerStrategy));
        when(serviceManager.getFlowExecutor()).thenReturn(taskExecutor);
        doNothing().when(inputAdapterStrategy).buildProducers(any(HIPIntegrationDefinition.class));
        doNothing().when(inputAdapterStrategy).startAll();
        doNothing().when(serviceManager).registerIntegration(any(HIPIntegrationDefinition.class), any(List.class));
        doNothing().when(clusterCoordinationService).broadcastRegistration(any(HIPIntegrationDefinition.class));
        doNothing().when(wiretapService).tapIntegrationLifecycleEvent(any(HIPIntegrationDefinition.class), anyString(), anyString());

        // Act
        orchestrationService.refreshHIPIntegration(request);

        // Assert
        ArgumentCaptor<HIPIntegrationRequestEntity> entityCaptor = ArgumentCaptor.forClass(HIPIntegrationRequestEntity.class);
        verify(hipIntegrationRegistry).save(entityCaptor.capture());

        HIPIntegrationRequestEntity savedEntity = entityCaptor.getValue();
        
        // Verify mutable fields were updated
        assertEquals("updated-business-flow", savedEntity.getBusinessFlowName());
        assertEquals("updated-flow-type", savedEntity.getBusinessFlowType());
        assertEquals("updated-flow-version", savedEntity.getBusinessFlowVersion());
        assertEquals("updated-integration-type", savedEntity.getHipIntegrationType());
        
        // Verify immutable fields were preserved
        assertEquals(integrationName, savedEntity.getHipIntegrationName());
        assertEquals(SERVICE_MANAGER_NAME, savedEntity.getServiceManagerName());
        assertEquals(originalCreatedAt, savedEntity.getCreatedAt());
        
        // Verify JSON fields were updated
        verify(hipIntegrationMapper, atLeastOnce()).saveJsonFields(any());
    }

    @Test
    void testRefreshHIPIntegration_PreservesImmutableFields() throws Exception {
        // Arrange
        String integrationName = "test-integration";
        String version = "1.0";
        
        HIPIntegrationRequest request = createTestRequest("different-name", "different-version"); // Different values
        request.setHipIntegrationName(integrationName); // Set correct values
        request.setVersion(version);
        
        HIPIntegrationRequestEntity existingEntity = createTestEntity(integrationName, version);
        HIPIntegrationDefinition definition = createTestDefinition(integrationName, version);

        when(hipIntegrationRegistry.find(SERVICE_MANAGER_NAME, integrationName, version)).thenReturn(existingEntity);
        when(serviceManager.getIntegrationDefinition(integrationName, version)).thenReturn(definition);
        when(hipIntegrationMapper.saveJsonFields(any())).thenReturn("{}");
        when(hipIntegrationMapper.mapToDefinition(any(HIPIntegrationRequest.class))).thenReturn(definition);
        when(serviceManager.getInputAdapterStrategyMap()).thenReturn(java.util.Map.of("test", inputAdapterStrategy));
        when(serviceManager.getHandlerStrategyMap()).thenReturn(java.util.Map.of("test", handlerStrategy));
        when(serviceManager.getFlowExecutor()).thenReturn(taskExecutor);
        doNothing().when(inputAdapterStrategy).buildProducers(any(HIPIntegrationDefinition.class));
        doNothing().when(inputAdapterStrategy).startAll();
        doNothing().when(serviceManager).registerIntegration(any(HIPIntegrationDefinition.class), any(List.class));
        doNothing().when(clusterCoordinationService).broadcastRegistration(any(HIPIntegrationDefinition.class));
        doNothing().when(wiretapService).tapIntegrationLifecycleEvent(any(HIPIntegrationDefinition.class), anyString(), anyString());

        // Act
        orchestrationService.refreshHIPIntegration(request);

        // Assert
        ArgumentCaptor<HIPIntegrationRequestEntity> entityCaptor = ArgumentCaptor.forClass(HIPIntegrationRequestEntity.class);
        verify(hipIntegrationRegistry).save(entityCaptor.capture());

        HIPIntegrationRequestEntity savedEntity = entityCaptor.getValue();
        
        // Verify immutable fields were preserved from original entity
        assertEquals(integrationName, savedEntity.getHipIntegrationName());
        assertEquals(SERVICE_MANAGER_NAME, savedEntity.getServiceManagerName());
        // Version should be preserved as BigDecimal
        assertNotNull(savedEntity.getVersion());
    }

    // Helper methods
    private HIPIntegrationRequest createTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName(integrationName);
        request.setVersion(version);
        request.setBusinessFlowName("test-business-flow");
        request.setServiceManagerName(SERVICE_MANAGER_NAME);

        Tag tag = new Tag();
        tag.setKey("environment");
        tag.setValue("test");
        request.setTags(Arrays.asList(tag));

        return request;
    }

    private HIPIntegrationRequest createCompleteTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = createTestRequest(integrationName, version);
        request.setBusinessFlowName("updated-business-flow");
        request.setBusinessFlowType("updated-flow-type");
        request.setBusinessFlowVersion("updated-flow-version");
        request.setHipIntegrationType("updated-integration-type");
        request.setOwner("updated-owner");

        AdapterConfigRef adapter = new AdapterConfigRef();
        adapter.setType("kafka");
        adapter.setId("updated-adapter");
        request.setAdapters(Arrays.asList(adapter));

        return request;
    }

    private HIPIntegrationRequestEntity createTestEntity(String integrationName, String version) {
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();
        entity.setId(1L);
        entity.setServiceManagerName(SERVICE_MANAGER_NAME);
        entity.setHipIntegrationName(integrationName);
        entity.setVersion(version);
        entity.setBusinessFlowName("original-business-flow");
        entity.setCreatedAt(LocalDateTime.now().minusDays(1));
        entity.setUpdatedAt(LocalDateTime.now().minusHours(1));
        return entity;
    }

    private HIPIntegrationDefinition createTestDefinition(String integrationName, String version) {
        HIPIntegrationDefinition definition = new HIPIntegrationDefinition();
        definition.setHipIntegrationName(integrationName);
        definition.setVersion(version);
        definition.setServiceManagerName(SERVICE_MANAGER_NAME);
        definition.setAdapterConfigRefs(Arrays.asList());
        definition.setHandlerConfigRefs(Arrays.asList());
        return definition;
    }
}
