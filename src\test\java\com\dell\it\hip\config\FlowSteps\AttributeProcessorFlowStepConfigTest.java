/*package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class AttributeProcessorFlowStepConfigTest {

    @Test
    public void testDotNotationDeserialization() throws Exception {
        // JSON with dot notation and array indices - the exact format specified
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"Receiver\",\n"
                + "  \"attributeMappings[0].derivedFrom\": \"ELEMENT_IN_PAYLOAD\",\n"
                + "  \"attributeMappings[0].required\": \"true\",\n"
                + "  \"attributeMappings[0].usage\": \"Routing,Mapping,Logging\",\n"
                + "  \"propertyRef\": \"attributeStep1\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getAttributeMappings());
        assertEquals(1, config.getAttributeMappings().size());
        
        // Check the first AttributeMapping
        AttributeMapping mapping = config.getAttributeMappings().get(0);
        assertNotNull(mapping);
        assertEquals("Receiver", mapping.getAttributeName());
        assertEquals("ELEMENT_IN_PAYLOAD", mapping.getDerivedFrom());
        assertTrue(mapping.isRequired());
        
        // Check usage list parsing
        assertNotNull(mapping.getUsage());
        assertEquals(3, mapping.getUsage().size());
        assertEquals("Routing", mapping.getUsage().get(0));
        assertEquals("Mapping", mapping.getUsage().get(1));
        assertEquals("Logging", mapping.getUsage().get(2));
        
        // Check other properties
        assertEquals("attributeStep1", config.getPropertyRef());
        
        System.out.println("✅ Successfully deserialized dot notation JSON!");
        System.out.println("Attribute mapping: " + mapping);
        System.out.println("Usage list: " + mapping.getUsage());
    }

    @Test
    public void testMultipleAttributeMappings() throws Exception {
        // JSON with multiple attributeMappings entries
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"Receiver\",\n"
                + "  \"attributeMappings[0].derivedFrom\": \"ELEMENT_IN_PAYLOAD\",\n"
                + "  \"attributeMappings[0].required\": \"true\",\n"
                + "  \"attributeMappings[0].usage\": \"Routing,Mapping\",\n"
                + "  \"attributeMappings[1].attributeName\": \"Sender\",\n"
                + "  \"attributeMappings[1].derivedFrom\": \"HEADER\",\n"
                + "  \"attributeMappings[1].required\": \"false\",\n"
                + "  \"attributeMappings[1].expression\": \"$.sender.id\",\n"
                + "  \"attributeMappings[1].usage\": \"Logging\",\n"
                + "  \"attributeMappings[2].attributeName\": \"DocumentType\",\n"
                + "  \"attributeMappings[2].derivedFrom\": \"PAYLOAD\",\n"
                + "  \"attributeMappings[2].required\": \"true\",\n"
                + "  \"attributeMappings[2].expression\": \"$.type\",\n"
                + "  \"propertyRef\": \"multiAttributeStep\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getAttributeMappings());
        assertEquals(3, config.getAttributeMappings().size());
        
        // Check first mapping
        AttributeMapping mapping0 = config.getAttributeMappings().get(0);
        assertEquals("Receiver", mapping0.getAttributeName());
        assertEquals("ELEMENT_IN_PAYLOAD", mapping0.getDerivedFrom());
        assertTrue(mapping0.isRequired());
        assertEquals(2, mapping0.getUsage().size());
        assertEquals("Routing", mapping0.getUsage().get(0));
        assertEquals("Mapping", mapping0.getUsage().get(1));
        
        // Check second mapping
        AttributeMapping mapping1 = config.getAttributeMappings().get(1);
        assertEquals("Sender", mapping1.getAttributeName());
        assertEquals("HEADER", mapping1.getDerivedFrom());
        assertFalse(mapping1.isRequired());
        assertEquals("$.sender.id", mapping1.getExpression());
        assertEquals(1, mapping1.getUsage().size());
        assertEquals("Logging", mapping1.getUsage().get(0));
        
        // Check third mapping
        AttributeMapping mapping2 = config.getAttributeMappings().get(2);
        assertEquals("DocumentType", mapping2.getAttributeName());
        assertEquals("PAYLOAD", mapping2.getDerivedFrom());
        assertTrue(mapping2.isRequired());
        assertEquals("$.type", mapping2.getExpression());
        assertNull(mapping2.getUsage()); // No usage specified
        
        assertEquals("multiAttributeStep", config.getPropertyRef());
        
        System.out.println("✅ Successfully deserialized multiple attributeMappings!");
        System.out.println("Total mappings: " + config.getAttributeMappings().size());
    }

    @Test
    public void testTypeConversions() throws Exception {
        // JSON testing various type conversions
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"TestAttribute\",\n"
                + "  \"attributeMappings[0].derivedFrom\": \"PAYLOAD\",\n"
                + "  \"attributeMappings[0].required\": \"false\",\n"
                + "  \"attributeMappings[0].expression\": \"$.test.path\",\n"
                + "  \"attributeMappings[0].usage\": \"Single\",\n"
                + "  \"attributeMappings[1].attributeName\": \"AnotherAttribute\",\n"
                + "  \"attributeMappings[1].derivedFrom\": \"HEADER\",\n"
                + "  \"attributeMappings[1].required\": \"true\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify type conversions
        assertNotNull(config);
        assertEquals(2, config.getAttributeMappings().size());
        
        // Check boolean conversion
        AttributeMapping mapping0 = config.getAttributeMappings().get(0);
        assertFalse(mapping0.isRequired()); // "false" string -> boolean false
        
        AttributeMapping mapping1 = config.getAttributeMappings().get(1);
        assertTrue(mapping1.isRequired()); // "true" string -> boolean true
        
        // Check single usage item
        assertEquals(1, mapping0.getUsage().size());
        assertEquals("Single", mapping0.getUsage().get(0));
        
        // Check null usage when not provided
        assertNull(mapping1.getUsage());
        
        System.out.println("✅ Type conversions work correctly!");
    }

    @Test
    public void testMissingProperties() throws Exception {
        // JSON with minimal properties
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"MinimalAttribute\",\n"
                + "  \"propertyRef\": \"minimalStep\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getAttributeMappings());
        assertEquals(1, config.getAttributeMappings().size());
        
        AttributeMapping mapping = config.getAttributeMappings().get(0);
        assertEquals("MinimalAttribute", mapping.getAttributeName());
        assertNull(mapping.getDerivedFrom()); // Not provided
        assertNull(mapping.getExpression()); // Not provided
        assertFalse(mapping.isRequired()); // Default boolean value
        assertNull(mapping.getUsage()); // Not provided
        
        assertEquals("minimalStep", config.getPropertyRef());
        
        System.out.println("✅ Missing properties handled gracefully!");
    }

    @Test
    public void testEmptyUsageString() throws Exception {
        // JSON with empty usage string
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"TestAttribute\",\n"
                + "  \"attributeMappings[0].usage\": \"\",\n"
                + "  \"propertyRef\": \"emptyUsageStep\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify empty usage is handled
        assertNotNull(config);
        assertEquals(1, config.getAttributeMappings().size());
        
        AttributeMapping mapping = config.getAttributeMappings().get(0);
        assertNull(mapping.getUsage()); // Empty string should result in null
        
        System.out.println("✅ Empty usage string handled correctly!");
    }

    @Test
    public void testNoAttributeMappings() throws Exception {
        // JSON without any attributeMappings
        String json = "{\n"
                + "  \"propertyRef\": \"noMappingsStep\",\n"
                + "  \"type\": \"attributeProcessor\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNull(config.getAttributeMappings()); // Should be null when no mappings provided
        assertEquals("noMappingsStep", config.getPropertyRef());
        assertEquals("attributeProcessor", config.getType());
        
        System.out.println("✅ No attributeMappings handled correctly!");
    }

    @Test
    public void testComplexUsageList() throws Exception {
        // JSON with complex usage list including spaces and special characters
        String json = "{\n"
                + "  \"attributeMappings[0].attributeName\": \"ComplexAttribute\",\n"
                + "  \"attributeMappings[0].usage\": \"Routing, Mapping , Logging,Validation\",\n"
                + "  \"propertyRef\": \"complexUsageStep\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        AttributeProcessorFlowStepConfig config = objectMapper.readValue(json, AttributeProcessorFlowStepConfig.class);
        
        // Verify usage parsing with spaces
        assertNotNull(config);
        assertEquals(1, config.getAttributeMappings().size());
        
        AttributeMapping mapping = config.getAttributeMappings().get(0);
        assertNotNull(mapping.getUsage());
        assertEquals(4, mapping.getUsage().size());
        assertEquals("Routing", mapping.getUsage().get(0));
        assertEquals("Mapping", mapping.getUsage().get(1)); // Space trimmed
        assertEquals("Logging", mapping.getUsage().get(2));
        assertEquals("Validation", mapping.getUsage().get(3));
        
        System.out.println("✅ Complex usage list parsed correctly!");
        System.out.println("Usage: " + mapping.getUsage());
    }
}*/
