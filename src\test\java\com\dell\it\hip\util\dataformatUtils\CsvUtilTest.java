package com.dell.it.hip.util.dataformatUtils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("CsvUtil Tests")
class CsvUtilTest {

    @Test
    @DisplayName("Should extract field by column index")
    void testExtractField_ByColumnIndex_ReturnsCorrectValue() {
        // Arrange
        String csvLine = "<PERSON>,<PERSON><PERSON>,30,Engineer";
        String expression = "col[2]";
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression);
        
        // Assert
        assertEquals("30", result);
    }

    @Test
    @DisplayName("Should extract field by column name with headers")
    void testExtractField_ByColumnName_ReturnsCorrectValue() {
        // Arrange
        String csvLine = "<PERSON>,<PERSON><PERSON>,30,Engineer";
        String expression = "Age";
        List<String> headers = Arrays.asList("FirstName", "LastName", "Age", "Job");
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression, headers);
        
        // Assert
        assertEquals("30", result);
    }

    @Test
    @DisplayName("Should return null for invalid column index")
    void testExtractField_InvalidColumnIndex_ReturnsNull() {
        // Arrange
        String csvLine = "John,Doe,30";
        String expression = "col[5]";
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should return null for negative column index")
    void testExtractField_NegativeColumnIndex_ReturnsNull() {
        // Arrange
        String csvLine = "John,Doe,30";
        String expression = "col[-1]";
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle case insensitive column names")
    void testExtractField_CaseInsensitiveColumnName_ReturnsCorrectValue() {
        // Arrange
        String csvLine = "John,Doe,30,Engineer";
        String expression = "age";
        List<String> headers = Arrays.asList("FirstName", "LastName", "Age", "Job");
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression, headers);
        
        // Assert
        assertEquals("30", result);
    }

    @Test
    @DisplayName("Should return null for unknown column name")
    void testExtractField_UnknownColumnName_ReturnsNull() {
        // Arrange
        String csvLine = "John,Doe,30,Engineer";
        String expression = "Salary";
        List<String> headers = Arrays.asList("FirstName", "LastName", "Age", "Job");
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression, headers);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle null inputs gracefully")
    void testExtractField_NullInputs_ReturnsNull() {
        // Act & Assert
        assertNull(CsvUtil.extractField(null, "col[0]"));
        assertNull(CsvUtil.extractField("John,Doe", null));
        assertNull(CsvUtil.extractField(null, null));
    }

    @Test
    @DisplayName("Should handle empty CSV line")
    void testExtractField_EmptyCSVLine_ReturnsEmptyString() {
        // Arrange
        String csvLine = "";
        String expression = "col[0]";

        // Act
        String result = CsvUtil.extractField(csvLine, expression);

        // Assert
        assertEquals("", result);
    }

    @Test
    @DisplayName("Should handle CSV with trailing empty columns")
    void testExtractField_TrailingEmptyColumns_ReturnsEmptyString() {
        // Arrange
        String csvLine = "John,Doe,,";
        String expression = "col[2]";
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression);
        
        // Assert
        assertEquals("", result);
    }

    @Test
    @DisplayName("Should split CSV into lines")
    void testSplitLines_ValidCSV_ReturnsCorrectLines() {
        // Arrange
        String csv = "Name,Age,Job\nJohn,30,Engineer\nJane,25,Designer";
        
        // Act
        List<String> result = CsvUtil.splitLines(csv);
        
        // Assert
        assertEquals(3, result.size());
        assertEquals("Name,Age,Job", result.get(0));
        assertEquals("John,30,Engineer", result.get(1));
        assertEquals("Jane,25,Designer", result.get(2));
    }

    @Test
    @DisplayName("Should handle empty CSV for splitLines")
    void testSplitLines_EmptyCSV_ReturnsEmptyList() {
        // Act
        List<String> result = CsvUtil.splitLines("");
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle null CSV for splitLines")
    void testSplitLines_NullCSV_ReturnsEmptyList() {
        // Act
        List<String> result = CsvUtil.splitLines(null);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should split CSV with header into rows")
    void testSplitLinesWithHeader_ValidCSV_ReturnsCorrectRows() {
        // Arrange
        String csv = "Name,Age,Job\nJohn,30,Engineer\nJane,25,Designer";
        
        // Act
        List<String[]> result = CsvUtil.splitLinesWithHeader(csv);
        
        // Assert
        assertEquals(3, result.size());
        assertArrayEquals(new String[]{"Name", "Age", "Job"}, result.get(0));
        assertArrayEquals(new String[]{"John", "30", "Engineer"}, result.get(1));
        assertArrayEquals(new String[]{"Jane", "25", "Designer"}, result.get(2));
    }

    @Test
    @DisplayName("Should handle CSV with quoted values")
    void testSplitLinesWithHeader_QuotedValues_HandlesCorrectly() {
        // Arrange
        String csv = "Name,Description\n\"John Doe\",\"Software Engineer, Senior\"";
        
        // Act
        List<String[]> result = CsvUtil.splitLinesWithHeader(csv);
        
        // Assert
        assertEquals(2, result.size());
        assertArrayEquals(new String[]{"Name", "Description"}, result.get(0));
        // Note: The current implementation uses a naive CSV split, so this tests the actual behavior
        assertEquals(2, result.get(1).length);
    }

    @Test
    @DisplayName("Should handle empty CSV for splitLinesWithHeader")
    void testSplitLinesWithHeader_EmptyCSV_ReturnsEmptyList() {
        // Act
        List<String[]> result = CsvUtil.splitLinesWithHeader("");
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle null CSV for splitLinesWithHeader")
    void testSplitLinesWithHeader_NullCSV_ReturnsEmptyList() {
        // Act
        List<String[]> result = CsvUtil.splitLinesWithHeader(null);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle single line CSV")
    void testSplitLines_SingleLine_ReturnsOneElement() {
        // Arrange
        String csv = "Name,Age,Job";
        
        // Act
        List<String> result = CsvUtil.splitLines(csv);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals("Name,Age,Job", result.get(0));
    }

    @Test
    @DisplayName("Should handle CSV with empty lines")
    void testSplitLines_WithEmptyLines_IncludesEmptyLines() {
        // Arrange
        String csv = "Name,Age\n\nJohn,30";
        
        // Act
        List<String> result = CsvUtil.splitLines(csv);
        
        // Assert
        assertEquals(3, result.size());
        assertEquals("Name,Age", result.get(0));
        assertEquals("", result.get(1));
        assertEquals("John,30", result.get(2));
    }

    @Test
    @DisplayName("Should extract field with convenience method")
    void testExtractField_ConvenienceMethod_WorksCorrectly() {
        // Arrange
        String csvLine = "John,Doe,30,Engineer";
        String expression = "col[1]";
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression);
        
        // Assert
        assertEquals("Doe", result);
    }

    @Test
    @DisplayName("Should handle malformed column index expression")
    void testExtractField_MalformedExpression_ThrowsException() {
        // Arrange
        String csvLine = "John,Doe,30";
        String expression = "col[abc]";
        
        // Act & Assert
        assertThrows(NumberFormatException.class, () -> {
            CsvUtil.extractField(csvLine, expression);
        });
    }

    @Test
    @DisplayName("Should handle empty headers list")
    void testExtractField_EmptyHeaders_ReturnsNull() {
        // Arrange
        String csvLine = "John,Doe,30,Engineer";
        String expression = "Age";
        List<String> headers = Collections.emptyList();
        
        // Act
        String result = CsvUtil.extractField(csvLine, expression, headers);
        
        // Assert
        assertNull(result);
    }
}
