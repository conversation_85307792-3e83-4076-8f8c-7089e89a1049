package com.dell.it.hip.strategy.flows;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.EnricherFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.TargetFileNameConfig;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;
@Component("enricher")
public class EnricherFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired private WiretapService wiretapService;

    @Override
    public String getType() {
        return "enricher";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) {
        EnricherFlowStepConfig config = (EnricherFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());
        if (config == null) {
            String msg = "No Enricher config for: " + stepConfigRef.getPropertyRef();
            wiretapService.tap(message, def, stepConfigRef, "error", msg);
            TransactionLoggingUtil.logError(message, def, stepConfigRef, "EnricherConfigNotFound", msg);
            return Collections.emptyList();
        }

        // 1. Data format and docType detection
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null) {
            dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));
        }
        String targetDocType = (String) message.getHeaders().get("HIP.target.doctype");
        if (targetDocType == null) {
            targetDocType = (String) message.getHeaders().get("HIP.documentType");
        }

        // 2. Lookup config for docType or default
        EnricherFlowStepConfig.DocTypeEnricherConfig dtConfig = findMatchingDocTypeConfig(config, targetDocType, dataFormat);
        EnricherFlowStepConfig.DefaultEnricherConfig defaultCfg = config.getDefaultConfig();
        EnricherFlowStepConfig.EnricherBehavior behavior = dtConfig != null
                ? dtConfig.getBehavior()
                : (defaultCfg != null ? defaultCfg.getBehavior() : EnricherFlowStepConfig.EnricherBehavior.ENRICH);

        // 3. Execute behavior
        switch (behavior) {
            case SKIP -> {
                wiretapService.tap(message, def, stepConfigRef, "info", "Enricher: SKIP for docType " + targetDocType);
                TransactionLoggingUtil.logInfo(message, def, stepConfigRef, "EnricherSkipped: Enrichment skipped");
                return Collections.singletonList(message);
            }
            case TERMINATE -> {
                wiretapService.tap(message, def, stepConfigRef, "terminated", "Enricher: TERMINATE for docType " + targetDocType);
                TransactionLoggingUtil.logError(message, def, stepConfigRef, "EnricherTerminated", "Enrichment terminated for docType " + targetDocType);
                return Collections.emptyList();
            }
            case ENRICH -> {
                Map<String, Object> headersToSet = new HashMap<>();
                // HIP.target.doctype
                if (!message.getHeaders().containsKey("HIP.target.doctype") && targetDocType != null) {
                    headersToSet.put("HIP.target.doctype", targetDocType);
                }

                // Target file name (if present in config)
                boolean isTargetFileNameSetting =
                        (dtConfig != null && dtConfig.isTargetFileNameSetting()) ||
                                (dtConfig == null && defaultCfg != null && defaultCfg.isTargetFileNameSetting());
                TargetFileNameConfig fileNameConfig =
                        dtConfig != null ? dtConfig.getTargetFileNameConfig() : (defaultCfg != null ? defaultCfg.getTargetFileNameConfig() : null);
                if (isTargetFileNameSetting && fileNameConfig != null) {
                    String filename = getFileName(fileNameConfig, message);
                    headersToSet.put("HIP.output.FileName", filename);
                }

                // Any custom header enrichment (e.g., AS2-SenderID, AS2-ReceiverID) directly from config
                Map<String, Object> enrichHeaders =
                        dtConfig != null ? dtConfig.getEnrichmentHeaders() : (defaultCfg != null ? defaultCfg.getEnrichmentHeaders() : null);
                if (enrichHeaders != null) {
                    headersToSet.putAll(enrichHeaders);
                }

                Message<?> enriched = MessageBuilder.fromMessage(message)
                        .copyHeaders(headersToSet)
                        .build();
                wiretapService.tap(enriched, def, stepConfigRef, "info", "Enricher: Added headers " + headersToSet.keySet());
                TransactionLoggingUtil.logInfo(enriched, def, stepConfigRef, "EnricherSuccess: Headers enriched: " + headersToSet.keySet());
                return Collections.singletonList(enriched);
            }
            default -> {
                String msg = "Enricher: Unknown behavior, skipping";
                wiretapService.tap(message, def, stepConfigRef, "warn", msg);
                TransactionLoggingUtil.logError(message, def, stepConfigRef, "EnricherUnknownBehavior", msg);
                return Collections.singletonList(message);
            }
        }
    }

    // === TargetFileName logic (from TargetFileNameFlowStepStrategy) ===
    private String getFileName(TargetFileNameConfig fileNameConfig, Message<?> message) {
        StringBuilder fileNameValue = new StringBuilder();
        if (fileNameConfig != null && fileNameConfig.getFileNameSection() != null) {
            int i = fileNameConfig.getFileNameSection().size() - 1;
            for (TargetFileNameConfig.TargetFileNameAttributes fileName : fileNameConfig.getFileNameSection()) {
                fileNameValue.append(getFileNameValue(fileName.getDerivedFrom(), fileName.getValueText(), message));
                if (i-- != 0) {
                    fileNameValue.append(fileNameConfig.getFileSeparator());
                } else if (fileNameConfig.getFileExtension().contains(".")) {
                    fileNameValue.append(fileNameConfig.getFileExtension());
                } else {
                    fileNameValue.append("." + fileNameConfig.getFileExtension());
                }
            }
        }
        return fileNameValue.toString();
    }

    private String getFileNameValue(String derivedFrom, String valueText, Message<?> message) {
        String value = "";
        switch (derivedFrom) {
            case "FixedText":
                value = valueText;
                break;
            case "DateAndTime":
                value = new SimpleDateFormat(valueText).format(Calendar.getInstance().getTime());
                break;
            case "RandomNumber4digit":
                value = getRandomNumber(derivedFrom);
                break;
            case "RandomNumber6digit":
                value = getRandomNumber(derivedFrom);
                break;
            case "FileNameAttribute":
                value = String.valueOf(message.getHeaders().get(valueText));
                break;
            default:
        }
        return value;
    }

    private String getRandomNumber(String digit) {
        if ("RandomNumber4digit".equalsIgnoreCase(digit)) {
            Random rnd = new Random();
            int number = rnd.nextInt(9999);
            return String.format("%04d", number);
        } else if ("RandomNumber6digit".equalsIgnoreCase(digit)) {
            Random rnd = new Random();
            int number = rnd.nextInt(999999);
            return String.format("%06d", number);
        }
        return "";
    }

    private EnricherFlowStepConfig.DocTypeEnricherConfig findMatchingDocTypeConfig(
            EnricherFlowStepConfig config, String docType, String dataFormat) {
        if (config.getDocTypeConfigs() == null || docType == null) return null;
        for (EnricherFlowStepConfig.DocTypeEnricherConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docType)
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }
}
