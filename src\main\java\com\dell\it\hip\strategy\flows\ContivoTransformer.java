package com.dell.it.hip.strategy.flows;

import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;

public interface ContivoTransformer {
    String transform(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def, ContivoMapConfig contivoMap) throws Exception;
}