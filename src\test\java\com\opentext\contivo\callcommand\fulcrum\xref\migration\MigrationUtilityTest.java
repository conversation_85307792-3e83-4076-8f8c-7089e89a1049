package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;

import java.io.*;
import java.nio.file.Path;
import java.sql.*;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify the FileToDatabaseMigrationUtility compilation and basic functionality
 */
public class MigrationUtilityTest {

    private static final String DB_DRIVER = "org.h2.Driver";

    // Generate unique database URL for each test instance to avoid conflicts
    private final String dbUrl = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
    
    // Test data matching the actual file structure
    private static final String TEST_FILE_CONTENT = 
        "\"wo1_CONTIVO\",\"\"\n" +
        "\"A\",\"Apple\"\n" +
        "\"MES-EMFC\",\"EMFC\"\n" +
        "\"MES-AMFF\",\"AMFF\"\n" +
        "\"COMPAL\",\"D\"\n";
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() throws Exception {
        // Setup H2 in-memory database
        setupDatabase();
    }
    
    private void setupDatabase() throws Exception {
        Class.forName(DB_DRIVER);

        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            // Create database schema with proper cleanup
            String[] cleanupStatements = {
                "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_SEQ",
                "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_ROW_SEQ",
                "DROP TABLE IF EXISTS TGTS_LOOKUP_COLUMN",
                "DROP TABLE IF EXISTS TGTS_LOOKUP_ROW",
                "DROP TABLE IF EXISTS TGTS_LOOKUP"
            };

            String[] createStatements = {
                "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
                "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
                
                "CREATE TABLE TGTS_LOOKUP (" +
                "  TABLE_ID BIGINT PRIMARY KEY," +
                "  SOLUTION_ID VARCHAR(255) NOT NULL," +
                "  TABLE_NAME VARCHAR(255) NOT NULL," +
                "  CREATE_DATE TIMESTAMP NOT NULL," +
                "  AVAILABLE_DATE TIMESTAMP" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_ROW (" +
                "  ROW_ID BIGINT PRIMARY KEY," +
                "  FK_TABLE_ID BIGINT NOT NULL," +
                "  ROW_TYPE INT NOT NULL," +
                "  FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_COLUMN (" +
                "  FK_ROW_ID BIGINT NOT NULL," +
                "  COL_NUM INT NOT NULL," +
                "  COL_VAL VARCHAR(4000)," +
                "  PRIMARY KEY (FK_ROW_ID, COL_NUM)," +
                "  FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID)" +
                ")"
            };

            try (Statement stmt = conn.createStatement()) {
                // Execute cleanup statements first (ignore errors for non-existent objects)
                for (String sql : cleanupStatements) {
                    try {
                        stmt.execute(sql);
                    } catch (SQLException e) {
                        // Ignore errors for non-existent objects
                    }
                }

                // Execute create statements
                for (String sql : createStatements) {
                    stmt.execute(sql);
                }
            }
        }
    }
    
    @Test
    void testMigrationUtilityCompilation() {
        // This test verifies that the FileToDatabaseMigrationUtility compiles correctly
        // and can be instantiated without errors
        
        assertDoesNotThrow(() -> {
            FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
                dbUrl, DB_DRIVER, null, null);
            assertNotNull(migrator, "Migration utility should be created successfully");
        }, "FileToDatabaseMigrationUtility should compile and instantiate without errors");
    }
    
    @Test
    void testHeaderMapInversion() throws Exception {
        // Test the specific fix for header map inversion
        
        // Create test file
        Path testFile = tempDir.resolve("test_file.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write(TEST_FILE_CONTENT);
        }
        
        // Create migration utility
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            dbUrl, DB_DRIVER, null, null);
        
        // Perform migration - this should not throw any compilation or runtime errors
        assertDoesNotThrow(() -> {
            FileToDatabaseMigrationUtility.MigrationResult result = migrator.migrateFileToDatabase(
                testFile.toString(), "TEST_SOLUTION", "test_table", true);
            
            assertNotNull(result, "Migration result should not be null");
            assertEquals(4, result.dataRowCount, "Should have 4 data rows");
            assertEquals(1, result.headerRowCount, "Should have 1 header row");
            
        }, "Migration with headers should complete without errors");
    }
    
    @Test
    void testDatabaseStructureAfterMigration() throws Exception {
        // Create test file
        Path testFile = tempDir.resolve("test_file.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write(TEST_FILE_CONTENT);
        }
        
        // Perform migration
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            dbUrl, DB_DRIVER, null, null);

        FileToDatabaseMigrationUtility.MigrationResult result = migrator.migrateFileToDatabase(
            testFile.toString(), "TEST_SOLUTION", "test_table", true);

        // Verify database structure
        try (Connection conn = DriverManager.getConnection(dbUrl)) {
            
            // Check TGTS_LOOKUP table
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT COUNT(*) FROM TGTS_LOOKUP WHERE SOLUTION_ID = ? AND TABLE_NAME = ?")) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    assertTrue(rs.next());
                    assertEquals(1, rs.getInt(1), "Should have 1 table entry");
                }
            }
            
            // Check TGTS_LOOKUP_ROW table
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT ROW_TYPE, COUNT(*) FROM TGTS_LOOKUP_ROW r " +
                    "JOIN TGTS_LOOKUP l ON r.FK_TABLE_ID = l.TABLE_ID " +
                    "WHERE l.SOLUTION_ID = ? AND l.TABLE_NAME = ? " +
                    "GROUP BY ROW_TYPE ORDER BY ROW_TYPE")) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    // Data rows (ROW_TYPE = 0)
                    assertTrue(rs.next());
                    assertEquals(0, rs.getInt(1), "First group should be data rows");
                    assertEquals(4, rs.getInt(2), "Should have 4 data rows");
                    
                    // Header rows (ROW_TYPE = 1)
                    assertTrue(rs.next());
                    assertEquals(1, rs.getInt(1), "Second group should be header rows");
                    assertEquals(1, rs.getInt(2), "Should have 1 header row");
                }
            }
            
            // Check header columns specifically
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT c.COL_NUM, c.COL_VAL FROM TGTS_LOOKUP_COLUMN c " +
                    "JOIN TGTS_LOOKUP_ROW r ON c.FK_ROW_ID = r.ROW_ID " +
                    "JOIN TGTS_LOOKUP l ON r.FK_TABLE_ID = l.TABLE_ID " +
                    "WHERE l.SOLUTION_ID = ? AND l.TABLE_NAME = ? AND r.ROW_TYPE = 1 " +
                    "ORDER BY c.COL_NUM")) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    // Column 1 header
                    assertTrue(rs.next());
                    assertEquals(1, rs.getInt(1), "First column number should be 1");
                    assertEquals("wo1_CONTIVO", rs.getString(2), "First column header should be 'wo1_CONTIVO'");
                    
                    // Column 2 header (empty string)
                    assertTrue(rs.next());
                    assertEquals(2, rs.getInt(1), "Second column number should be 2");
                    assertEquals(1, rs.getString(2).length(), 0, "Second column header should be empty string");
                }
            }
            
            // Check sample data
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT c.COL_NUM, c.COL_VAL FROM TGTS_LOOKUP_COLUMN c " +
                    "JOIN TGTS_LOOKUP_ROW r ON c.FK_ROW_ID = r.ROW_ID " +
                    "JOIN TGTS_LOOKUP l ON r.FK_TABLE_ID = l.TABLE_ID " +
                    "WHERE l.SOLUTION_ID = ? AND l.TABLE_NAME = ? AND r.ROW_TYPE = 0 " +
                    "ORDER BY r.ROW_ID, c.COL_NUM LIMIT 2")) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    // First data row, column 1
                    assertTrue(rs.next());
                    assertEquals(1, rs.getInt(1));
                    assertEquals("A", rs.getString(2));
                    
                    // First data row, column 2
                    assertTrue(rs.next());
                    assertEquals(2, rs.getInt(1));
                    assertEquals("Apple", rs.getString(2));
                }
            }
        }
    }
    
    @Test
    void testMigrationWithoutHeaders() throws Exception {
        // Test migration of file without headers
        String dataOnlyContent = "\"A\",\"Apple\"\n\"MES-EMFC\",\"EMFC\"\n";
        
        Path testFile = tempDir.resolve("data_only_file.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write(dataOnlyContent);
        }
        
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            dbUrl, DB_DRIVER, null, null);
        
        FileToDatabaseMigrationUtility.MigrationResult result = migrator.migrateFileToDatabase(
            testFile.toString(), "TEST_SOLUTION", "data_only_table", false);
        
        assertEquals(2, result.dataRowCount, "Should have 2 data rows");
        assertEquals(0, result.headerRowCount, "Should have 0 header rows");
    }
}
