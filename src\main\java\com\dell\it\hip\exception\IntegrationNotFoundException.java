package com.dell.it.hip.exception;

import com.dell.it.hip.util.security.InputValidationUtil;

/**
 * Exception thrown when a requested integration is not found.
 */
public class IntegrationNotFoundException extends RuntimeException {

    public IntegrationNotFoundException(String message) {
        super(message);
    }

    public IntegrationNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public IntegrationNotFoundException(String integrationName, String version) {
        super(String.format("Integration not found: %s:%s",
            InputValidationUtil.sanitizeForErrorMessage(integrationName),
            InputValidationUtil.sanitizeForErrorMessage(version)));
    }
}
