package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.ValidationFlowStepConfig;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.SchemaValidator;
import com.dell.it.hip.util.validation.StructuralValidator;


@Component("validation")
public class ValidationFlowStepStrategy extends AbstractFlowStepStrategy {

	@Autowired
	private WiretapService wiretapService;
	@Autowired
	private TransactionLoggingUtil transactionLoggingUtil;
	@Autowired
	private SchemaValidator schemaValidator;

	@Override
	public String getType() {
		return "validation";
	}

	@Override
	protected List<Message<?>> doExecute(
			Message<?> message,
			FlowStepConfigRef stepConfigRef,
			HIPIntegrationDefinition def
	) {
		ValidationFlowStepConfig config = (ValidationFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());
		if (config == null)
			throw new IllegalStateException("No Validation config for: " + stepConfigRef.getPropertyRef());

		String payload = Objects.toString(message.getPayload(), "");
		String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
		String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
		String docVersion = (String) message.getHeaders().get("HIP.documentTypeVersion");
		if (dataFormat == null) dataFormat = MessageFormatDetector.detect(payload);

		/*String docName = null, docVersion = null;
		if (docTypeHeader != null && docTypeHeader.contains(":")) {
			String[] parts = docTypeHeader.split(":", 2);
			docName = parts[0];
			docVersion = parts[1];
		}*/

		// Find matching config for docType
		ValidationFlowStepConfig.DocTypeValidationConfig selected = null;
		if (config.getDocTypeConfigs() != null && docTypeHeader != null) {
			for (ValidationFlowStepConfig.DocTypeValidationConfig cfg : config.getDocTypeConfigs()) {
				if (cfg.getName().equalsIgnoreCase(docTypeHeader)
						&& (cfg.getVersion() == null || cfg.getVersion().equalsIgnoreCase(docVersion))
						&& (cfg.getDataFormat() == null || cfg.getDataFormat().equalsIgnoreCase(dataFormat))) {
					selected = cfg;
					break;
				}
			}
		}
		// Fallback to default
		ValidationFlowStepConfig.ValidationBehavior behavior = null;
		String schemaKey = null;
		String schemaType = null;
		if (selected != null) {
			behavior = selected.getValidationBehavior();
			schemaKey = selected.getSchemaKey();
			schemaType = selected.getSchemaType();
		} else if (config.getDefaultConfig() != null) {
			behavior = config.getDefaultConfig().getValidationBehavior();
			schemaKey = config.getDefaultConfig().getSchemaKey();
			schemaType = config.getDefaultConfig().getSchemaType();
		}

		if (behavior == null) {
			String errMsg = "No validation config found for docType=" + docTypeHeader + ", dataFormat=" + dataFormat;
			wiretapService.tap(message, def, stepConfigRef, "terminated", errMsg);
			transactionLoggingUtil.logError(message, def, stepConfigRef, "ValidationConfigNotFound", errMsg);
			return Collections.emptyList();
		}

		switch (behavior) {
			case SKIP -> {
				String msg = "Validation skipped for docType=" + docTypeHeader + ", format=" + dataFormat;
				wiretapService.tap(message, def, stepConfigRef, "info", msg);
				transactionLoggingUtil.logInfo(message, def, stepConfigRef, msg);
				return Collections.singletonList(message);
			}
			case TERMINATE -> {
				String msg = "Validation terminated message for docType=" + docTypeHeader + ", format=" + dataFormat;
				wiretapService.tap(message, def, stepConfigRef, "terminated", msg);
				transactionLoggingUtil.logError(message, def, stepConfigRef, "ValidationTerminated", msg);
				return Collections.emptyList();
			}
			case STRUCTURAL -> {
				boolean structureOk = StructuralValidator.validate(payload, dataFormat);
				if (!structureOk) {
					String msg = "Structural validation failed for format: " + dataFormat;
					wiretapService.tap(message, def, stepConfigRef, "error", msg);
					transactionLoggingUtil.logError(message, def, stepConfigRef, "StructuralValidationFailure", msg);
					return Collections.emptyList();
				}
				Message<?> enriched = MessageBuilder.fromMessage(message)
						.setHeader("HIP.payload.dataformat", dataFormat)
						.setHeader("HIP.docType.validation", "PASS")
						.build();
				String msg = "Structural validation passed for docType=" + docTypeHeader + ", format=" + dataFormat;
				wiretapService.tap(enriched, def, stepConfigRef, "info", msg);
				transactionLoggingUtil.logInfo(enriched, def, stepConfigRef, msg);
				return Collections.singletonList(enriched);
			}
			case SCHEMA -> {
				// 1. First do structural validation
				boolean structuralValid = StructuralValidator.validate(payload, dataFormat);
				if (!structuralValid) {
					String msg = "Structural validation failed for format: " + dataFormat;
					wiretapService.tap(message, def, stepConfigRef, "error", msg);
					transactionLoggingUtil.logError(message, def, stepConfigRef, "StructuralValidationFailure", msg);
					return Collections.emptyList();
				}
				// 2. Then do schema validation (if key provided)
				if (schemaKey == null) {
					String msg = "No schemaKey provided for SCHEMA validation.";
					wiretapService.tap(message, def, stepConfigRef, "error", msg);
					transactionLoggingUtil.logError(message, def, stepConfigRef, "SchemaValidationFailure", msg);
					return Collections.emptyList();
				}
				try {
					boolean valid = schemaValidator.validate(payload, dataFormat, schemaKey);
					if (!valid) {
						String msg = "Schema validation failed for format: " + dataFormat + " using key: " + schemaKey;
						wiretapService.tap(message, def, stepConfigRef, "error", msg);
						transactionLoggingUtil.logError(message, def, stepConfigRef, "SchemaValidationFailure", msg);
						return Collections.emptyList();
					}
				} catch (Exception ex) {
					String msg = "Schema validation error for format: " + dataFormat + " using key: " + schemaKey + ", " + ex.getMessage();
					wiretapService.tap(message, def, stepConfigRef, "error", msg);
					transactionLoggingUtil.logError(message, def, stepConfigRef, "SchemaValidationFailure", msg);
					return Collections.emptyList();
				}
				Message<?> enriched = MessageBuilder.fromMessage(message)
						.setHeader("HIP.payload.dataformat", dataFormat)
						.setHeader("HIP.docType.validation", "PASS")
						.build();
				String msg = "Schema validation passed for docType=" + docTypeHeader + ", format=" + dataFormat;
				wiretapService.tap(enriched, def, stepConfigRef, "info", msg);
				transactionLoggingUtil.logInfo(enriched, def, stepConfigRef, msg);
				return Collections.singletonList(enriched);
			}
			default -> {
				String msg = "Unknown validation behavior. Skipping.";
				wiretapService.tap(message, def, stepConfigRef, "warn", msg);
				transactionLoggingUtil.logInfo(message, def, stepConfigRef, msg);
				return Collections.singletonList(message);
			}
		}
	}
}