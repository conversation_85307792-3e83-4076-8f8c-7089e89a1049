<!DOCTYPE html>
<html lang="en">

<head>
  <meta http-equiv="Content-type" content="text/html; charset=utf-8">
  <meta http-equiv="Content-Language" content="en-us">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>Snyk test report</title>
  <meta name="description" content=" known vulnerabilities found in .">
  <base target="_blank">
  <link rel="icon" type="image/png" href="https://res.cloudinary.com/snyk/image/upload/v1468845142/favicon/favicon.png"
    sizes="194x194">
  <link rel="shortcut icon" href="https://res.cloudinary.com/snyk/image/upload/v1468845142/favicon/favicon.ico">
  <style type="text/css">
  
    body {
      -moz-font-feature-settings: "pnum";
      -webkit-font-feature-settings: "pnum";
      font-variant-numeric: proportional-nums;
      display: flex;
      flex-direction: column;
      font-feature-settings: "pnum";
      font-size: 100%;
      line-height: 1.5;
      min-height: 100vh;
      -webkit-text-size-adjust: 100%;
      margin: 0;
      padding: 0;
      color: #393842;
      font-family: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, roboto, noto, arial, sans-serif;
    }
  
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-weight: 500;
    }
  
    a,
    a:link,
    a:visited {
      border-bottom: 1px solid #4b45a9;
      text-decoration: none;
      color: #4b45a9;
    }
  
    a:hover,
    a:focus,
    a:active {
      border-bottom: 1px solid #4b45a9;
    }
  
    hr {
      border: none;
      margin: 1em 0;
      border-top: 1px solid #c5c5c5;
    }
  
    ul {
      padding: 0 1em;
      margin: 1em 0;
    }
  
    code {
      background-color: #EEE;
      color: #333;
      padding: 0.25em 0.5em;
      border-radius: 0.25em;
    }
  
    pre {
      font-size: 14px;
    }
  
    pre code {
      padding: 10px;
      font-family: monospace;
      background-color: #393842;
      border-radius: 4px;
      color: #fff;
      display: block;
      white-space: pre-wrap;
      overflow-x: auto;
      max-width: 100%;
      min-width: 100px;
    }
  
    a code {
      border-radius: .125rem .125rem 0 0;
      padding-bottom: 0;
      color: #4b45a9;
    }
  
    a[href^="http://"]:after,
    a[href^="https://"]:after:not(.brand) {
      background-image: linear-gradient(transparent,transparent),url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20112%20109%22%3E%3Cg%20id%3D%22Page-1%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20id%3D%22link-external%22%3E%3Cg%20id%3D%22arrow%22%3E%3Cpath%20id%3D%22Line%22%20stroke%3D%22%234B45A9%22%20stroke-width%3D%2215%22%20d%3D%22M88.5%2021l-43%2042.5%22%20stroke-linecap%3D%22square%22%2F%3E%3Cpath%20id%3D%22Triangle%22%20fill%3D%22%234B45A9%22%20d%3D%22M111.2%200v50L61%200z%22%2F%3E%3C%2Fg%3E%3Cpath%20id%3D%22square%22%20fill%3D%22%234B45A9%22%20d%3D%22M66%2015H0v94h94V44L79%2059v35H15V30h36z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
      background-repeat: no-repeat;
      background-size: .75rem;
      content: "";
      display: inline-block;
      height: .75rem;
      margin-left: .25rem;
      width: .75rem;
    }
  
    .suppressed {
      border-left: 5px solid #888;
    }
  
    .suppression-card {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 10px;
      background-color: #fff7e6;
    }
  
    .suppression-card__container {
      display: flex;
      flex-wrap: wrap;
    }
  
    .suppression-card__items {
      flex: 1 1 50%;
      list-style: none;
      padding: 0;
      margin: 0;
    }
  
    .suppression-card__item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
    }
  
    .suppression-card__item__key {
      flex: 0 0 40%;
      color: #a55c09;
      font-weight: 500;
      margin-bottom: 2px;
      flex-shrink: 0;
      padding-right: 32px;
      white-space: nowrap;
      width: 180px;
    }
  
    .suppression-card__item__value {
      flex: 1 1 60%;
      text-align: left;
      color: #a55c09;
      padding-right: 32px;
      word-break: break-all;
    }
  
    .suppression-card__user-initial {
      display: inline-block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #6666cc;
      color: white;
      text-align: center;
      line-height: 24px;
      margin-right: 5px;
      font-weight: bold;
    }
  
    .suppression-card strong {
      color: #b35900;
    }
  
  /* Layout */
  
    [class*=layout-container] {
      margin: 0 auto;
      max-width: 71.25em;
      padding: 20px;
      position: relative;
    }
    .layout-container--short {
      padding-top: 0;
      padding-bottom: 0;
      max-width: 48.75em;
    }
  
    .layout-container--short:after {
      display: block;
      content: "";
      clear: both;
    }
  
  /* Header */
  
    .header {
      padding-bottom: 1px;
    }
  
    .paths {
      margin-left: 8px;
      font-size:14px;
    }
  
    .project__header {
      background-color: #030328;
      color: #fff;
      }
  
    .project__header__title {
      overflow-wrap: break-word;
      word-wrap: break-word;
      word-break: break-all;
      margin-top: 0;
      margin-bottom: 12px;
    }
  
    .meta-count {
      display: inline-block;
      margin: 0 12px 6px 0;
      padding-right: 12px;
      font-size:14px;
    }
  
  /* Card */
  
    .card {
      background-color: #fff;
      border: 1px solid #c5c5c5;
      border-radius: .25rem;
      margin: 0 0 2em 0;
      position: relative;
      min-height: 40px;
      padding: 0;
    }
  
    .card .label {
      background-color: #767676;
      border: 2px solid #767676;
      color: white;
      padding: 0.25rem 0.75rem;
      font-size: 0.875rem;
      text-transform: uppercase;
      display: inline-block;
      margin: 0;
      border-radius: 0.25rem;
    }
  
    .card .label__text {
      vertical-align: text-top;
        font-weight: bold;
    }
  
    .card .label--critical {
      background-color: #AB1A1A;
      border-color: #AB1A1A;
    }
  
    .card .label--high {
      background-color: #CE5019;
      border-color: #CE5019;
    }
  
    .card .label--medium {
      background-color: #D68000;
      border-color: #D68000;
    }
  
    .card .label--low {
      background-color: #88879E;
      border-color: #88879E;
    }
  
    .severity--low {
      border-color: #88879E;
    }
  
    .severity--medium {
      border-color: #D68000;
    }
  
    .severity--high {
      border-color: #CE5019;
    }
  
    .severity--critical {
      border-color: #AB1A1A;
    }
  
    .card--vuln {
      padding-top: 4em;
    }
  
    .card--vuln .label {
      left: 0;
      position: absolute;
      top: 1.1em;
      padding-left: 1.9em;
      padding-right: 1.9em;
      border-radius: 0 0.25rem 0.25rem 0;
    }
  
    .card__section {
      background-color: #faf9fa
    }
  
    .card__section h2 {
      font-size: 22px;
      margin-bottom: 0.5em;
    }
  
    .card__section p {
      margin: 0 0 12px 0;
    }
  
    .card__meta {
      padding: 0;
      margin-top: 10px;
      font-size: 1.1em;
    }
  
    .card__meta__item {
      display: inline-block;
    }
  
    .card .card__meta__paths {
      font-size: 0.9em;
    }
  
    .card__title {
      font-size: 28px;
      margin-top: 0;
    }
  
    .card__cta p {
      margin: 0;
      text-align: right;
    }
  
  /* Styles by Andy */
  
  .brand, .brand:link, .brand:visited { display:inline-block; border:none; }
  .brand::after { background:none; }
  
  .test-meta { float:right; max-width:500px; opacity:.75; font-size:12px; text-align: right; }
  .test-meta .filepath { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
  .test-meta .timestamp { margin:0 0 6px }
  
  .report-summary { display:flex; justify-content: space-between; margin-bottom: 24px; }
  .header-wrap, .source-panel { align-self: center; }
  .severity-icon { display:inline-block; width:32px; height: 32px;  }
  .severity-icon.severity-icon--critical { background:url(data:image/svg+xml;base64,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) }
  .severity-icon.severity-icon--high { background:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI0NFNTAxOSIvPgo8cGF0aCBkPSJNMjAuNDI5NyAxMy44ODY3VjE2LjYyODlIMTEuNTIzNFYxMy44ODY3SDIwLjQyOTdaTTEyLjU2NjQgNi45Mzc1VjI0SDkuMDUwNzhWNi45Mzc1SDEyLjU2NjRaTTIyLjkzNzUgNi45Mzc1VjI0SDE5LjQzMzZWNi45Mzc1SDIyLjkzNzVaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K) }
  .severity-icon.severity-icon--medium { background:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI0Q2ODAwMCIvPgo8cGF0aCBkPSJNOC42MTcxOSA2LjkzNzVIMTEuNTkzOEwxNS45NzY2IDE5LjQ2NDhMMjAuMzU5NCA2LjkzNzVIMjMuMzM1OUwxNy4xNzE5IDI0SDE0Ljc4MTJMOC42MTcxOSA2LjkzNzVaTTcuMDExNzIgNi45Mzc1SDkuOTc2NTZMMTAuNTE1NiAxOS4xNDg0VjI0SDcuMDExNzJWNi45Mzc1Wk0yMS45NzY2IDYuOTM3NUgyNC45NTMxVjI0SDIxLjQzNzVWMTkuMTQ4NEwyMS45NzY2IDYuOTM3NVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=) }
  .severity-icon.severity-icon--low { background:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzg4ODc5RSIvPgo8cGF0aCBkPSJNMjIgMjEuMjU3OFYyNEgxMy40MTAyVjIxLjI1NzhIMjJaTTE0LjU0NjkgNi45Mzc1VjI0SDExLjAzMTJWNi45Mzc1SDE0LjU0NjlaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K) }
  .meta-count .severity-icon { float:left; width:24px; height:24px; margin-right:6px; background-size: 24px 24px; }
  
  .source-panel { text-align: right; }
  .source-panel__heading { display:block; margin-top:6px; text-transform:uppercase; color:#727184; font-size:12px; }
  .scan-coverage { max-width: 400px; padding:0; margin:12px 0 0; font-size:14px; }
  .scan-coverage .type { display:inline-block; list-style-type: none; padding:3px; margin:0 0 0 10px; }
  
  .card.severity--low { --severity-color: #88879E; }
  .card.severity--medium { --severity-color: #D68000; }
  .card.severity--high { --severity-color: #CE5019; }
  .card.severity--critical { --severity-color: #AB1A1A; }
  .card {
    padding:0;
    border-width:4px 1px 1px;
    border-color:#d3d3d9;
    border-top-color: var(--severity-color);
    border-radius: 4px 4px 0 0;
  }
  
  .card__tab__radio { display: none; pointer-events: none; }
  .dataflow, .fix-analysis { display: none; }
  
  .card input[id^="tab-dataflow"]:checked ~ .card__header .card__action[for^="tab-dataflow"] {
    background-color: var(--severity-color);
    color: #fff;
    border-radius: 20px;
  }
  
  .card input[id^="tab-fix"]:checked ~ .card__header .card__action[for^="tab-fix"] {
    background-color: #4b45a1;
    color: #fff;
    border-radius: 20px;
  }
  
  .card input[id^="tab-dataflow"]:checked ~ .card__section .dataflow,
  .card input[id^="tab-fix"]:checked ~ .card__section .fix-analysis {
    display: block;
  }
  
  .card__header { padding:24px }
  .card__header__main { display:flex; }
  .card__header__main .severity-icon { margin-right:12px; }
  .card__meta { display:flex; align-items:flex-start; margin:0; list-style:none; }
  .card__meta__item { display:inline-block; padding-right:8px; border-right:1px solid #eee; margin-right: 8px; font-size:12px; color:#727184 }
  .card__meta__item:last-child { border:none; }
  .card .card__title { display:inline-block; margin:0; font-size:22px; font-weight:600; vertical-align: top;}
  
  .card__actions { display:flex; padding:2px; border:1px solid #d3d3d9; border-radius:20px; background-color:#fff;  margin-left: auto; }
  .card__action { align-self: center; padding:5px 15px; font-size:14px; cursor: pointer; transition: all .2s; }
  .card__action:hover { color: #4b45a1; }
  
  .card__section {padding:24px; border-top: 1px solid #d3d3d9}
  
  .card__snippet { background-color: transparent; font-family: monospace; white-space: pre; margin: 1em 0px;  }
  
  .card__summary { position: relative; padding: 16px 24px; border: 1px solid #d3d3d9; border-left-width:4px; border-radius:2px; background-color:#fff; color:#393842; word-break: break-word; }
  .card__summary.severity--critical { border-left-color:#AB1A1A ; }
  .card__summary.severity--high { border-left-color:#CE5019 ; }
  .card__summary.severity--medium { border-left-color:#D68000 ; }
  .card__summary.severity--low { border-left-color:#88879E; }
  .card__summary .file-location { margin-top:12px; font-size:13px; color:#727184 }
  
  .dataflow { margin:12px 0 }
  .dataflow__item, .code-source { display: flex; padding:4px 8px; border:1px solid transparent; border-width: 1px 0; font-family: monospace; color:#393842; font-size:14px }
  .dataflow__item:hover { cursor:pointer; border-color: #eee; background-color:#fff; }
  .dataflow__item:hover .marker { background-color: #ffc905; }
  .dataflow__step, .dataflow__lineno, .code-lineno { align-self: flex-start; flex:0 0 50px; width: 50px; text-align: right; color:#727184; font-size:12px; }
  .dataflow__step  { flex:0 0 30px; width: 30px; }
  .dataflow__lineno { flex:0 0 60px;  padding:0 20px 0 0; }
  .dataflow__code { display:flex; align-self: flex-start; align-content: flex-start; flex-grow: 1; margin-right:40px; line-height:1; overflow:auto; }
  .dataflow__code h1, .dataflow__code h2, .dataflow__code h3, .dataflow__code h4, .dataflow__code h5, .dataflow__code h6, .dataflow__code strong, .dataflow__code b, .dataflow__code em, .dataflow__code i, .dataflow__code p  { padding:0; margin:0; font-family: monospace; color:#393842; font-size:14px; font-weight:400; font-style: normal;  } /* overriding html default styling within the code */
  .dataflow__code br { display:none  } /* overriding html default styling within the code */
  .dataflow__code .dataflow__codeprefix, .dataflow__code .dataflow__codesuffix { display:inline-block; margin:0; white-space:nowrap }
  .dataflow__badge { align-self:flex-start; padding:2px 3px; border:1px solid #d3d3d9; border-radius:2px; margin-top:2px; background-color:#f4f4f6; font-size:11px; line-height:1; font-family: sans-serif; text-transform: uppercase; color:#646374 }
  .dataflow__filename { padding:6px 12px; border:1px solid #eee; border-radius: 4px; margin:20px 0 10px; font-size:13px; color:#393842; background-color: #fff; }
  
  .marker { padding:2px 6px; border-radius:4px; background-color: rgba(0,0,0,.1); word-break: break-all; white-space: nowrap; }
  .marker.block { white-space: pre; line-height: 1.5; }
  
  .card .card__section h2, .card .card__section h3 { font-size:18px; }
  .card__panel__heading { padding:5px 0; font-size:22px; font-weight: 600; }
  .heading-char { display: inline-block; width:22px; height:22px; border:1px solid #d3d3d9; border-radius:15px; margin-right:5px; color:#727184; font-size:14px; background-color:#fff; text-align: center; }
  
  .card__panel__markdown { font-size:14px; color:#393842; }
  .card__panel__markdown table { border-collapse: collapse; border:1px solid #d3d3d9; border-radius:3px; }
  .card__panel__markdown tbody { display: table-row-group; vertical-align: top; }
  .card__panel__markdown table td, .card__panel__markdown table th { min-width: 100px; padding:2px 5px; text-align: left; border-top:1px solid #d3d3d9; }
  .card__panel__markdown table td { background-color:#fff; font-size:14px; line-height: 1.4; }
  
  .is-hidden { display:none; }
  
  @media print {
    /* All your print styles go here */
     .card .fix-analysis { display:block; }
     .card__section { background-color: none; }
     .project__header, .card__actions, .severity-icon { display: none; }
     .marker { border:1px solid #555; margin:-1px 0; background: transparent }
  }
  
  </style>
  <style type="text/css">
    .metatable {
      text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -webkit-box-direction: normal;
      color: inherit;
      font-feature-settings: "pnum";
      box-sizing: border-box;
      background: transparent;
      border: 0;
      font: inherit;
      font-size: 100%;
      margin: 0;
      outline: none;
      padding: 0;
      text-align: left;
      text-decoration: none;
      vertical-align: baseline;
      z-index: auto;
      margin-top: 12px;
      border-collapse: collapse;
      border-spacing: 0;
      font-variant-numeric: tabular-nums;
      max-width: 51.75em;
    }
  
    tbody {
      text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -webkit-box-direction: normal;
      color: inherit;
      font-feature-settings: "pnum";
      border-collapse: collapse;
      border-spacing: 0;
      box-sizing: border-box;
      background: transparent;
      border: 0;
      font: inherit;
      font-size: 100%;
      margin: 0;
      outline: none;
      padding: 0;
      text-align: left;
      text-decoration: none;
      vertical-align: baseline;
      z-index: auto;
      display: flex;
      flex-wrap: wrap;
    }
  
    .meta-row {
      text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -webkit-box-direction: normal;
      color: inherit;
      font-feature-settings: "pnum";
      border-collapse: collapse;
      border-spacing: 0;
      box-sizing: border-box;
      background: transparent;
      border: 0;
      font: inherit;
      font-size: 100%;
      outline: none;
      text-align: left;
      text-decoration: none;
      vertical-align: baseline;
      z-index: auto;
      display: flex;
      align-items: start;
      border-top: 1px solid #d3d3d9;
      padding: 8px 0 0 0;
      border-bottom: none;
      margin: 8px;
      width: 47.75%;
    }
  
    .meta-row-label {
      text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -webkit-box-direction: normal;
      font-feature-settings: "pnum";
      border-collapse: collapse;
      border-spacing: 0;
      color: #4c4a73;
      box-sizing: border-box;
      background: transparent;
      border: 0;
      font: inherit;
      margin: 0;
      outline: none;
      text-decoration: none;
      z-index: auto;
      align-self: start;
      flex: 1;
      font-size: 1rem;
      line-height: 1.5rem;
      padding: 0;
      text-align: left;
      vertical-align: top;
      text-transform: none;
      letter-spacing: 0;
    }
  
    .meta-row-value {
      text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -webkit-box-direction: normal;
      color: inherit;
      font-feature-settings: "pnum";
      border-collapse: collapse;
      border-spacing: 0;
      word-break: break-word;
      box-sizing: border-box;
      background: transparent;
      border: 0;
      font: inherit;
      font-size: 100%;
      margin: 0;
      outline: none;
      padding: 0;
      text-align: right;
      text-decoration: none;
      vertical-align: baseline;
      z-index: auto;
    }
  </style>
</head>

<body class="section-projects">
  <main class="layout-stacked">
    <header class="project__header">
      <div class="layout-container">
        <a class="brand" href="https://snyk.io" title="Snyk">
          <svg width="100" height="46" fill="none" xmlns="http://www.w3.org/2000/svg">
            <title>Snyk - Snyk Code</title>
            <path d="M89.7 24.95h-.4v6.95h-5V14.6l5-7.8v16.15c.99-1.22 4.33-5.84 4.33-5.84h6.17l-5.85 6.18 6.05 8.63h-6.3l-4-6.96ZM75.77 23.8l2.05-6.67h4.94l-5.8 14.71c-1.72 4.43-4.2 7.42-7.89 7.42a7.02 7.02 0 0 1-3.24-.72l1.99-3.08c.3.04.6.05.91.05 1.7 0 2.98-1.66 3.81-3.62l-6.08-14.76h5.6l2.18 6.59c.43 1.25.74 3.64.74 3.64s.39-2.3.79-3.56Zm-15.09-.34c0-1.93-.85-2.84-2.47-2.84-.8 0-1.62.23-2.19.57v10.7h-4.99V17.28l4.89-.4-.12 2.4h.16a6.2 6.2 0 0 1 4.97-2.5c2.56 0 4.77 1.58 4.77 5.33v9.8H60.7v-8.44Zm-23.94 7.67.45-3.4c1.56.77 3.32 1.13 4.8 1.13 1.08 0 1.79-.36 1.79-1.01 0-1.87-6.73-1.42-6.73-6.3 0-3.13 2.86-4.77 6.44-4.77 1.79 0 3.64.48 4.83.9l-.48 3.35a13.22 13.22 0 0 0-4.38-.93c-.88 0-1.59.3-1.59.88 0 1.84 6.88 1.47 6.88 6.24 0 3.17-2.82 4.99-6.68 4.99-2.13 0-3.85-.37-5.33-1.08Z" fill="#fff"/>
            <g clip-path="url(#a)" fill="#fff">
              <path d="M25.86 10c.27.73.42 1.5.47 2.28v.24l-1.7 4.53c.4 3.58.77 8 .35 9.06-.2.53-.81 1.48-1.81 2.81l1.08 10.04 4.3-3.08a1.7 1.7 0 0 0 .7-1.37V12.17a1.7 1.7 0 0 0-1.22-1.63 33.9 33.9 0 0 0-2.17-.54ZM11.53 12.29a9.74 9.74 0 0 1 6.22 0l.52-.86.35-2.42s-2.43-.13-3.96-.13c-1.43 0-2.76.05-4 .14l.35 2.42.52.85ZM4.27 26.11c-.41-1.06-.04-5.48.35-9.06l-1.68-4.53v-.24c.04-.78.2-1.55.46-2.28-.9.2-1.62.39-2.18.54A1.7 1.7 0 0 0 0 12.17v22.34a1.7 1.7 0 0 0 .71 1.37l4.32 3.1L6.1 28.95c-1-1.35-1.62-2.3-1.83-2.84Z"/>
              <path d="M20.7.97a.11.11 0 0 1 .11.06l.57 1.07c1.53 2.84 4.12 8.18 4.12 10.17v.1l-1.7 4.57V17c.54 4.82.7 8.1.42 8.8-.24.62-1.2 1.94-2 3l1.18 9.57-.71.5-7.07 5.07a1.7 1.7 0 0 1-1.98 0l-5.71-4.11-.87-.63-1.16-.83 1.16-9.56a19 19 0 0 1-2.02-3.02c-.27-.7-.11-3.98.42-8.8v-.07l-1.7-4.57v-.1c0-1.99 2.59-7.33 4.12-10.17l.57-1.06a.11.11 0 0 1 .1-.07.12.12 0 0 1 .13.1l1.53 10.67.95 1.56.24-.08c1.04-.37 2.13-.57 3.24-.6 1.1.03 2.2.24 3.25.6l.24.08.93-1.56L20.6 1.07a.12.12 0 0 1 .12-.1Zm0-.97a1.08 1.08 0 0 0-1.07.94l-1.5 10.45-.45.73c-1-.3-2.02-.47-3.05-.49a11 11 0 0 0-3.04.49l-.45-.73L9.64.94A1.08 1.08 0 0 0 8.56 0a1.1 1.1 0 0 0-.97.57l-.57 1.07c-.7 1.31-4.23 8-4.23 10.64v.26l.06.17 1.62 4.36c-.56 5.16-.68 8.22-.34 9.1.08.2.32.82 1.91 2.94l-1.12 9.16-.07.57.47.34 1.16.83.87.62 5.72 4.1a2.66 2.66 0 0 0 3.1 0l7.07-5.06.7-.5.47-.34-.07-.57-1.12-9.18c1.57-2.09 1.82-2.7 1.9-2.9.34-.88.22-3.95-.34-9.1l1.62-4.37.06-.16v-.27c0-2.64-3.53-9.32-4.23-10.64L21.66.58a1.09 1.09 0 0 0-.97-.57L20.7 0Z"/>
              <path d="m7.76 27.3 2.08-.61a.22.22 0 0 0 .11-.08l1.99-2.26a.24.24 0 0 1 .37.02c.03.04.05.08.05.13l.35 6.48c0 .05-.02.1-.04.15-.14.2-.54.81-.54 1.2 0 .48.73 1.11 1.42 1.62l.03-.06c.24-.44.57-.52.72-.18.06.25.08.5.06.76h.01v.6c0 .03 0 .07-.02.1-.13.26-.75 1.37-2.08 1.37-.57 0-1.96-.18-2.58-1.88a18 18 0 0 1-.78-5.2v-.2l-.13-.15c-.33-.41-.72-.92-1.13-1.46a.24.24 0 0 1 .11-.36ZM20.52 29.11l-.12.16v.2c0 .13 0 3.08-.78 5.2a2.67 2.67 0 0 1-2.58 1.88c-1.33 0-1.94-1.1-2.08-1.37a.24.24 0 0 1-.02-.1v-.6c-.02-.26 0-.52.06-.77.14-.34.46-.26.72.18l.03.07c.7-.51 1.42-1.15 1.42-1.63 0-.38-.4-1-.54-1.2a.26.26 0 0 1-.04-.14l.35-6.48a.24.24 0 0 1 .3-.23c.05.02.09.04.12.08l1.98 2.26c.03.04.07.06.12.07l2.07.6a.24.24 0 0 1 .13.39c-.4.53-.8 1.03-1.14 1.43ZM10.26 22.31a.37.37 0 0 1-.36.32.61.61 0 0 0 .95.36.6.6 0 0 0-.04-1h1.04v.03a1.37 1.37 0 1 1-2.74.02V22h1.06-.02a.37.37 0 0 1 .1.32ZM9.59 20.92a.24.24 0 0 1-.19-.09c-.3-.35-1.2-1.55.37-1.55 1.17 0 2.1.71 2.62 1.22a.24.24 0 0 1-.17.42H9.59ZM19.11 22h-.01 1.06v.05a1.37 1.37 0 1 1-2.75-.02V22h1.05a.6.6 0 0 0-.25.73.6.6 0 0 0 .49.37.6.6 0 0 0 .68-.47.37.37 0 0 1-.27-.63ZM17.04 20.92a.24.24 0 0 1-.17-.42 3.88 3.88 0 0 1 2.63-1.22c1.56 0 .66 1.2.36 1.55a.24.24 0 0 1-.19.09h-2.63Z"/>
              <path d="M17.02 37.48a2 2 0 0 1-.49-.06.24.24 0 0 0-.24.1 2 2 0 0 1-3.29 0 .24.24 0 0 0-.24-.1c-.15.03-.3.05-.46.05h-.14a.24.24 0 0 0-.26.23c0 .05 0 .09.03.13a3.1 3.1 0 0 0 5.45 0 .24.24 0 0 0-.1-.34.24.24 0 0 0-.13-.02h-.13Zm-1.72-2.72c.17.06.33.14.48.25-.15-.25-.35-.34-.48-.25Zm-1.77.22c.14-.1.3-.17.46-.22-.13-.08-.3-.01-.46.23v-.01ZM13.91 34.26c-.17.06-.35.14-.5.25.17-.25.36-.34.5-.25ZM15.92 34.54a2.26 2.26 0 0 0-.55-.28c.15-.1.37 0 .55.28Z"/>
              <path d="M13.68 37.12a.1.1 0 0 0-.06.17 1.67 1.67 0 0 0 2.03 0 .1.1 0 0 0 .03-.1.1.1 0 0 0-.1-.07h-1.9ZM5.44 11.62c.1-.97 1-3.12 1.94-5.08a.2.2 0 0 1 .37.06l.18 1.28v.06c-.1.73-.54 3.25-1.67 5.02a.2.2 0 0 1-.35-.04l-.45-1.21a.22.22 0 0 1-.02-.1ZM23.04 12.96a14.32 14.32 0 0 1-1.67-5.08l.19-1.28a.2.2 0 0 1 .36-.06c.94 1.96 1.84 4.1 1.94 5.08v.08l-.45 1.21a.2.2 0 0 1-.********* 0 0 1-.09-.08Z"/>
            </g>
            <defs>
              <clipPath id="a">
                <path fill="#fff" d="M0 0h29.27v45.23H0z"/>
              </clipPath>
            </defs>
          </svg>
        </a>
        <div class="test-meta">
          <p class="timestamp">July 16th 2025, 1:34:44 pm (UTC+00:00)</p>
          <div class="filepath" title="/builds/des/eis/hybrid-integration-platform/hip-services">Source: /builds/des/eis/hybrid-integration-platform/hip-services</div>
        </div>
      </div>
    </header>

    <div class="layout-container">
      <div class="report-summary">
        <div class="header-wrap">
            <h1 class="project__header__title">Snyk Code Report</h1>

          <div class="meta-counts">
            <div class="meta-count">
              <span class="severity-icon severity-icon--high"></span> <span><strong>1</strong> high issues</span>
            </div>
            <div class="meta-count">
              <span class="severity-icon severity-icon--medium"></span> <span><strong>1</strong> medium issues</span>
            </div>
            <div class="meta-count">
              <span class="severity-icon severity-icon--low"></span> <span><strong>1</strong> low issues</span>
            </div>
          </div>
        </div>
        <div class="source-panel">
          <span class="source-panel__heading">Scan Coverage</span>
          <ul class="scan-coverage">
            <li class="type">Java files: <strong>268</strong></li><li class="type">XML files: <strong>1</strong></li>
          </ul>            
        </div>
      </div>
        <div class="cards--vuln filter--patch filter--ignore">
          <div class="card card--vuln disclosure--not-new severity--high"  data-snyk-test="high">
              <input type="radio" name="tab-view-0" id="tab-dataflow-0" value="dataflow" class="card__tab__radio" checked>
              <input type="radio" name="tab-view-0" id="tab-fix-0" value="fix" class="card__tab__radio" >
          
              <header class="card__header">
                  <div class="card__header__main">
                      <div class="severity-icon severity-icon--high"></div>
                      <h2 class="card__title">Cross-site Scripting (XSS)</h2>
                      <div class="card__actions">
                          <label for="tab-dataflow-0" class="card__action">Data Flow</label>
                          <label for="tab-fix-0" class="card__action">Fix Analysis</label>
                      </div>
                  </div>
                  <ul class="card__meta">
                      <li class="card__meta__item">SNYK-CODE</li>
                      <li class="card__meta__item">CWE-79</li>
                      <li class="card__meta__item">XSS</li>
                  </ul>
              </header>
              <div class="card__section">
                  <div class="card__summary severity--high">
                      <p>Unsanitized input from the request URL flows into here, where it is used to render an HTML page returned to the user. This may result in a Cross-Site Scripting attack (XSS).</p>
                      <div class="file-location">Found in: <strong>src/main/java/com/dell/it/hip/controller/HIPIntegrationManagementController.java (line : 285)</strong></div>
                  </div>
                  <div class="card__panel dataflow">
                      <h2 class="card__panel__heading"><span class="heading-char">⇣</span> Data Flow</h2>
                      <div class="dataflow__filename"> src/main/java/com/dell/it/hip/controller/HIPIntegrationManagementController.java</div>
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">276:39</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">	public ResponseEntity&lt;String&gt; status(<br /></span><span class="marker">@PathVariable String name,</span><span class="dataflow__codesuffix"> @PathVariable String version) {</span></div>
                          <span class="dataflow__badge" title="Data is 'tainted' if it comes from an insecure source such as a file, the network, or the user. This tainted data is passed to a sensitive sink, as described below.">Source</span>
                          <span class="dataflow__step">0</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">276:39</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">	public ResponseEntity&lt;String&gt; status(<br /></span><span class="marker">@PathVariable String name,</span><span class="dataflow__codesuffix"> @PathVariable String version) {</span></div>
                          
                          <span class="dataflow__step">1</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">278:81</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String validatedName &#x3D; InputValidationUtil.validateAndSanitizeIntegrationName(<br /></span><span class="marker">name)</span><span class="dataflow__codesuffix">;</span></div>
                          
                          <span class="dataflow__step">2</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">278:26</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String validatedName &#x3D; <br /></span><span class="marker">InputValidationUtil.validateAndSanitizeIntegrationName(</span><span class="dataflow__codesuffix">name);</span></div>
                          
                          <span class="dataflow__step">3</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">278:10</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String <br /></span><span class="marker">validatedName &#x3D; InputValidationUtil.validateAndSanitizeIntegrationName(name);</span><span class="dataflow__codesuffix"></span></div>
                          
                          <span class="dataflow__step">4</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">282:51</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.filter(i -&gt; i.getHipIntegrationName().equals(<br /></span><span class="marker">validatedName)</span><span class="dataflow__codesuffix"> &amp;&amp; i.getVersion().equals(validatedVersion))</span></div>
                          
                          <span class="dataflow__step">5</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">282:18</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.filter(i -&gt; <br /></span><span class="marker">i.getHipIntegrationName().equals(</span><span class="dataflow__codesuffix">validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))</span></div>
                          
                          <span class="dataflow__step">6</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">282:18</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.filter(i -&gt; <br /></span><span class="marker">i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))</span><span class="dataflow__codesuffix"></span></div>
                          
                          <span class="dataflow__step">7</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">282:13</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.filter(<br /></span><span class="marker">i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))</span><span class="dataflow__codesuffix"></span></div>
                          
                          <span class="dataflow__step">8</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">281:19</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String status &#x3D; <br /></span><span class="marker block">orchestrationService.getAllHIPIntegrationsWithStatus().stream()
          				.filter(</span><span class="dataflow__codesuffix">i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))</span></div>
                          
                          <span class="dataflow__step">9</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">283:10</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.map(<br /></span><span class="marker">i </span><span class="dataflow__codesuffix">-&gt; i.getStatus().name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">10</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">283:15</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.map(i -&gt; <br /></span><span class="marker">i.</span><span class="dataflow__codesuffix">getStatus().name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">11</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">283:15</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.map(i -&gt; <br /></span><span class="marker">i.getStatus(</span><span class="dataflow__codesuffix">).name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">12</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">283:15</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">				.map(i -&gt; <br /></span><span class="marker">i.getStatus().name(</span><span class="dataflow__codesuffix">)).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">13</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">281:19</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String status &#x3D; <br /></span><span class="marker block">orchestrationService.getAllHIPIntegrationsWithStatus().stream()
          				.filter(i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))
          				.map(</span><span class="dataflow__codesuffix">i -&gt; i.getStatus().name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">14</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">281:19</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String status &#x3D; <br /></span><span class="marker block">orchestrationService.getAllHIPIntegrationsWithStatus().stream()
          				.filter(i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))
          				.map(i -&gt; i.getStatus().name()).findFirst(</span><span class="dataflow__codesuffix">).orElse(IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">15</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">281:19</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String status &#x3D; <br /></span><span class="marker block">orchestrationService.getAllHIPIntegrationsWithStatus().stream()
          				.filter(i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))
          				.map(i -&gt; i.getStatus().name()).findFirst().orElse(</span><span class="dataflow__codesuffix">IntegrationStatus.UNREGISTERED.name());</span></div>
                          
                          <span class="dataflow__step">16</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">281:10</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		String <br /></span><span class="marker block">status &#x3D; orchestrationService.getAllHIPIntegrationsWithStatus().stream()
          				.filter(i -&gt; i.getHipIntegrationName().equals(validatedName) &amp;&amp; i.getVersion().equals(validatedVersion))
          				.map(i -&gt; i.getStatus().name()).findFirst().orElse(IntegrationStatus.UNREGISTERED.name());</span><span class="dataflow__codesuffix"></span></div>
                          
                          <span class="dataflow__step">17</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">285:69</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(<br /></span><span class="marker">status)</span><span class="dataflow__codesuffix">;</span></div>
                          
                          <span class="dataflow__step">18</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">285:10</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		return <br /></span><span class="marker">ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(</span><span class="dataflow__codesuffix">status);</span></div>
                          
                          <span class="dataflow__step">19</span>
                      </div>
                      
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">285:10</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		return <br /></span><span class="marker">ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(</span><span class="dataflow__codesuffix">status);</span></div>
                          <span class="dataflow__badge" title="Sinks are the operations that must receive clean data and that you wouldn't want an attacker to be able to manipulate.">Sink</span>
                          <span class="dataflow__step">20</span>
                      </div>
                  </div>
                  <div class="card__panel fix-analysis">
                      <h2 class="card__panel__heading"><span class="heading-char">✓</span> Fix Analysis</h2>
                      <div class="card__panel__markdown"><h2 id="details">Details</h2>
          <p>A cross-site scripting attack occurs when the attacker tricks a legitimate web-based application or site to accept a request as originating from a trusted source.</p>
          <p>This is done by escaping the context of the web application; the web application then delivers that data to its users along with other trusted dynamic content, without validating it. The browser unknowingly executes malicious script on the client side (through client-side languages; usually JavaScript or HTML)  in order to perform actions that are otherwise typically blocked by the browser&#39;s Same Origin Policy.</p>
          <p>Injecting malicious code is the most prevalent manner by which XSS is exploited; for this reason, escaping characters in order to prevent this manipulation is the top method for securing code against this vulnerability.</p>
          <p>Escaping means that the application is coded to mark key characters, and particularly key characters included in user input, to prevent those characters from being interpreted in a dangerous context. For example, in HTML, <code>&lt;</code> can be coded as  <code>&amp;lt</code>; and <code>&gt;</code> can be coded as <code>&amp;gt</code>; in order to be interpreted and displayed as themselves in text, while within the code itself, they are used for HTML tags. If malicious content is injected into an application that escapes special characters and that malicious content uses <code>&lt;</code> and <code>&gt;</code> as HTML tags, those characters are nonetheless not interpreted as HTML tags by the browser if they&#39;ve been correctly escaped in the application code and in this way the attempted attack is diverted.</p>
          <p>The most prominent use of XSS is to steal cookies (source: OWASP HttpOnly) and hijack user sessions, but XSS exploits have been used to expose sensitive information, enable access to privileged services and functionality and deliver malware.</p>
          <h3 id="types-of-attacks">Types of attacks</h3>
          <p>There are a few methods by which XSS can be manipulated:</p>
          <table>
          <thead>
          <tr>
          <th>Type</th>
          <th>Origin</th>
          <th>Description</th>
          </tr>
          </thead>
          <tbody><tr>
          <td><strong>Stored</strong></td>
          <td>Server</td>
          <td>The malicious code is inserted in the application (usually as a link) by the attacker. The code is activated every time a user clicks the link.</td>
          </tr>
          <tr>
          <td><strong>Reflected</strong></td>
          <td>Server</td>
          <td>The attacker delivers a malicious link externally from the vulnerable web site application to a user. When clicked, malicious code is sent to the vulnerable web site, which reflects the attack back to the user&#39;s browser.</td>
          </tr>
          <tr>
          <td><strong>DOM-based</strong></td>
          <td>Client</td>
          <td>The attacker forces the user&#39;s browser to render a malicious page. The data in the page itself delivers the cross-site scripting data.</td>
          </tr>
          <tr>
          <td><strong>Mutated</strong></td>
          <td></td>
          <td>The attacker injects code that appears safe, but is then rewritten and modified by the browser, while parsing the markup. An example is rebalancing unclosed quotation marks or even adding quotation marks to unquoted parameters.</td>
          </tr>
          </tbody></table>
          <h3 id="affected-environments">Affected environments</h3>
          <p>The following environments are susceptible to an XSS attack:</p>
          <ul>
          <li>Web servers</li>
          <li>Application servers</li>
          <li>Web application environments</li>
          </ul>
          <h2 id="best-practices-for-prevention">Best practices for prevention</h2>
          <p>This section describes the top best practices designed to specifically protect your code:</p>
          <ul>
          <li>Sanitize data input in an HTTP request before reflecting it back, ensuring all data is validated, filtered or escaped before echoing anything back to the user, such as the values of query parameters during searches.</li>
          <li>Convert special characters such as <code>?</code>, <code>&amp;</code>, <code>/</code>, <code>&lt;</code>, <code>&gt;</code> and spaces to their respective HTML or URL encoded equivalents.</li>
          <li>Give users the option to disable client-side scripts.</li>
          <li>Redirect invalid requests.</li>
          <li>Detect simultaneous logins, including those from two separate IP addresses, and invalidate those sessions.</li>
          <li>Use and enforce a Content Security Policy (source: Wikipedia) to disable any features that might be manipulated for an XSS attack.</li>
          <li>Read the documentation for any of the libraries referenced in your code to understand which elements allow for embedded HTML.</li>
          </ul>
          </div>
                  </div>
              </div>
          </div>
          <div class="card card--vuln disclosure--not-new severity--medium"  data-snyk-test="medium">
              <input type="radio" name="tab-view-1" id="tab-dataflow-1" value="dataflow" class="card__tab__radio" checked>
              <input type="radio" name="tab-view-1" id="tab-fix-1" value="fix" class="card__tab__radio" >
          
              <header class="card__header">
                  <div class="card__header__main">
                      <div class="severity-icon severity-icon--medium"></div>
                      <h2 class="card__title">Use of Hardcoded Passwords</h2>
                      <div class="card__actions">
                          <label for="tab-dataflow-1" class="card__action">Data Flow</label>
                          <label for="tab-fix-1" class="card__action">Fix Analysis</label>
                      </div>
                  </div>
                  <ul class="card__meta">
                      <li class="card__meta__item">SNYK-CODE</li>
                      <li class="card__meta__item">CWE-798,CWE-259</li>
                      <li class="card__meta__item">HardcodedPassword</li>
                  </ul>
              </header>
              <div class="card__section">
                  <div class="card__summary severity--medium">
                      <p>Do not hardcode passwords in code. Found hardcoded password used in here.</p>
                      <div class="file-location">Found in: <strong>src/main/java/com/dell/it/hip/config/RedissonClientConfig.java (line : 43)</strong></div>
                  </div>
                  <div class="card__panel dataflow">
                      <h2 class="card__panel__heading"><span class="heading-char">⇣</span> Data Flow</h2>
                      <div class="dataflow__filename"> src/main/java/com/dell/it/hip/config/RedissonClientConfig.java</div>
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">43:40</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">		config.useSingleServer().setPassword(<br /></span><span class="marker">&quot;sFjIvYHMJHcv&quot;)</span><span class="dataflow__codesuffix"></span></div>
                          <span class="dataflow__badge" title="Data is 'tainted' if it comes from an insecure source such as a file, the network, or the user. This tainted data is passed to a sensitive sink, as described below.">Source</span><span class="dataflow__badge" title="Sinks are the operations that must receive clean data and that you wouldn't want an attacker to be able to manipulate.">Sink</span>
                          <span class="dataflow__step">0</span>
                      </div>
                  </div>
                  <div class="card__panel fix-analysis">
                      <h2 class="card__panel__heading"><span class="heading-char">✓</span> Fix Analysis</h2>
                      <div class="card__panel__markdown"><h2 id="details">Details</h2>
          <p>Developers may use hardcoded passwords during development to streamline setup or simplify authentication while testing. Although these passwords are intended to be removed before deployment, they are sometimes inadvertently left in the code. This introduces serious security risks, especially if the password grants elevated privileges or is reused across multiple systems.</p>
          <p>An attacker who discovers a hardcoded password can potentially gain unauthorized access, escalate privileges, exfiltrate sensitive data, or disrupt service availability. If the password is reused across different environments or applications, the compromise can spread quickly and broadly.</p>
          <h2 id="best-practices-for-prevention">Best practices for prevention</h2>
          <ul>
          <li>Plan software architecture such that keys and passwords are always stored outside the code, wherever possible.</li>
          <li>Plan encryption into software architecture for all credential information and ensure proper handling of keys, credentials, and passwords.</li>
          <li>Prompt for a secure password on first login rather than hard-code a default password.</li>
          <li>If a hardcoded password or credential must be used, limit its use, for example, to system console users rather than via the network.</li>
          <li>Use strong hashes for inbound password authentication, ideally with randomly assigned salts to increase the difficulty level in case of brute-force attack.</li>
          </ul>
          </div>
                  </div>
              </div>
          </div>
          <div class="card card--vuln disclosure--not-new severity--low"  data-snyk-test="low">
              <input type="radio" name="tab-view-2" id="tab-dataflow-2" value="dataflow" class="card__tab__radio" checked>
              <input type="radio" name="tab-view-2" id="tab-fix-2" value="fix" class="card__tab__radio" >
          
              <header class="card__header">
                  <div class="card__header__main">
                      <div class="severity-icon severity-icon--low"></div>
                      <h2 class="card__title">Use of Hardcoded Credentials</h2>
                      <div class="card__actions">
                          <label for="tab-dataflow-2" class="card__action">Data Flow</label>
                          <label for="tab-fix-2" class="card__action">Fix Analysis</label>
                      </div>
                  </div>
                  <ul class="card__meta">
                      <li class="card__meta__item">SNYK-CODE</li>
                      <li class="card__meta__item">CWE-798</li>
                      <li class="card__meta__item">NoHardcodedCredentials</li>
                  </ul>
              </header>
              <div class="card__section">
                  <div class="card__summary severity--low">
                      <p>Do not hardcode credentials in code.</p>
                      <div class="file-location">Found in: <strong>src/main/java/com/dell/it/hip/monitoring/health/SftpHealthIndicator.java (line : 57)</strong></div>
                  </div>
                  <div class="card__panel dataflow">
                      <h2 class="card__panel__heading"><span class="heading-char">⇣</span> Data Flow</h2>
                      <div class="dataflow__filename"> src/main/java/com/dell/it/hip/monitoring/health/SftpHealthIndicator.java</div>
                      <div class="dataflow__item">
                          <span class="dataflow__lineno" title="line:column">57:65</span>
                          <div class="dataflow__code"><span class="dataflow__codeprefix">                String username &#x3D; parts.length &gt; 2 ? parts[2] : <br /></span><span class="marker">&quot;testuser&quot;;</span><span class="dataflow__codesuffix"></span></div>
                          <span class="dataflow__badge" title="Data is 'tainted' if it comes from an insecure source such as a file, the network, or the user. This tainted data is passed to a sensitive sink, as described below.">Source</span><span class="dataflow__badge" title="Sinks are the operations that must receive clean data and that you wouldn't want an attacker to be able to manipulate.">Sink</span>
                          <span class="dataflow__step">0</span>
                      </div>
                  </div>
                  <div class="card__panel fix-analysis">
                      <h2 class="card__panel__heading"><span class="heading-char">✓</span> Fix Analysis</h2>
                      <div class="card__panel__markdown"><h2 id="details">Details</h2>
          <p>Developers may use hardcoded credentials for convenience when coding in order to simplify their workflow. While they are responsible for removing these before production, occasionally this task may fall through the cracks. This also becomes a maintenance challenge when credentials are re-used across multiple applications.</p>
          <p>Once attackers gain access, they may take advantage of privilege level to remove or alter data, take down a site or app, or hold any of the above for ransom. The risk across multiple similar projects is even greater. If code containing the credentials is reused across multiple projects, they will all be compromised.</p>
          <h2 id="best-practices-for-prevention">Best practices for prevention</h2>
          <ul>
          <li>Plan software architecture such that keys and passwords are always stored outside the code, wherever possible.</li>
          <li>Plan encryption into software architecture for all credential information and ensure proper handling of keys, credentials, and passwords.</li>
          <li>Prompt for a secure password on first login rather than hard-code a default password.</li>
          <li>If a hardcoded password or credential must be used, limit its use, for example, to system console users rather than via the network.</li>
          <li>Use strong hashes for inbound password authentication, ideally with randomly assigned salts to increase the difficulty level in case of brute-force attack.</li>
          </ul>
          </div>
                  </div>
              </div>
          </div>
        </div>
    </div>
  </main>
</body>
<script>
  // collapse vulns in remediations
  const remediations = document.querySelectorAll(".js-remediation");
  remediations.forEach((remediation) => {
    remediation.parentElement.classList.toggle("shown");
    remediation.addEventListener("click", remediationClick);
  })

  // hide all panes
  const allPanes = document.querySelectorAll(`[data-pane]`);
  allPanes.forEach((pane) => {
    pane.classList.remove('shown');
  });
  // set first nav item as active & un-hide it's pane
  const remediationNav = document.querySelectorAll(".js-nav");
  remediationNav.forEach((nav) => {
    nav.addEventListener("click", navClick);
  })
  if (remediationNav && remediationNav.length > 0) {
    remediationNav[0].classList.add('active');
    const targetPaneData = remediationNav[0].dataset && remediationNav[0].dataset.toggle;
    if (targetPaneData) {
      showPane(targetPaneData);
    }
  }

  function remediationClick() {
    this.parentElement.classList.toggle("shown");
  }

  function navClick() {
    const remediationNav = document.querySelectorAll(".js-nav");
    remediationNav.forEach((nav) => {
      nav.classList.remove('active');
    });
    this.classList.toggle('active');
    const targetPaneData = this.dataset && this.dataset.toggle;
    if (targetPaneData) {
      showPane(targetPaneData);
    }
  }

  function showPane(targetPaneData) {
    const targetPanes = document.querySelectorAll(`[data-pane='${targetPaneData}']`);
    if (targetPanes) {
      const allPanes = document.querySelectorAll(`[data-pane]`);
      allPanes.forEach((pane) => {
        pane.classList.remove('shown');
      });
      targetPanes[0].classList.add('shown');
    }
  }
</script>
</html>