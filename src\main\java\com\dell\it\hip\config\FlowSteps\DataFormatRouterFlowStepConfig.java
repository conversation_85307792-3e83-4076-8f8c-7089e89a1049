package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class DataFormatRouterFlowStepConfig extends FlowStepConfig {
    private List<DocTypeDataFormatRouterConfig> docTypeConfigs = new ArrayList<>();
    private DefaultDataFormatRouterConfig defaultConfig;

    @Data
    public static class DocTypeDataFormatRouterConfig extends DocTypeConfig {
        private String dataFormat;           // e.g. "EDI_X12", "EDI_EDIFACT"
        private String targetIntegrationName; // HIPIntegration to route to
        private String targetIntegrationVersion; // Version, optional
    }

    @Data
    public static class DefaultDataFormatRouterConfig {
        private String dataFormat; // e.g. "CSV", "XML", "JSON", etc
        private String routeToHandler; // e.g. "primary", "fallback", etc (use as needed)
    }
}
