# Test Coverage Exclusions for HIP Services
# Classes that should be excluded from the 70% coverage requirement

# ===== CONFIGURATION CLASSES =====
# These are primarily configuration classes with getters/setters and minimal business logic

com.dell.it.hip.config.adapters.IbmmqAdapterConfig - Configuration class with only getters/setters
com.dell.it.hip.config.adapters.SftpAdapterConfig - Configuration class with only getters/setters  
com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig - Configuration class with only getters/setters
com.dell.it.hip.config.adapters.DynamicRabbitMQAdapterConfig - Configuration class with only getters/setters
com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.AggregatorFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.AttributeProcessorFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.DedupFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.DefaultSplitterConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.DocTypeConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.Edi997FlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.EDIFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.EnvelopeFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.FlowRoutingConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.FlowTargetsRoutingConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.HandlerTarget - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.SplitterDocTypeConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.SplitterFlowStepConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.StrictOrderConfig - Configuration class with only getters/setters
com.dell.it.hip.config.FlowSteps.ValidationConfig - Configuration class with only getters/setters
com.dell.it.hip.config.Handlers.KafkaHandlerConfig - Configuration class with only getters/setters
com.dell.it.hip.config.Handlers.DynamicHttpsHandlerConfig - Configuration class with only getters/setters
com.dell.it.hip.config.RedisHealthIndicator - Configuration class for health check
com.dell.it.hip.config.RetrySettings - Configuration class with only getters/setters
com.dell.it.hip.config.rules.Rule - Configuration class with only getters/setters
com.dell.it.hip.config.rules.RuleCondition - Configuration class with only getters/setters
com.dell.it.hip.config.TransactionStatus - Enum class
com.dell.it.hip.config.HIPIntegrationControlEvent - Event class with only getters/setters
com.dell.it.hip.config.Tag - Configuration class with only getters/setters

# ===== DATA TRANSFER OBJECTS (DTOs) =====
# These are simple data containers with minimal logic

com.dell.it.hip.controller.dto.IntegrationDefinitionsWithStatusResponse - DTO class
com.dell.it.hip.controller.dto.IntegrationStatusResponse - DTO class
com.dell.it.hip.config.FlowSteps.AttributeProcessorStepConfigRef - DTO class
com.dell.it.hip.config.FlowSteps.AttributeProcessResult - DTO class
com.dell.it.hip.config.FlowSteps.AttributeMapping - DTO class
com.dell.it.hip.security.entity.User - Entity class with minimal business logic
com.dell.it.hip.security.entity.Role - Entity class with minimal business logic

# ===== EXCEPTION CLASSES =====
# These are simple exception classes with minimal logic

com.dell.it.hip.exception.AdapterConfigurationException - Exception class
com.dell.it.hip.exception.ExternalSystemUnavailableException - Exception class
com.dell.it.hip.exception.IntegrationNotFoundException - Exception class
com.dell.it.hip.exception.MessageTransformationException - Exception class
com.dell.it.hip.exception.RoutingDecisionException - Exception class
com.dell.it.hip.exception.ThrottleLimitExceededException - Exception class

# ===== UTILITY CLASSES WITH MINIMAL LOGIC =====
# These are simple utility classes with basic functionality

com.dell.it.hip.util.ObjectMapperSingleton - Singleton utility class
com.dell.it.hip.util.ThrottleSettingsConverter - Simple converter utility
com.dell.it.hip.util.HeaderFilterUtil - Simple utility class
com.dell.it.hip.util.adapters.HeaderMapperUtil - Simple utility class
com.dell.it.hip.util.adapters.IbmmqHeaderMapperUtil - Simple utility class
com.dell.it.hip.util.adapters.KafkaHeaderMapperUtil - Simple utility class
com.dell.it.hip.util.adapters.RabbitMqHeaderMapperUtil - Simple utility class
com.dell.it.hip.util.adapters.SftpHeaderMapperUtil - Simple utility class
com.dell.it.hip.util.dataformatUtils.RegexUtil - Simple utility class
com.dell.it.hip.util.logging.TransactionErrorEvent - Event class

# ===== INTERFACE/ABSTRACT CLASSES =====
# These are interfaces or abstract classes with minimal implementation

com.dell.it.hip.strategy.adapters.InputAdapterStrategy - Interface
com.dell.it.hip.strategy.handlers.HandlerStrategy - Interface
com.dell.it.hip.strategy.flows.FlowStepStrategy - Interface

# ===== MAIN APPLICATION CLASS =====
# Application startup class with minimal logic

com.dell.it.hip.HipServicesApplication - Main application class with only startup logic

# ===== CONSTANTS/ENUM CLASSES =====
# Classes that only contain constants or enum values

com.dell.it.hip.util.contivoUtils.Constant - Constants class
