package com.dell.it.hip.util.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class HIPRedisHashServiceTest {

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private RedisTemplate<String, byte[]> byteRedisTemplate;

    @Mock
    private HashOperations<String, Object, Object> stringHashOps;

    @Mock
    private HashOperations<String, Object, Object> byteHashOps;

    private HIPRedisHashService hashService;
    private final String testCacheKey = "TEST-CACHE";

    @BeforeEach
    void setUp() {
        lenient().when(stringRedisTemplate.opsForHash()).thenReturn(stringHashOps);
        lenient().when(byteRedisTemplate.opsForHash()).thenReturn(byteHashOps);

        hashService = new HIPRedisHashService(stringRedisTemplate, byteRedisTemplate, testCacheKey);
    }

    @Test
    void testSetAndGet() {
        // Given
        String field = "test-field";
        String value = "test-value";
        lenient().when(stringHashOps.get(testCacheKey, field)).thenReturn(value);

        // When
        hashService.set(field, value);
        String result = hashService.get(field);

        // Then
        verify(stringHashOps).put(testCacheKey, field, value);
        assertEquals(value, result);
    }

    @Test
    void testSetWithExpiration() {
        // Given
        String field = "test-field";
        String value = "test-value";
        Duration timeout = Duration.ofMinutes(5);
        long currentTime = System.currentTimeMillis();
        
        // Mock current time for expiration calculation
        lenient().when(stringHashOps.get(testCacheKey, field + ":expires")).thenReturn(null);
        lenient().when(stringHashOps.get(testCacheKey, field)).thenReturn(value);

        // When
        hashService.set(field, value, timeout);
        String result = hashService.get(field);

        // Then
        verify(stringHashOps).put(testCacheKey, field, value);
        verify(stringHashOps).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertEquals(value, result);
    }

    @Test
    void testGetExpiredField() {
        // Given
        String field = "expired-field";
        String value = "expired-value";
        long pastTime = System.currentTimeMillis() - 10000; // 10 seconds ago
        
        lenient().when(stringHashOps.get(testCacheKey, field + ":expires")).thenReturn(String.valueOf(pastTime));
        lenient().when(stringHashOps.get(testCacheKey, field)).thenReturn(value);

        // When
        String result = hashService.get(field);

        // Then
        verify(stringHashOps).delete(testCacheKey, field, field + ":expires");
        assertNull(result);
    }

    @Test
    void testIncrement() {
        // Given
        String field = "counter-field";
        Long expectedValue = 5L;
        lenient().when(stringHashOps.increment(testCacheKey, field, 1)).thenReturn(expectedValue);

        // When
        Long result = hashService.increment(field);

        // Then
        verify(stringHashOps).increment(testCacheKey, field, 1);
        assertEquals(expectedValue, result);
    }

    @Test
    void testIncrementByDelta() {
        // Given
        String field = "counter-field";
        long delta = 10L;
        Long expectedValue = 15L;
        lenient().when(stringHashOps.increment(testCacheKey, field, delta)).thenReturn(expectedValue);

        // When
        Long result = hashService.increment(field, delta);

        // Then
        verify(stringHashOps).increment(testCacheKey, field, delta);
        assertEquals(expectedValue, result);
    }

    @Test
    void testSetIfAbsent() {
        // Given
        String field = "new-field";
        String value = "new-value";
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(true);

        // When
        Boolean result = hashService.setIfAbsent(field, value);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        assertTrue(result);
    }

    @Test
    void testSetIfAbsentWithExpiration() {
        // Given
        String field = "new-field";
        String value = "new-value";
        Duration timeout = Duration.ofMinutes(1);
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(true);

        // When
        Boolean result = hashService.setIfAbsent(field, value, timeout);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        verify(stringHashOps).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertTrue(result);
    }

    @Test
    void testSetIfAbsentFailsWhenFieldExists() {
        // Given
        String field = "existing-field";
        String value = "new-value";
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(false);

        // When
        Boolean result = hashService.setIfAbsent(field, value);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        verify(stringHashOps, never()).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertFalse(result);
    }

    @Test
    void testByteOperations() {
        // Given
        String field = "byte-field";
        byte[] value = "byte-value".getBytes();
        lenient().when(byteHashOps.get(testCacheKey, field)).thenReturn(value);

        // When
        hashService.setBytes(field, value);
        byte[] result = hashService.getBytes(field);

        // Then
        verify(byteHashOps).put(testCacheKey, field, value);
        assertArrayEquals(value, result);
    }

    @Test
    void testHasField() {
        // Given
        String field = "test-field";
        lenient().when(stringHashOps.hasKey(testCacheKey, field)).thenReturn(true);

        // When
        Boolean result = hashService.hasField(field);

        // Then
        verify(stringHashOps).hasKey(testCacheKey, field);
        assertTrue(result);
    }

    @Test
    void testDeleteSingleField() {
        // Given
        String field = "test-field";
        lenient().when(stringHashOps.delete(testCacheKey, field, field + ":expires")).thenReturn(1L);

        // When
        Long result = hashService.delete(field);

        // Then
        verify(stringHashOps).delete(testCacheKey, field, field + ":expires");
        assertEquals(1L, result);
    }

    @Test
    void testDeleteMultipleFields() {
        // Given
        String[] fields = {"field1", "field2", "field3"};
        String[] allFields = {"field1", "field1:expires", "field2", "field2:expires", "field3", "field3:expires"};
        lenient().when(stringHashOps.delete(eq(testCacheKey), any(Object[].class))).thenReturn(3L);

        // When
        Long result = hashService.delete(fields);

        // Then
        verify(stringHashOps).delete(eq(testCacheKey), any(Object[].class));
        assertEquals(3L, result);
    }

    @Test
    void testGetFields() {
        // Given
        Set<Object> expectedFields = Set.of("field1", "field2", "field3");
        lenient().when(stringHashOps.keys(testCacheKey)).thenReturn(expectedFields);

        // When
        Set<Object> result = hashService.getFields();

        // Then
        verify(stringHashOps).keys(testCacheKey);
        assertEquals(expectedFields, result);
    }

    @Test
    void testGetAll() {
        // Given
        Map<Object, Object> expectedEntries = new HashMap<>();
        expectedEntries.put("field1", "value1");
        expectedEntries.put("field2", "value2");
        lenient().when(stringHashOps.entries(testCacheKey)).thenReturn(expectedEntries);

        // When
        Map<Object, Object> result = hashService.getAll();

        // Then
        verify(stringHashOps).entries(testCacheKey);
        assertEquals(expectedEntries, result);
    }

    @Test
    void testSize() {
        // Given
        Long expectedSize = 10L;
        lenient().when(stringHashOps.size(testCacheKey)).thenReturn(expectedSize);

        // When
        Long result = hashService.size();

        // Then
        verify(stringHashOps).size(testCacheKey);
        assertEquals(expectedSize, result);
    }

    @Test
    void testGetCacheHashKey() {
        // When
        String result = hashService.getCacheHashKey();

        // Then
        assertEquals(testCacheKey, result);
    }

    @Test
    void testCleanupExpiredFields() {
        // Given
        Map<Object, Object> entries = new HashMap<>();
        entries.put("field1", "value1");
        entries.put("field1:expires", String.valueOf(System.currentTimeMillis() - 1000)); // expired
        entries.put("field2", "value2");
        entries.put("field2:expires", String.valueOf(System.currentTimeMillis() + 10000)); // not expired
        
        lenient().when(stringHashOps.entries(testCacheKey)).thenReturn(entries);

        // When
        hashService.cleanupExpiredFields();

        // Then
        verify(stringHashOps).delete(testCacheKey, "field1", "field1:expires");
        verify(stringHashOps, never()).delete(testCacheKey, "field2", "field2:expires");
    }

    @Test
    void testSetWithTimeUnit() {
        // Given
        String field = "test-field";
        String value = "test-value";
        long timeout = 30;
        TimeUnit unit = TimeUnit.SECONDS;

        // When
        hashService.set(field, value, timeout, unit);

        // Then
        verify(stringHashOps).put(testCacheKey, field, value);
        verify(stringHashOps).put(eq(testCacheKey), eq(field + ":expires"), anyString());
    }

    @Test
    void testSetIfAbsentWithTimeUnit() {
        // Given
        String field = "new-field";
        String value = "new-value";
        long timeout = 60;
        TimeUnit unit = TimeUnit.SECONDS;
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(true);

        // When
        Boolean result = hashService.setIfAbsent(field, value, timeout, unit);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        verify(stringHashOps).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertTrue(result);
    }

    // ========== EXCEPTION HANDLING TESTS ==========

    @Test
    void testSetThrowsException() {
        // Given
        String field = "test-field";
        String value = "test-value";
        doThrow(new RuntimeException("Redis error")).when(stringHashOps).put(testCacheKey, field, value);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.set(field, value);
        });
        assertEquals("Failed to set hash field", exception.getMessage());
    }

    @Test
    void testGetThrowsException() {
        // Given
        String field = "test-field";
        when(stringHashOps.get(testCacheKey, field + ":expires")).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.get(field);
        });
        assertEquals("Failed to get hash field", exception.getMessage());
    }

    @Test
    void testIncrementThrowsException() {
        // Given
        String field = "counter-field";
        when(stringHashOps.increment(testCacheKey, field, 1)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.increment(field);
        });
        assertEquals("Failed to increment hash field", exception.getMessage());
    }

    @Test
    void testIncrementByDeltaThrowsException() {
        // Given
        String field = "counter-field";
        long delta = 5L;
        when(stringHashOps.increment(testCacheKey, field, delta)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.increment(field, delta);
        });
        assertEquals("Failed to increment hash field", exception.getMessage());
    }

    @Test
    void testSetIfAbsentThrowsException() {
        // Given
        String field = "test-field";
        String value = "test-value";
        when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.setIfAbsent(field, value);
        });
        assertEquals("Failed to set if absent hash field", exception.getMessage());
    }

    @Test
    void testSetBytesThrowsException() {
        // Given
        String field = "byte-field";
        byte[] value = "test".getBytes();
        doThrow(new RuntimeException("Redis error")).when(byteHashOps).put(testCacheKey, field, value);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.setBytes(field, value);
        });
        assertEquals("Failed to set byte hash field", exception.getMessage());
    }

    @Test
    void testGetBytesThrowsException() {
        // Given
        String field = "byte-field";
        when(byteHashOps.get(testCacheKey, field)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.getBytes(field);
        });
        assertEquals("Failed to get byte hash field", exception.getMessage());
    }

    @Test
    void testHasFieldThrowsException() {
        // Given
        String field = "test-field";
        when(stringHashOps.hasKey(testCacheKey, field)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.hasField(field);
        });
        assertEquals("Failed to check hash field existence", exception.getMessage());
    }

    @Test
    void testDeleteSingleFieldThrowsException() {
        // Given
        String field = "test-field";
        when(stringHashOps.delete(testCacheKey, field, field + ":expires")).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.delete(field);
        });
        assertEquals("Failed to delete hash field", exception.getMessage());
    }

    @Test
    void testDeleteMultipleFieldsThrowsException() {
        // Given
        String[] fields = {"field1", "field2"};
        when(stringHashOps.delete(eq(testCacheKey), any(Object[].class))).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.delete(fields);
        });
        assertEquals("Failed to delete hash fields", exception.getMessage());
    }

    @Test
    void testGetFieldsThrowsException() {
        // Given
        when(stringHashOps.keys(testCacheKey)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.getFields();
        });
        assertEquals("Failed to get hash fields", exception.getMessage());
    }

    @Test
    void testGetAllThrowsException() {
        // Given
        when(stringHashOps.entries(testCacheKey)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.getAll();
        });
        assertEquals("Failed to get all hash entries", exception.getMessage());
    }

    @Test
    void testSizeThrowsException() {
        // Given
        when(stringHashOps.size(testCacheKey)).thenThrow(new RuntimeException("Redis error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.size();
        });
        assertEquals("Failed to get hash cache size", exception.getMessage());
    }

    // ========== EDGE CASES AND ADDITIONAL SCENARIOS ==========

    @Test
    void testGetWithNullValue() {
        // Given
        String field = "null-field";
        lenient().when(stringHashOps.get(testCacheKey, field + ":expires")).thenReturn(null);
        lenient().when(stringHashOps.get(testCacheKey, field)).thenReturn(null);

        // When
        String result = hashService.get(field);

        // Then
        assertNull(result);
    }

    @Test
    void testGetBytesWithNullValue() {
        // Given
        String field = "null-byte-field";
        lenient().when(byteHashOps.get(testCacheKey, field)).thenReturn(null);

        // When
        byte[] result = hashService.getBytes(field);

        // Then
        assertNull(result);
    }

    @Test
    void testSetBytesWithNullValue() {
        // Given
        String field = "null-byte-field";
        byte[] value = null;

        // When
        hashService.setBytes(field, value);

        // Then
        verify(byteHashOps).put(testCacheKey, field, value);
    }

    @Test
    void testSetIfAbsentWithExpirationWhenFieldExists() {
        // Given
        String field = "existing-field";
        String value = "new-value";
        Duration timeout = Duration.ofMinutes(1);
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(false);

        // When
        Boolean result = hashService.setIfAbsent(field, value, timeout);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        verify(stringHashOps, never()).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertFalse(result);
    }

    @Test
    void testSetIfAbsentWithTimeUnitWhenFieldExists() {
        // Given
        String field = "existing-field";
        String value = "new-value";
        long timeout = 60;
        TimeUnit unit = TimeUnit.SECONDS;
        lenient().when(stringHashOps.putIfAbsent(testCacheKey, field, value)).thenReturn(false);

        // When
        Boolean result = hashService.setIfAbsent(field, value, timeout, unit);

        // Then
        verify(stringHashOps).putIfAbsent(testCacheKey, field, value);
        verify(stringHashOps, never()).put(eq(testCacheKey), eq(field + ":expires"), anyString());
        assertFalse(result);
    }

    @Test
    void testCleanupExpiredFieldsWithException() {
        // Given
        when(stringHashOps.entries(testCacheKey)).thenThrow(new RuntimeException("Redis error"));

        // When & Then - should not throw exception, just log error
        assertDoesNotThrow(() -> hashService.cleanupExpiredFields());
    }

    @Test
    void testCleanupExpiredFieldsWithNoExpiredFields() {
        // Given
        Map<Object, Object> entries = new HashMap<>();
        entries.put("field1", "value1");
        entries.put("field2", "value2");
        // No expiration fields

        lenient().when(stringHashOps.entries(testCacheKey)).thenReturn(entries);

        // When
        hashService.cleanupExpiredFields();

        // Then
        verify(stringHashOps, never()).delete(eq(testCacheKey), anyString(), anyString());
    }

    @Test
    void testCleanupExpiredFieldsWithNonExpiredFields() {
        // Given
        Map<Object, Object> entries = new HashMap<>();
        entries.put("field1", "value1");
        entries.put("field1:expires", String.valueOf(System.currentTimeMillis() + 10000)); // not expired

        lenient().when(stringHashOps.entries(testCacheKey)).thenReturn(entries);

        // When
        hashService.cleanupExpiredFields();

        // Then
        verify(stringHashOps, never()).delete(testCacheKey, "field1", "field1:expires");
    }

    @Test
    void testGetWithExpirationParsingError() {
        // Given
        String field = "test-field";
        String value = "test-value";
        lenient().when(stringHashOps.get(testCacheKey, field + ":expires")).thenReturn("invalid-number");
        lenient().when(stringHashOps.get(testCacheKey, field)).thenReturn(value);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            hashService.get(field);
        });
        assertEquals("Failed to get hash field", exception.getMessage());
    }

    @Test
    void testDeleteEmptyFieldsArray() {
        // Given
        String[] fields = {};
        String[] allFields = {};
        lenient().when(stringHashOps.delete(eq(testCacheKey), any(Object[].class))).thenReturn(0L);

        // When
        Long result = hashService.delete(fields);

        // Then
        verify(stringHashOps).delete(eq(testCacheKey), any(Object[].class));
        assertEquals(0L, result);
    }

    @Test
    void testCleanupExpiredFieldsWithMixedFields() {
        // Given
        Map<Object, Object> entries = new HashMap<>();
        entries.put("field1", "value1");
        entries.put("field1:expires", String.valueOf(System.currentTimeMillis() - 1000)); // expired
        entries.put("field2", "value2");
        entries.put("field2:expires", String.valueOf(System.currentTimeMillis() + 10000)); // not expired
        entries.put("field3", "value3"); // no expiration
        entries.put("field4:expires", String.valueOf(System.currentTimeMillis() - 2000)); // expired

        lenient().when(stringHashOps.entries(testCacheKey)).thenReturn(entries);

        // When
        hashService.cleanupExpiredFields();

        // Then
        verify(stringHashOps).delete(testCacheKey, "field1", "field1:expires");
        verify(stringHashOps).delete(testCacheKey, "field4", "field4:expires");
        verify(stringHashOps, never()).delete(testCacheKey, "field2", "field2:expires");
        verify(stringHashOps, never()).delete(testCacheKey, "field3", "field3:expires");
    }
}
