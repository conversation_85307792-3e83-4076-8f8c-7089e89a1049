package com.dell.it.hip.core.registry;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StrictOrderMetricRegistry {

    private final HIPRedisCompatibilityService redisService;

    @Autowired
    public StrictOrderMetricRegistry(HIPRedisCompatibilityService redisService) {
        this.redisService = redisService;
    }

    private String getBaseKey(HIPIntegrationDefinition def, String type, String propertyRef) {
        return String.format("strictorder:%s:%s:%s:%s:%s",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                propertyRef,
                type);
    }

    public void incrementInOrder(HIPIntegrationDefinition def, String propertyRef) {
        redisService.increment(getBaseKey(def, "inorder", propertyRef));
    }
    public void incrementOutOfOrder(HIPIntegrationDefinition def, String propertyRef) {
        redisService.increment(getBaseKey(def, "outoforder", propertyRef));
    }
    public void incrementMissing(HIPIntegrationDefinition def, String propertyRef) {
        redisService.increment(getBaseKey(def, "missing", propertyRef));
    }
    public long getInOrderCount(HIPIntegrationDefinition def, String propertyRef) {
        String key = getBaseKey(def, "inorder", propertyRef);
        String val = redisService.get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
    public long getOutOfOrderCount(HIPIntegrationDefinition def, String propertyRef) {
        String key = getBaseKey(def, "outoforder", propertyRef);
        String val = redisService.get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
    public long getMissingCount(HIPIntegrationDefinition def, String propertyRef) {
        String key = getBaseKey(def, "missing", propertyRef);
        String val = redisService.get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
}
