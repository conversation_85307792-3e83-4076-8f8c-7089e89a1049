# FCXRefCallCmd Database Lookup Functionality - Technical Documentation

## Executive Summary

This document provides comprehensive technical documentation for the FCXRefCallCmd class database lookup functionality, specifically focusing on the execution path when `fc.fs.path` is null, which triggers database-based lookups using FCLookUp objects. This analysis covers the complete code flow, database configuration, schema requirements, SQL operations, and implementation guidance for client Java applications.

## 1. Code Flow Analysis

### 1.1 FCXRefCallCmd.init() Method Execution Flow

When `fc.fs.path` is passed as null, the following execution path occurs:

```java
private void init(IContivoRuntime icr) throws RuntimeMessageException {
    // Step 1: Check if client already initialized
    if (this.client != null) return;
    
    // Step 2: Initialize core components
    this.client = (LookUpClient)icr.getInstance(LookUpClient.class);
    this.props = (FCXRefProp)icr.getInstance(FCXRefProp.class);
    this.metrics = (IPerformanceMetrics)icr.getInstance(IPerformanceMetrics.class);
    
    // Step 3: Get user properties
    Properties prop = icr.getUserProperties();
    
    // Step 4: Initialize FCXRefProp if null
    if (this.props == null) {
        String solutionID = prop.getProperty("fc.solutionID");
        this.props = new FCXRefProp(solutionID);
    }
    
    // Step 5: Initialize LookUpClient (DATABASE PATH)
    if (this.client == null) {
        String fsPath = prop.getProperty("fc.fs.path"); // This is NULL
        if (fsPath != null) {
            // File system path - SKIPPED
        } else {
            // DATABASE INITIALIZATION PATH - EXECUTED
            String dbURL = prop.getProperty("fc.db.url");        // Required
            String driverName = prop.getProperty("fc.db.driver"); // Required
            String userName = prop.getProperty("fc.db.user");     // Optional
            String password = prop.getProperty("fc.db.password"); // Optional
            
            // Create database connection pool
            SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(
                dbURL, driverName, userName, password);
            
            // Create FCLookUp client with database pool
            this.client = (LookUpClient)new FCLookUp((DBPool)dbPool);
        }
    }
}
```

### 1.2 FCLookUp Object Creation and Initialization

The FCLookUp constructor initializes:
- **FCTableCache**: Guava-based table caching mechanism
- **FCValueCache**: Guava-based value caching mechanism  
- **DBPool**: Database connection pool
- **tableSizeThreshold**: Set to 100 rows (affects caching strategy)

### 1.3 Lookup Execution Flow

When a lookup is performed via `fcLookupValue()`:

1. **Cache Check**: Check if value exists in FCValueCache
2. **Table Cache Check**: Check if table exists in FCTableCache
3. **Database Query**: If not cached, execute database queries:
   - `getTableHeaders()` - Retrieve column headers
   - `getTableRows()` - Retrieve table data
   - `checkTableExist()` - Verify table existence
4. **Table Creation**: Create Table object with headers and rows
5. **Cache Storage**: Store table in FCTableCache
6. **Value Search**: Execute `table.findValue()` to locate result
7. **Result Caching**: Cache the lookup result in FCValueCache

## 2. Database Configuration

### 2.1 Required fc.db.* Properties

| Property | Required | Description | Example |
|----------|----------|-------------|---------|
| `fc.db.url` | **Yes** | JDBC database URL | `***********************************` |
| `fc.db.driver` | **Yes** | JDBC driver class name | `oracle.jdbc.driver.OracleDriver` |
| `fc.db.user` | No | Database username | `fulcrum_user` |
| `fc.db.password` | No | Database password | `password123` |
| `fc.solutionID` | **Yes** | Solution identifier for data partitioning | `PROD_SOLUTION_01` |

### 2.2 Oracle Database Support Confirmation

The system supports Oracle databases as evidenced by:
- JDBC URL format compatibility (`jdbc:oracle:thin:@...`)
- Oracle-specific SQL syntax in queries (e.g., `ROWNUM` usage)
- Driver class support for `oracle.jdbc.driver.OracleDriver`

### 2.3 Database Connection Management

The `SimpleConnectionToDBPool` class manages connections:
- Loads JDBC driver dynamically using `Class.forName(driverName).newInstance()`
- Creates connections using `driver.connect(url, properties)`
- Supports optional username/password authentication
- Implements connection pooling interface `DBPool`

## 3. Database Schema Requirements

### 3.1 Core Tables

The FCLookUp functionality requires three main tables:

#### 3.1.1 TGTS_LOOKUP Table
```sql
CREATE TABLE TGTS_LOOKUP (
    TABLE_ID        NUMBER PRIMARY KEY,
    SOLUTION_ID     VARCHAR2(255) NOT NULL,
    TABLE_NAME      VARCHAR2(255) NOT NULL,
    CREATE_DATE     TIMESTAMP NOT NULL,
    AVAILABLE_DATE  TIMESTAMP
);
```

**Purpose**: Stores metadata for lookup tables
**Key Columns**:
- `TABLE_ID`: Unique identifier for each table version
- `SOLUTION_ID`: Partitions data by solution
- `TABLE_NAME`: Logical name of the lookup table
- `CREATE_DATE`: Version timestamp for table data
- `AVAILABLE_DATE`: Optional availability date filter

#### 3.1.2 TGTS_LOOKUP_ROW Table
```sql
CREATE TABLE TGTS_LOOKUP_ROW (
    ROW_ID       NUMBER PRIMARY KEY,
    FK_TABLE_ID  NUMBER NOT NULL,
    ROW_TYPE     NUMBER NOT NULL,
    CONSTRAINT FK_LOOKUP_ROW_TABLE 
        FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)
);
```

**Purpose**: Stores row metadata for lookup tables
**Key Columns**:
- `ROW_ID`: Unique identifier for each row
- `FK_TABLE_ID`: References TGTS_LOOKUP.TABLE_ID
- `ROW_TYPE`: Distinguishes data rows (0) from header rows (1)

#### 3.1.3 TGTS_LOOKUP_COLUMN Table
```sql
CREATE TABLE TGTS_LOOKUP_COLUMN (
    FK_ROW_ID  NUMBER NOT NULL,
    COL_NUM    NUMBER NOT NULL,
    COL_VAL    VARCHAR2(4000),
    CONSTRAINT FK_LOOKUP_COL_ROW 
        FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID),
    CONSTRAINT PK_LOOKUP_COLUMN 
        PRIMARY KEY (FK_ROW_ID, COL_NUM)
);
```

**Purpose**: Stores actual column data for lookup tables
**Key Columns**:
- `FK_ROW_ID`: References TGTS_LOOKUP_ROW.ROW_ID
- `COL_NUM`: Column number (1-based indexing)
- `COL_VAL`: Column value (up to 4000 characters)

### 3.2 Sample Data Structure

#### Example: Customer Lookup Table

**TGTS_LOOKUP**:
```
TABLE_ID | SOLUTION_ID | TABLE_NAME | CREATE_DATE         | AVAILABLE_DATE
---------|-------------|------------|--------------------|--------------
1001     | PROD_SOL_01 | CUSTOMERS  | 2024-01-15 10:00:00| NULL
```

**TGTS_LOOKUP_ROW**:
```
ROW_ID | FK_TABLE_ID | ROW_TYPE
-------|-------------|----------
2001   | 1001        | 1        -- Header row
2002   | 1001        | 0        -- Data row 1
2003   | 1001        | 0        -- Data row 2
```

**TGTS_LOOKUP_COLUMN**:
```
FK_ROW_ID | COL_NUM | COL_VAL
----------|---------|----------
2001      | 1       | CUST_ID     -- Header
2001      | 2       | CUST_NAME   -- Header
2001      | 3       | REGION      -- Header
2002      | 1       | C001        -- Data
2002      | 2       | John Doe    -- Data
2002      | 3       | NORTH       -- Data
2003      | 1       | C002        -- Data
2003      | 2       | Jane Smith  -- Data
2003      | 3       | SOUTH       -- Data
```

## 4. SQL Queries and Operations

### 4.1 Table Existence Check Query
```sql
SELECT table_id 
FROM tgts_lookup lookup  
INNER JOIN (
    SELECT solution_id, table_name, MAX(create_date) AS max_create_date  
    FROM tgts_lookup 
    WHERE solution_id=? AND table_name=? 
    AND (available_date<=? OR available_date IS NULL) 
    GROUP BY solution_id, table_name
) grouped_lookup 
ON lookup.solution_id = grouped_lookup.solution_id 
AND lookup.table_name = grouped_lookup.table_name 
AND lookup.create_date = grouped_lookup.max_create_date
```

**Parameters**: 
1. `solutionID` (String)
2. `tableName` (String)  
3. `currentTimestamp` (Timestamp)

### 4.2 Header Retrieval Query
```sql
SELECT col_num, col_val 
FROM tgts_lookup_column 
WHERE fk_row_id IN (
    SELECT row_id 
    FROM tgts_lookup_row 
    WHERE fk_table_id IN ([TABLE_QUERY]) 
    AND ROW_TYPE=1
)
```

**Purpose**: Retrieves column headers for tables with header rows
**Returns**: Map<String, Integer> mapping column names to column numbers

### 4.3 Table Data Retrieval Query
```sql
SELECT col_num, col_val, fk_row_id 
FROM tgts_lookup_column 
WHERE fk_row_id IN (
    SELECT row_id 
    FROM tgts_lookup_row 
    WHERE fk_table_id IN ([TABLE_QUERY]) 
    AND ROW_TYPE=0 
    AND ROWNUM <= ?
)
```

**Parameters**: 
1. `solutionID` (String)
2. `tableName` (String)
3. `currentTimestamp` (Timestamp)  
4. `tableSizeThreshold + 1` (Integer) - Default: 101

**Returns**: List<Map<Integer, String>> representing table rows

### 4.4 Direct Value Lookup Query
```sql
SELECT tbl.table_id, cl.col_val 
FROM (
    [TABLE_QUERY]
) tbl 
LEFT JOIN (
    SELECT lookup1.table_id, colRet.col_val 
    FROM tgts_lookup lookup1, tgts_lookup_row row1, 
         [DYNAMIC_WHERE_COLUMNS] tgts_lookup_column colRet 
    WHERE lookup1.solution_id=? AND lookup1.table_name=? 
    AND lookup1.table_id=row1.fk_table_id 
    AND [DYNAMIC_CONDITIONS]
    ORDER BY lookup1.create_date DESC
) cl ON tbl.table_id = cl.table_id
```

**Dynamic Parameters**: Based on search criteria and operations
**Purpose**: Performs direct database lookup for large tables (>100 rows)

## 5. Client Implementation Guide

### 5.1 Required Dependencies

Add to your `pom.xml` or build configuration:
```xml
<dependencies>
    <!-- Oracle JDBC Driver -->
    <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>21.7.0.0</version>
    </dependency>
    
    <!-- Guava for caching (if using FCLookUp) -->
    <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>31.1-jre</version>
    </dependency>
</dependencies>
```

### 5.2 Basic Implementation Example

```java
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

public class FCLookupClientExample {
    
    // Configuration properties
    private static final String DB_URL = "***********************************";
    private static final String DB_DRIVER = "oracle.jdbc.driver.OracleDriver";
    private static final String DB_USER = "fulcrum_user";
    private static final String DB_PASSWORD = "password123";
    private static final String SOLUTION_ID = "PROD_SOLUTION_01";
    
    public static void main(String[] args) {
        try {
            // Initialize database connection pool
            SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(
                DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
            
            // Create FCLookUp client
            FCLookUp lookupClient = new FCLookUp(dbPool);
            
            // Perform lookup
            String result = lookupClient.lookupValue(
                SOLUTION_ID,           // Solution ID
                "CUSTOMERS",           // Table name
                new String[]{"1"},     // Find column (column 1)
                "2",                   // Result column (column 2)
                new String[]{"C001"},  // Find value
                new Op[]{Op.EQ},       // Operation (equals)
                false                  // Use column numbers (not names)
            );
            
            System.out.println("Lookup result: " + result);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

### 5.3 Configuration Properties Setup

Create a properties file `fc-config.properties`:
```properties
# Database Configuration
fc.db.url=***********************************
fc.db.driver=oracle.jdbc.driver.OracleDriver
fc.db.user=fulcrum_user
fc.db.password=password123

# Solution Configuration  
fc.solutionID=PROD_SOLUTION_01

# Force database mode (set fs.path to null)
fc.fs.path=
```

### 5.4 Properties-Based Initialization

```java
public class PropertiesBasedLookup {
    
    public static FCLookUp initializeLookupClient(Properties props) 
            throws RuntimeMessageException {
        
        // Validate required properties
        String dbURL = Optional.ofNullable(props.getProperty("fc.db.url"))
            .orElseThrow(() -> new RuntimeException("fc.db.url is required"));
            
        String driverName = Optional.ofNullable(props.getProperty("fc.db.driver"))
            .orElseThrow(() -> new RuntimeException("fc.db.driver is required"));
            
        String userName = props.getProperty("fc.db.user");
        String password = props.getProperty("fc.db.password");
        
        // Create connection pool
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(
            dbURL, driverName, userName, password);
            
        // Return FCLookUp client
        return new FCLookUp(dbPool);
    }
}
```

### 5.5 Advanced Usage with Column Names

```java
// Lookup using column names instead of numbers
String customerName = lookupClient.lookupValue(
    SOLUTION_ID,                    // Solution ID
    "CUSTOMERS",                    // Table name  
    new String[]{"CUST_ID"},        // Find column name
    "CUST_NAME",                    // Result column name
    new String[]{"C001"},           // Find value
    new Op[]{Op.EQ},               // Operation
    true                           // Use column names
);
```

### 5.6 Multiple Criteria Lookup

```java
// Lookup with multiple search criteria
String result = lookupClient.lookupValue(
    SOLUTION_ID,                           // Solution ID
    "CUSTOMERS",                           // Table name
    new String[]{"CUST_ID", "REGION"},     // Multiple find columns
    "CUST_NAME",                           // Result column
    new String[]{"C001", "NORTH"},         // Multiple find values
    new Op[]{Op.EQ, Op.EQ},               // Operations for each criteria
    true                                   // Use column names
);
```

## 6. Performance Considerations

### 6.1 Caching Strategy
- Tables ≤100 rows: Fully cached in memory
- Tables >100 rows: Direct database queries with value caching
- Cache keys include all lookup parameters for precise matching

### 6.2 Connection Management
- Connection pooling via `SimpleConnectionToDBPool`
- Automatic connection cleanup with try-with-resources pattern
- Driver instance reuse for multiple connections

### 6.3 Query Optimization
- Versioned table queries using MAX(create_date)
- ROWNUM limiting for large table previews
- Indexed lookups on solution_id and table_name recommended

## 7. Error Handling and Troubleshooting

### 7.1 Common Configuration Errors
- **Missing fc.db.url**: "Please specify the fc.db.url in Tools->Options->User Properties Manager"
- **Missing fc.db.driver**: "Please specify the fc.db.driver in Tools->Options->User Properties Manager"  
- **Invalid driver**: "Unable to find driver class: [driver_name]"
- **Missing solutionID**: "Please specify the fc.solutionID in Tools->Options->User Properties Manager"

### 7.2 Runtime Errors
- **Table not found**: "Table '[table_name]' in solutionID '[solution_id]' does not exist"
- **Column not found**: "Expected a column name for parameter [param]. However '[column]' does not match the name of any columns"
- **No header row**: "Table [table] does not have a header row, but you are attempting to do a lookup by column name"

### 7.3 Database Connection Issues
- Verify Oracle JDBC driver is in classpath
- Check database connectivity and credentials
- Ensure database schema exists and is accessible
- Validate JDBC URL format for your Oracle instance

This completes the core technical documentation. The implementation follows the exact patterns used in the FCXRefCallCmd class, providing a solid foundation for client Java applications requiring database-based lookup functionality.
