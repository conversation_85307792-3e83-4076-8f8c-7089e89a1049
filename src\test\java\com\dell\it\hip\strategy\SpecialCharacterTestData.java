package com.dell.it.hip.strategy;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class providing standardized test data for special character testing
 * across all HIP adapters and handlers.
 * 
 * This class centralizes test data to ensure consistency across all test suites
 * and provides helper methods for character encoding validation.
 */
public final class SpecialCharacterTestData {
    
    private SpecialCharacterTestData() {
        // Utility class - prevent instantiation
    }
    
    // ========== UNICODE CHARACTER TEST DATA ==========
    
    /**
     * Basic Unicode characters with Latin accents and diacritics
     */
    public static final String UNICODE_BASIC = "Hello World with accents: àáâãäåæçèéêë";
    
    /**
     * Extended Unicode with various language scripts
     */
    public static final String UNICODE_EXTENDED = "English, Español, Français, Deutsch, 中文, 日本語, العربية, Русский";
    
    /**
     * Cyrillic text (Russian)
     */
    public static final String CYRILLIC_TEXT = "Привет мир! Это тест кодировки.";
    
    /**
     * Chinese text (Simplified)
     */
    public static final String CHINESE_TEXT = "你好世界！这是编码测试。";
    
    /**
     * Japanese text (Hiragana and Kanji)
     */
    public static final String JAPANESE_TEXT = "こんにちは世界！これはエンコーディングテストです。";
    
    /**
     * Arabic text (right-to-left)
     */
    public static final String ARABIC_TEXT = "مرحبا بالعالم! هذا اختبار التشفير.";
    
    // ========== EMOJI CHARACTER TEST DATA ==========
    
    /**
     * Common emoji characters
     */
    public static final String EMOJI_FACES = "😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌";
    
    /**
     * Emoji symbols and objects
     */
    public static final String EMOJI_SYMBOLS = "❤️💙💚💛💜🖤🤍🤎💯✨🎯🚀📱💻🔧⚙️🛠️";
    
    /**
     * Status and reaction emojis
     */
    public static final String EMOJI_STATUS = "Status: ✅ Success! 🎉 Processing complete 🚀";
    
    /**
     * Mixed emoji payload for comprehensive testing
     */
    public static final String EMOJI_MIXED = "Test 🧪 Results: ✅ Pass ❌ Fail ⚠️ Warning 📊 Data 🔍 Analysis";
    
    // ========== SPECIAL SYMBOL TEST DATA ==========
    
    /**
     * XML/HTML special characters and entities
     */
    public static final String XML_SPECIAL_CHARS = "<tag attr=\"value\">&amp;&lt;&gt;&quot;&#39;</tag>";
    
    /**
     * JSON special characters and escape sequences
     */
    public static final String JSON_SPECIAL_CHARS = "{\"message\": \"Hello \\\"World\\\"\", \"symbols\": \"{}[]:,\"}";
    
    /**
     * SQL injection prevention characters
     */
    public static final String SQL_SPECIAL_CHARS = "'; DROP TABLE users; --";
    
    /**
     * Regular expression metacharacters
     */
    public static final String REGEX_SPECIAL_CHARS = ".*+?^${}()|[]\\";
    
    /**
     * URL encoding test characters
     */
    public static final String URL_SPECIAL_CHARS = "Hello World! @#$%^&*()+={}[]|\\:;\"'<>?,./";
    
    // ========== ZERO-WIDTH AND COMBINING CHARACTERS ==========
    
    /**
     * Zero-width characters (invisible but present)
     */
    public static final String ZERO_WIDTH_CHARS = "Text\u200Bwith\u200Czero\u200Dwidth\u2060chars";
    
    /**
     * Combining characters (accents applied to base characters)
     */
    public static final String COMBINING_CHARS = "e\u0301"; // e + acute accent = é
    
    /**
     * Normalization test data (same visual result, different Unicode representation)
     */
    public static final String NORMALIZATION_NFC = "é"; // Single character
    public static final String NORMALIZATION_NFD = "e\u0301"; // Base + combining
    
    // ========== MIXED CONTENT TEST DATA ==========
    
    /**
     * Mixed content with various character types
     */
    public static final String MIXED_CONTENT = "Mixed content: Hello 世界 🌍 with symbols: ©®™ and math: ∑∏∆";
    
    /**
     * Complex mixed payload for comprehensive testing
     */
    public static final String COMPLEX_MIXED = "🌍 Global Test: English + 中文 + العربية + Русский + 日本語 + Español + Français <>&\"'";
    
    // ========== LARGE CONTENT GENERATORS ==========
    
    /**
     * Generate large Unicode content for performance testing
     * @param lines Number of lines to generate
     * @return Large string with Unicode content
     */
    public static String generateLargeUnicodeContent(int lines) {
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < lines; i++) {
            content.append("Line ").append(i).append(": ")
                   .append("Unicode text 中文 🌍 with symbols ©®™ ")
                   .append("and emoji 😀😃😄 ")
                   .append("Arabic: مرحبا ")
                   .append("Russian: Привет\n");
        }
        return content.toString();
    }
    
    /**
     * Generate large emoji content for testing
     * @param count Number of emoji repetitions
     * @return String with repeated emoji patterns
     */
    public static String generateLargeEmojiContent(int count) {
        StringBuilder content = new StringBuilder();
        String emojiPattern = "🚀🎯📱💻🔧⚙️🛠️✨💯❤️";
        for (int i = 0; i < count; i++) {
            content.append("Emoji set ").append(i).append(": ").append(emojiPattern).append(" ");
        }
        return content.toString();
    }
    
    // ========== TEST DATA COLLECTIONS ==========
    
    /**
     * All basic test strings for iteration
     */
    public static final List<String> ALL_BASIC_TESTS = Arrays.asList(
        UNICODE_BASIC,
        UNICODE_EXTENDED,
        EMOJI_STATUS,
        XML_SPECIAL_CHARS,
        JSON_SPECIAL_CHARS,
        ZERO_WIDTH_CHARS,
        MIXED_CONTENT
    );
    
    /**
     * All language-specific test strings
     */
    public static final List<String> ALL_LANGUAGE_TESTS = Arrays.asList(
        CYRILLIC_TEXT,
        CHINESE_TEXT,
        JAPANESE_TEXT,
        ARABIC_TEXT
    );
    
    /**
     * All emoji test strings
     */
    public static final List<String> ALL_EMOJI_TESTS = Arrays.asList(
        EMOJI_FACES,
        EMOJI_SYMBOLS,
        EMOJI_STATUS,
        EMOJI_MIXED
    );
    
    /**
     * All special character test strings
     */
    public static final List<String> ALL_SPECIAL_CHAR_TESTS = Arrays.asList(
        XML_SPECIAL_CHARS,
        JSON_SPECIAL_CHARS,
        SQL_SPECIAL_CHARS,
        REGEX_SPECIAL_CHARS,
        URL_SPECIAL_CHARS
    );
    
    // ========== VALIDATION HELPER METHODS ==========
    
    /**
     * Validate that a string maintains its Unicode integrity after processing
     * @param original Original string
     * @param processed Processed string
     * @return true if strings are identical
     */
    public static boolean validateUnicodeIntegrity(String original, String processed) {
        if (original == null && processed == null) return true;
        if (original == null || processed == null) return false;
        return original.equals(processed);
    }
    
    /**
     * Validate that byte array conversion preserves Unicode characters
     * @param original Original string
     * @param bytes Byte array representation
     * @return true if conversion is lossless
     */
    public static boolean validateByteConversion(String original, byte[] bytes) {
        if (original == null) return bytes.length == 0;
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        return original.equals(reconstructed);
    }
    
    /**
     * Check if a string contains any replacement characters (indicating encoding issues)
     * @param text Text to check
     * @return true if replacement characters are found
     */
    public static boolean containsReplacementCharacters(String text) {
        return text != null && text.contains("\uFFFD");
    }
    
    /**
     * Get byte length of string in UTF-8 encoding
     * @param text Input string
     * @return Byte length in UTF-8
     */
    public static int getUtf8ByteLength(String text) {
        return text == null ? 0 : text.getBytes(StandardCharsets.UTF_8).length;
    }
    
    /**
     * Check if string contains only ASCII characters
     * @param text Input string
     * @return true if ASCII-only
     */
    public static boolean isAsciiOnly(String text) {
        if (text == null) return true;
        return text.chars().allMatch(c -> c < 128);
    }
    
    /**
     * Count Unicode code points in string (different from character count for emojis)
     * @param text Input string
     * @return Number of Unicode code points
     */
    public static int countCodePoints(String text) {
        return text == null ? 0 : text.codePointCount(0, text.length());
    }
}
