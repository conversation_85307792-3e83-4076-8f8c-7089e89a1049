package com.dell.it.hip.util.contivoUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.FlowSteps.CrossReferenceData;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.exception.MessageTransformationException;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.ContivoMapCache;

@ExtendWith(MockitoExtension.class)
public class ContivoTransformerServiceTest {

    @InjectMocks
    private ContivoTransformerService transformerService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private WiretapService wiretapService;

    @Mock
    private ContivoMapCache contivoMapCache;

    @Mock
    private CacheClassLoader cacheClassLoader;

    @Mock
    private Message<String> message;

    @Mock
    private HIPIntegrationDefinition definition;

    @Mock
    private FlowStepConfigRef stepConfigRef;

    private ContivoMapConfig contivoMap;

    @BeforeEach
    public void setup() {
        contivoMap = new ContivoMapConfig();
        contivoMap.setMapClass("com.test.TestTransformer");
        contivoMap.setMapName("TestMap");
        contivoMap.setRedisMapDataKey("test-key");

        //ContivoMapConfig.CrossReferenceData crossRefData = new ContivoMapConfig.CrossReferenceData();
        CrossReferenceData crossRefData = new CrossReferenceData();
        crossRefData.setCrossReferenceFilePath("path/to/ref");
        Map<String, String> mapParams = new HashMap<>();
        mapParams.put("param1", "value1");
        crossRefData.setCrossRefMapParams(mapParams);
        contivoMap.setCrossReferenceData(crossRefData);

        when(applicationContext.getBean("cacheClassLoader")).thenReturn(cacheClassLoader);
        lenient().when(message.getPayload()).thenReturn("{\"test\":\"data\"}");
    }

    @Test
    public void shouldHandleNullMapClass() {
        contivoMap.setMapClass(null);

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleInvalidClassName() {
        contivoMap.setMapClass("invalid.ClassName");

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleNullRedisKey() throws Exception {
        contivoMap.setRedisMapDataKey(null);
        byte[] fakeBytes = new byte[]{};

        // force fallback path
        when(contivoMapCache.loadMapData(null)).thenReturn(fakeBytes);

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleEmptyPayload() {
    	lenient().when(message.getPayload()).thenReturn("");

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    void shouldReturnTransformedPayload() throws Exception {
        Message<String> message = MessageBuilder.withPayload("test").build();

        HIPIntegrationDefinition def = new HIPIntegrationDefinition();
        FlowStepConfigRef stepConfigRef = new FlowStepConfigRef();
        ContivoMapConfig contivoMap = new ContivoMapConfig();
        contivoMap.setMapClass("com.test.TestTransformer");
        contivoMap.setRedisMapDataKey("mock-key");
        contivoMap.setMapName("MockMap");

        // Create and set up CrossReferenceData properly
        CrossReferenceData crossRefData = new CrossReferenceData();
        crossRefData.setCrossReferenceFilePath("path/to/ref");
        Map<String, String> mapParams = new HashMap<>();
        mapParams.put("key1", "value1");
        mapParams.put("key2", "value2");
        crossRefData.setCrossRefMapParams(mapParams);
        contivoMap.setCrossReferenceData(crossRefData);

        byte[] mockJar = Files.readAllBytes(Paths.get("src/test/resources/mock.jar"));

        when(applicationContext.getBean("cacheClassLoader")).thenReturn(cacheClassLoader);
        when(contivoMapCache.loadMapData("mock-key")).thenReturn(mockJar);

        // The test should handle ClassNotFoundException gracefully and return the original payload
        // This tests that the null pointer exception is fixed and the method doesn't crash
        assertThrows(Exception.class, () -> {
            transformerService.transform(message, stepConfigRef, def, contivoMap);
        });

        // Verify that the cross reference data was accessed without null pointer exception
        assertNotNull(contivoMap.getCrossReferenceData());
        assertNotNull(contivoMap.getCrossReferenceData().getCrossRefMapParams());
        assertEquals(2, contivoMap.getCrossReferenceData().getCrossRefMapParams().size());
    }

    @Test
    public void shouldHandleNullCrossReferenceData() {
        contivoMap.setCrossReferenceData(null);

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleLargePayload() {
        String largePayload = "A".repeat(100000);
        lenient().when(message.getPayload()).thenReturn(largePayload);

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleJsonPayload() {
    	lenient().when(message.getPayload()).thenReturn("{\"field\":\"value\"}");

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleXmlPayload() {
    	lenient().when(message.getPayload()).thenReturn("<root><field>value</field></root>");

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    @Test
    public void shouldHandleMultipleCrossRefParams() {
        Map<String, String> mapParams = new HashMap<>();
        mapParams.put("a", "1");
        mapParams.put("b", "2");
        contivoMap.getCrossReferenceData().setCrossRefMapParams(mapParams);

        assertThrows(Exception.class, () ->
            transformerService.transform(message, stepConfigRef, definition, contivoMap)
        );
    }

    // Mock Transformer class to avoid actual loading
    public static class MockTransformer {}
}
