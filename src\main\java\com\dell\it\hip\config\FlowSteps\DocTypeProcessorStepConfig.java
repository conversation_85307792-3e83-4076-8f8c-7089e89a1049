package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = DocTypeProcessorStepConfigDeserializer.class)
public class DocTypeProcessorStepConfig extends FlowStepConfig {
    // For each format (JSON, XML, etc), which docTypes to check (in order)
    private Map<String, List<String>> supportedDocTypesPerFormat;
    private String genericDocType; // e.g. "GENERIC"

    public Map<String, List<String>> getSupportedDocTypesPerFormat() {
        return supportedDocTypesPerFormat;
    }

    public void setSupportedDocTypesPerFormat(Map<String, List<String>> supportedDocTypesPerFormat) {
        this.supportedDocTypesPerFormat = supportedDocTypesPerFormat;
    }

    public String getGenericDocType() {
        return genericDocType;
    }

    public void setGenericDocType(String genericDocType) {
        this.genericDocType = genericDocType;
    }

    public boolean isAllowGenericDocType() {
        return allowGenericDocType;
    }

    public void setAllowGenericDocType(boolean allowGenericDocType) {
        this.allowGenericDocType = allowGenericDocType;
    }

    public boolean isTerminateOnUnknownFormat() {
        return terminateOnUnknownFormat;
    }

    public void setTerminateOnUnknownFormat(boolean terminateOnUnknownFormat) {
        this.terminateOnUnknownFormat = terminateOnUnknownFormat;
    }

    public List<AttributeMapping> getAttributeMappings() {
        return attributeMappings;
    }

    public void setAttributeMappings(List<AttributeMapping> attributeMappings) {
        this.attributeMappings = attributeMappings;
    }

    public ValidationConfig getValidation() {
        return validation;
    }

    public void setValidation(ValidationConfig validation) {
        this.validation = validation;
    }

    private boolean allowGenericDocType;
    private boolean terminateOnUnknownFormat;
    private List<AttributeMapping> attributeMappings; // Extra, optional
    private ValidationConfig validation; // Fallback for generic

    // Getters & Setters (Lombok can be used in your project)
    // ...
}

/**
 * Custom deserializer to handle dot notation with array indices like:
 * "supportedDocTypesPerFormat.JSON[0]": "testdoc:1"
 * "supportedDocTypesPerFormat.JSON[1]": "testdoce1:1"
 *
 * Also handles attributeMappings as List<AttributeMapping> from JSON array format.
 */
class DocTypeProcessorStepConfigDeserializer extends JsonDeserializer<DocTypeProcessorStepConfig> {

    private static final Pattern DOT_NOTATION_PATTERN =
        Pattern.compile("supportedDocTypesPerFormat\\.([^\\[]+)\\[(\\d+)\\]");

    @Override
    public DocTypeProcessorStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {

        JsonNode node = parser.getCodec().readTree(parser);
        DocTypeProcessorStepConfig config = new DocTypeProcessorStepConfig();
        Map<String, List<String>> supportedDocTypesPerFormat = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        // Iterate through all fields in the JSON
        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            // Check if this field matches our dot notation pattern
            Matcher matcher = DOT_NOTATION_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                String format = matcher.group(1);  // e.g., "JSON"
                int index = Integer.parseInt(matcher.group(2));  // e.g., 0 or 1
                String value = fieldValue.asText();

                // Get or create the list for this format
                List<String> docTypes = supportedDocTypesPerFormat.computeIfAbsent(format, k -> new ArrayList<>());

                // Ensure the list is large enough for this index
                while (docTypes.size() <= index) {
                    docTypes.add(null);
                }

                // Set the value at the specified index
                docTypes.set(index, value);
            } else {
                // Handle regular properties
                switch (fieldName) {
                    case "genericDocType":
                        config.setGenericDocType(fieldValue.asText());
                        break;
                    case "allowGenericDocType":
                        config.setAllowGenericDocType(fieldValue.asBoolean());
                        break;
                    case "terminateOnUnknownFormat":
                        config.setTerminateOnUnknownFormat(fieldValue.asBoolean());
                        break;
                    case "attributeMappings":
                        // Handle List<AttributeMapping> deserialization
                        if (fieldValue.isArray()) {
                            List<AttributeMapping> attributeMappings = new ArrayList<>();
                            for (JsonNode arrayElement : fieldValue) {
                                AttributeMapping mapping = objectMapper.treeToValue(arrayElement, AttributeMapping.class);
                                attributeMappings.add(mapping);
                            }
                            config.setAttributeMappings(attributeMappings);
                        }
                        break;
                    case "validation":
                        // Handle ValidationConfig if present
                        if (!fieldValue.isNull()) {
                            ValidationConfig validation = objectMapper.treeToValue(fieldValue, ValidationConfig.class);
                            config.setValidation(validation);
                        }
                        break;
                    // Add other properties as needed from parent class FlowStepConfig
                    case "type":
                        config.setType(fieldValue.asText());
                        break;
                    case "id":
                        config.setId(fieldValue.asText());
                        break;
                    case "propertyRef":
                        config.setPropertyRef(fieldValue.asText());
                        break;
                    case "role":
                        config.setRole(fieldValue.asText());
                        break;
                }
            }
        }

        config.setSupportedDocTypesPerFormat(supportedDocTypesPerFormat);
        return config;
    }
}