package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.FlowSteps.ValidationFlowStepConfig.DefaultValidationConfig;
import com.dell.it.hip.config.FlowSteps.ValidationFlowStepConfig.DocTypeValidationConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Data;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonDeserialize(using = ValidationFlowStepConfigDeserializer.class)
public class ValidationFlowStepConfig extends FlowStepConfig {
	@JsonProperty("configurationList")
    private List<DocTypeValidationConfig> docTypeConfigs = new ArrayList<>();
    private DefaultValidationConfig defaultConfig;

    @Data
    public static class DocTypeValidationConfig extends DocTypeConfig {
    	@JsonProperty("action")
        private ValidationBehavior validationBehavior = ValidationBehavior.STRUCTURAL;
        private String schemaKey;       // Optionally provide per-doctype schema
        private String schemaType;
        //private ValidationConfig validation; // For future extension (optional)
    }

    @Data
    public static class DefaultValidationConfig extends DocTypeConfig{
    	@JsonProperty("action")
        private ValidationBehavior validationBehavior = ValidationBehavior.STRUCTURAL;
        private String schemaKey;
        private String schemaType;
        //private ValidationConfig validation; // For future extension (optional)
    }

    @Data
    public static class ValidationConfig {
        private boolean structural;
        private boolean schema;
        private String schemaType;      // e.g. "XSD", "JSONSCHEMA", "STAEDI"
        private String schemaKey;       // Redis key for schema
        // ...any more
    }

    public enum ValidationBehavior {
        SCHEMA, STRUCTURAL, SKIP, TERMINATE
    }
}

class ValidationFlowStepConfigDeserializer extends JsonDeserializer<ValidationFlowStepConfig> {

    @Override
    public ValidationFlowStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) parser.getCodec();
        JsonNode root = mapper.readTree(parser);

        ValidationFlowStepConfig result = new ValidationFlowStepConfig();

        Map<Integer, ObjectNode> configMap = new HashMap<>();
        ObjectNode defaultConfigNode = mapper.createObjectNode();

        Iterator<Map.Entry<String, JsonNode>> fields = root.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (key.startsWith("configurationList[")) {
                int indexStart = key.indexOf('[') + 1;
                int indexEnd = key.indexOf(']');
                int index = Integer.parseInt(key.substring(indexStart, indexEnd));
                String fieldName = key.substring(key.indexOf(']') + 2);

                ObjectNode node = configMap.computeIfAbsent(index, k -> mapper.createObjectNode());
                node.set(fieldName, value);

            } else if (key.startsWith("defaultConfig.")) {
                String fieldName = key.substring("defaultConfig.".length());
                defaultConfigNode.set(fieldName, value);

           /* } else if ("stepName".equals(key)) {
                result.setStepName(value.asText());
            } else if ("copyHeaders".equals(key)) {
                result.setCopyHeaders(value.asBoolean());
            } else if ("genericSplitting".equals(key)) {
                result.setGenericSplitting(value.asBoolean());
            } else if ("splitterRegex".equals(key)) {
                result.setSplitterRegex(value.asText());*/
            }
        }

        // Deserialize configurationList entries
        List<DocTypeValidationConfig> configList = new ArrayList<>();
        for (ObjectNode node : configMap.values()) {
        	DocTypeValidationConfig config = mapper.treeToValue(node, DocTypeValidationConfig.class);
            configList.add(config);
        }
        result.setDocTypeConfigs(configList);

        // Deserialize defaultConfig
        if (!defaultConfigNode.isEmpty()) {
        	DefaultValidationConfig defaultConfig = mapper.treeToValue(defaultConfigNode, DefaultValidationConfig.class);
            result.setDefaultConfig(defaultConfig);
        }

        return result;
    }
}