package com.dell.it.hip.util.contivoUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.contivo.mixedruntime.runtime.wrapper.Transformer;
import com.contivo.mixedruntime.runtime.wrapper.configuration.BaseTransformerConfiguration;
import com.contivo.mixedruntime.runtime.wrapper.configuration.TransformerConfiguration;
import com.contivo.mixedruntime.util.TransformMetaData;
import com.contivo.mixedruntime.util.classloaders.DummyRuntimeContext;
import com.contivo.mixedruntime.util.classloaders.RuntimeContext;
import com.contivo.options.TUserProperties;
import com.contivo.runtime.core.TTransformParam;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.strategy.flows.ContivoTransformer;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.ContivoMapCache;
import com.emc.it.eis.contivo.integration.transformer.DefaultTransformationResultHandler;
import com.emc.it.eis.contivo.integration.transformer.TransformationResultHandler;
import com.emc.it.eis.contivo.integration.transformer.TransformerContext;

import groovy.util.logging.Slf4j;

@Service
@Slf4j
public class ContivoTransformerService implements ContivoTransformer {
	
	private static final Logger logger = LoggerFactory.getLogger(ContivoTransformerService.class);
	
	private static String transformationServicesKey = "cic-svc";
	private TransformationResultHandler resultHandler = new DefaultTransformationResultHandler();
	private boolean includeTransformationContext;
	private Map<?, ?> transformationBeans;
	
	@Autowired
    private WiretapService wiretapService;
	
	@Autowired
	private ContivoMapCache contivoMapCache;
	
	private CacheClassLoader cacheClassLoader;
	
	@Autowired
	private ApplicationContext applicationContext;
	
	@Value("${map.cross.reference.file.path:/etc/config/mapCrossReferenceFiles}")
	private String crossRefFilePath;
	
	@Value("${map.load.registration:true}")
	private boolean loadMapReg;

	public String transform(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def, ContivoMapConfig contivoMap) throws Exception{
		// TODO Auto-generated method stub
		try {
			cacheClassLoader = (CacheClassLoader) applicationContext.getBean("cacheClassLoader");
			if(loadMapReg && cacheClassLoader!=null) {
				try {
					Class<?> transformClass = Class.forName(contivoMap.getMapClass(), true, cacheClassLoader);
					if(transformClass!=null) {
						String transformedPayload = getTransformedPayload(message.getPayload().toString(), contivoMap.getMapClass(), contivoMap.getCrossReferenceData().getCrossRefMapParams(), contivoMap.getCrossReferenceData().getCrossReferenceFilePath(), cacheClassLoader);
						return transformedPayload;
					}
				} catch (Exception e) {
					throw e;
				}	
			}else {
				CacheClassLoader cacheClassLoader = cacheClassLoader(contivoMap);
				String transformedPayload = getTransformedPayload(message.getPayload().toString(), contivoMap.getMapClass(), contivoMap.getCrossReferenceData().getCrossRefMapParams(), contivoMap.getCrossReferenceData().getCrossReferenceFilePath(), cacheClassLoader);
				return transformedPayload;
			}
		} catch (Exception ex) {
			emitWiretapError(message, def, stepConfigRef, ex.getMessage());
			throw ex;
		}
		return null;
	}
	
	private CacheClassLoader cacheClassLoader(ContivoMapConfig contivoMap) throws Exception {
		CacheClassLoader childClassLoader = new CacheClassLoader(Thread.currentThread().getContextClassLoader());
		byte[] classCode = contivoMapCache.loadMapData(contivoMap.getRedisMapDataKey());
		//saveByteArrayToFile(classCode,"C:\\Users\\<USER>\\Downloads\\Transform_TRANSACTION_to_85.jar");
		childClassLoader.addJAR(contivoMap.getMapName() + ".jar", classCode);
		logger.info("Dynamic contivo map loaded in memory with --- > " + contivoMap.getMapName());
		return childClassLoader;
	}
	
	private String getTransformedPayload(String payload, String MapClassName, Map<String,String> crossReferenceMapParameters, String crossMapPath, CacheClassLoader cacheClassLoader) throws Exception {

		try {
			logger.info("getTransformedPayload called -------------> ");
			if(crossReferenceMapParameters!=null) {
				TUserProperties properties = TUserProperties.getInstance();
				for (var entry : crossReferenceMapParameters.entrySet()) {
					logger.info("cross ref map details -------------> "+entry.getKey() + "/" + entry.getValue());
					properties.put(entry.getKey(), entry.getValue());
				}
				properties.put(Constant.CROSS_MAP_PATH_KEY, crossRefFilePath+"/"+crossMapPath);
			}
			Class<?> transformClass = Class.forName(MapClassName, true, cacheClassLoader);			
			TransformMetaData metadata = (TransformMetaData) TransformMetaData.createFromClass(transformClass).orElse(null);
			RuntimeContext dummyContext = new DummyRuntimeContext(transformClass.getClassLoader(), "DummyContext");
			TransformerConfiguration configuration = BaseTransformerConfiguration.create(dummyContext, metadata, null, null);

			Transformer transformer = new Transformer(configuration);
			TransformerContext context = populateParams(payload, transformer);
			List<String> target = transformer.toTargetString();
			this.resultHandler.process(transformer.getResults());
			Object results = createTransformationResultObject(context, target);
			logger.info("Transformed Payload is ---------------> " + results.toString());
			return results.toString();
		} catch (Exception e) {
			logger.error("Transformation Failed with below error -----> " + e.getMessage());
			e.printStackTrace();
			throw new Exception("Transformation Failed with below error -----> " + e.getMessage());
		}
	}
	
	private Object createTransformationResultObject(TransformerContext context, List<String> target) {
		Object results = null;
		if (context != null && this.includeTransformationContext) {
			List<Object> resultList = new ArrayList<>();
			resultList.add(target);
			resultList.add(context);
			results = (Object) resultList;
		} else {
			if (!ObjectUtils.isEmpty(target)) {
				String transformedDocument = target.get(0);
				results = transformedDocument;
			}
		}
		return results;
	}
	
	private TransformerContext populateParams(String source, Transformer transformer) throws Exception {
		List<Object> sourceList = new ArrayList<Object>();
		sourceList.add(source);
		return populateParams(sourceList, transformer);
	}
	private TransformerContext populateParams(List<Object> sources, Transformer transformer) throws Exception {
		TransformerContext context = null;
		TTransformParam params = new TTransformParam();
		params.setMaxErrors(10);
		params.setMaxWarnings(2);
		for (Object source : sources) {
			if (source instanceof String) {
				transformer.addSource(source);
			} 
			if (source instanceof TransformerContext transformerContext) {
				context = transformerContext;
				params.reg(TransformerContext.class, context);
			} 
		} 
		context = populateTransformationServices(context, params);
		return context;
	}

	private TransformerContext populateTransformationServices(TransformerContext context, TTransformParam params) {
		if (transformationBeans != null) {
			if (context != null) {
				context.getData().put(transformationServicesKey, transformationBeans);
			} else {
				context = new TransformerContext();
				context.getData().put(transformationServicesKey, transformationBeans);
				params.reg(TransformerContext.class, context);
			}
		}

		return context;
	}
	
	private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "MappingError", errorMsg);
    }

}
