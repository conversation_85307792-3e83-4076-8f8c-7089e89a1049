package com.dell.it.hip.config;

import com.dell.it.hip.config.HIPClusterEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${spring.data.redis.enabled:true}")
    private boolean redisEnabled;

    @Value("${redis.cache.name:HIP-DEFAULT-CACHE}")
    private String redisCacheName;

    /**
     * StringRedisTemplate - only created when Redis is enabled.
     * Configured for hash-based operations with proper serialization.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        logger.info("Creating StringRedisTemplate with Redis enabled: {} and cache name: {}", redisEnabled, redisCacheName);
        try {
            StringRedisTemplate template = new StringRedisTemplate(redisConnectionFactory);

            // Configure serializers for hash operations
            template.setKeySerializer(new StringRedisSerializer());
            template.setValueSerializer(new StringRedisSerializer());
            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(new StringRedisSerializer());

            // Test the connection
            template.getConnectionFactory().getConnection().ping();
            logger.info("Redis connection test successful for hash-based operations");
            return template;
        } catch (Exception e) {
            logger.error("Failed to create StringRedisTemplate: {}", e.getMessage(), e);
            throw new RuntimeException("Redis connection failed", e);
        }
    }
    
    /**
     * RedisTemplate for byte array operations - configured for hash-based storage.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public RedisTemplate<String, byte[]> byteRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        logger.info("Creating byte RedisTemplate with Redis enabled: {} and cache name: {}", redisEnabled, redisCacheName);
        try {
            RedisTemplate<String, byte[]> template = new RedisTemplate<>();
            template.setConnectionFactory(redisConnectionFactory);

            // Configure serializers for hash operations
            template.setKeySerializer(RedisSerializer.string());
            template.setValueSerializer(RedisSerializer.byteArray());
            template.setHashKeySerializer(RedisSerializer.string());
            template.setHashValueSerializer(RedisSerializer.byteArray());

            // Test the connection
            template.getConnectionFactory().getConnection().ping();
            logger.info("Redis byte template connection test successful for hash-based operations");
            return template;
        } catch (Exception e) {
            logger.error("Failed to create byte RedisTemplate: {}", e.getMessage(), e);
            throw new RuntimeException("Redis connection failed", e);
        }
    }

    /**
     * Redis Message Listener Container - only created when Redis is enabled.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }

	/**
	 * RedisTemplate for HIPClusterEvent operations - configured for hash-based storage.
	 */
	@Bean
	@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
	public RedisTemplate<String, HIPClusterEvent> hipEventRedisTemplate(RedisConnectionFactory factory) {
		logger.info("Creating HIPClusterEvent RedisTemplate with cache name: {}", redisCacheName);
		RedisTemplate<String, HIPClusterEvent> template = new RedisTemplate<>();
		template.setConnectionFactory(factory);

		// Configure serializers for hash operations
		template.setKeySerializer(new StringRedisSerializer());
		template.setValueSerializer(new Jackson2JsonRedisSerializer<>(HIPClusterEvent.class));
		template.setHashKeySerializer(new StringRedisSerializer());
		template.setHashValueSerializer(new Jackson2JsonRedisSerializer<>(HIPClusterEvent.class));

		template.afterPropertiesSet();
		return template;
	}

	/**
	 * Expose the redis.cache.name property as a bean for injection into services.
	 */
	@Bean("redisCacheName")
	@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
	public String redisCacheName() {
		logger.info("Redis cache name configured: {}", redisCacheName);
		return redisCacheName;
	}
}