package com.dell.it.hip.strategy.flows.rules;

import java.util.Map;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.FlowSteps.CrossReferenceData;
import com.dell.it.hip.config.rules.Rule;

public class SelectContivoMapRuleAction implements RuleAction {

	@Override
    public String getName() { return "ContivoMap"; }
    @Override
    public String getType() { return "ContivoMap"; }

    @Override
    public Message<?> performAction(
            Message<?> message,
            Map<String, Object> params,
            Rule rule,
            HIPIntegrationDefinition def,
            Map<String, Object> context
    ) {
        String mapIdentifier = (String) params.get("mapIdentifier");
        String mapIdentifierVersion = (String) params.get("mapIdentifierVersion");
        if (mapIdentifier == null || mapIdentifierVersion == null) {
            throw new IllegalArgumentException("Missing mapIdentifier or mapIdentifierVersion param in SelectContivoMapRuleAction");
        }

        // Load and cache map
       // ContivoMapConfig contivoMap = contivoMapCache.getOrLoad(mapIdentifier, mapIdentifierVersion);
        ContivoMapConfig contivoMap = getOrCreateContivoMapConfig(params);
        // Put only the identifier info in the header (for trace/debug)
        Message<?> newMsg = MessageBuilder.fromMessage(message)
                .setHeader("HIP.contivoMapId", mapIdentifier)
                .setHeader("HIP.contivoMapVersion", mapIdentifierVersion)
                .build();

        // Put the actual map object in context for use by the flow step
        if (context != null) {
            context.put("ContivoMap", contivoMap);
        }
        return newMsg;
    }
    
    
    private ContivoMapConfig getOrCreateContivoMapConfig(Map<String, Object> params) {
        ContivoMapConfig mapData = new ContivoMapConfig();
        CrossReferenceData crossData = new CrossReferenceData();
        mapData.setMapIdentifier(params.get("mapIdentifier").toString());
        mapData.setMapIdentifierVersion(params.get("mapIdentifierVersion").toString());
        mapData.setMapName(params.get("mapName").toString());
        mapData.setMapClass(params.get("mapClass").toString());
        mapData.setContivoVersion(params.get("contivoVersion").toString());
        //Set Cross Map Data
        mapData.setCrossReferenceData(crossData);
        mapData.setRedisMapDataKey(params.get("redisMapDataKey").toString());
        return mapData;
    }
}