# H2 Database Sequence Creation Fix

## Issue Description

**Problem:** Unit tests were failing with H2 database sequence creation errors.

**Error Message:**
```
org.h2.jdbc.JdbcSQLSyntaxErrorException: Sequence "TGTS_LOOKUP_SEQ" already exists; SQL statement:
CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000 [90035-232]
```

**Root Cause:** Multiple test classes were using the same H2 in-memory database URL (`jdbc:h2:mem:testdb`), causing sequence name collisions when tests ran in parallel or sequentially without proper cleanup.

## Solution Implementation

### 1. Unique Database URLs Per Test Instance

**Problem:** All tests used the same database URL, sharing the same in-memory database instance.

**Before:**
```java
private static final String DB_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1";
```

**After:**
```java
// Generate unique database URL for each test instance to avoid conflicts
private final String dbUrl = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
```

**Benefits:**
- ✅ **Complete Isolation**: Each test class gets its own database instance
- ✅ **Parallel Execution**: Tests can run in parallel without conflicts
- ✅ **No Cleanup Dependencies**: No need to coordinate cleanup between tests

### 2. Proper Schema Cleanup Before Creation

**Problem:** Sequences and tables from previous test runs weren't being cleaned up.

**Before:**
```java
String[] createStatements = {
    "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
    "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
    // ... table creation
};
```

**After:**
```java
// Create database schema with proper cleanup
String[] cleanupStatements = {
    "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_SEQ",
    "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_ROW_SEQ",
    "DROP TABLE IF EXISTS TGTS_LOOKUP_COLUMN",
    "DROP TABLE IF EXISTS TGTS_LOOKUP_ROW", 
    "DROP TABLE IF EXISTS TGTS_LOOKUP"
};

String[] createStatements = {
    "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
    "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
    // ... table creation
};

try (Statement stmt = conn.createStatement()) {
    // Execute cleanup statements first (ignore errors for non-existent objects)
    for (String sql : cleanupStatements) {
        try {
            stmt.execute(sql);
        } catch (SQLException e) {
            // Ignore errors for non-existent objects
        }
    }
    
    // Execute create statements
    for (String sql : createStatements) {
        stmt.execute(sql);
    }
}
```

**Benefits:**
- ✅ **Clean State**: Each test starts with a clean database schema
- ✅ **Error Resilience**: Ignores errors for non-existent objects during cleanup
- ✅ **Proper Order**: Drops tables before sequences to respect foreign key constraints

## Files Modified

### 1. FCXRefMigrationTest.java

**Changes:**
- Replaced static `DB_URL` with instance variable `dbUrl` using `System.nanoTime()`
- Added cleanup statements before schema creation
- Updated all database connection references to use unique URL

**Key Changes:**
```java
// Before
private static final String DB_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1";

// After  
private final String dbUrl = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
```

### 2. MigrationUtilityTest.java

**Changes:**
- Same pattern as FCXRefMigrationTest.java
- Unique database URL per test instance
- Proper cleanup before schema creation
- Updated all connection references

### 3. DatabaseCompatibilityTest.java

**Changes:**
- Renamed `H2_DB_URL` to instance variable `h2DbUrl`
- Added cleanup statements
- Updated all connection references

## Database Isolation Strategy

### Per-Test-Class Isolation

Each test class now gets its own completely isolated H2 in-memory database:

```java
// Test Class 1: jdbc:h2:mem:testdb_1234567890123
// Test Class 2: jdbc:h2:mem:testdb_1234567890456  
// Test Class 3: jdbc:h2:mem:testdb_1234567890789
```

**Benefits:**
- **No Shared State**: Tests cannot interfere with each other
- **Parallel Execution**: Multiple test classes can run simultaneously
- **Deterministic Results**: Each test starts with identical, clean state

### Cleanup Order

The cleanup follows proper dependency order:

1. **Tables** (in reverse dependency order):
   - `TGTS_LOOKUP_COLUMN` (references TGTS_LOOKUP_ROW)
   - `TGTS_LOOKUP_ROW` (references TGTS_LOOKUP)
   - `TGTS_LOOKUP` (base table)

2. **Sequences**:
   - `TGTS_LOOKUP_SEQ`
   - `TGTS_LOOKUP_ROW_SEQ`

## Error Handling

### Graceful Cleanup Error Handling

```java
for (String sql : cleanupStatements) {
    try {
        stmt.execute(sql);
    } catch (SQLException e) {
        // Ignore errors for non-existent objects
        // This allows tests to run even if cleanup targets don't exist
    }
}
```

**Why This Works:**
- **First Run**: Cleanup statements fail (objects don't exist) but are ignored
- **Subsequent Runs**: Cleanup statements succeed, ensuring clean state
- **Robustness**: Tests work regardless of previous execution state

### Connection Management

Each test properly manages its database connection:

```java
try (Connection conn = DriverManager.getConnection(dbUrl)) {
    // Database operations
} // Connection automatically closed
```

## Testing Strategy

### Test Execution Scenarios

**1. Individual Test Execution:**
```bash
mvn test -Dtest=FCXRefMigrationTest
# Uses: jdbc:h2:mem:testdb_1234567890123
```

**2. Sequential Test Execution:**
```bash
mvn test -Dtest=FCXRefMigrationTest,MigrationUtilityTest
# FCXRefMigrationTest uses: jdbc:h2:mem:testdb_1234567890123
# MigrationUtilityTest uses: jdbc:h2:mem:testdb_1234567890456
```

**3. Parallel Test Execution:**
```bash
mvn test -Dparallel=classes -DthreadCount=4
# Each test class gets unique database URL
# No conflicts between parallel executions
```

### Verification Tests

**1. Sequence Creation Test:**
```java
@Test
void testSequenceCreation() throws Exception {
    // Verify sequences are created successfully
    try (Connection conn = DriverManager.getConnection(dbUrl)) {
        String sql = "SELECT NEXT VALUE FOR TGTS_LOOKUP_SEQ";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            assertTrue(rs.next());
            assertEquals(1000, rs.getLong(1)); // First value should be 1000
        }
    }
}
```

**2. Database Isolation Test:**
```java
@Test
void testDatabaseIsolation() throws Exception {
    // Each test instance should have unique database URL
    String url1 = "jdbc:h2:mem:testdb_" + System.nanoTime();
    String url2 = "jdbc:h2:mem:testdb_" + System.nanoTime();
    
    assertNotEquals(url1, url2, "Database URLs should be unique");
}
```

## Performance Impact

### Memory Usage
- **Before**: Single shared H2 database for all tests
- **After**: One H2 database per test class
- **Impact**: Minimal increase (H2 in-memory databases are lightweight)

### Execution Time
- **Before**: Sequential execution required due to shared database
- **After**: Parallel execution possible with isolated databases
- **Impact**: Potential speedup with parallel test execution

### Resource Cleanup
- **Before**: Manual cleanup coordination required
- **After**: Automatic cleanup with database URL uniqueness
- **Impact**: More reliable and maintainable tests

## Troubleshooting

### Common Issues After Fix

**1. Test Still Failing with Sequence Errors:**
- **Cause**: Old test instances still running
- **Solution**: Clean rebuild: `mvn clean test`

**2. Out of Memory Errors:**
- **Cause**: Too many H2 databases in memory
- **Solution**: Increase heap size: `mvn test -Xmx1g`

**3. Slow Test Execution:**
- **Cause**: Database creation overhead
- **Solution**: Normal for comprehensive testing; consider test grouping

### Debugging Database Issues

**1. Verify Unique URLs:**
```java
System.out.println("Using database URL: " + dbUrl);
```

**2. Check Schema Creation:**
```java
DatabaseMetaData metaData = conn.getMetaData();
ResultSet tables = metaData.getTables(null, null, "TGTS_LOOKUP%", null);
while (tables.next()) {
    System.out.println("Table: " + tables.getString("TABLE_NAME"));
}
```

**3. Verify Sequence Values:**
```java
String sql = "SELECT NEXT VALUE FOR TGTS_LOOKUP_SEQ";
// Should return 1000, 1001, 1002, etc.
```

## Conclusion

This fix resolves the H2 sequence creation conflicts by implementing:

1. **✅ Database Isolation**: Unique database URL per test class
2. **✅ Proper Cleanup**: DROP IF EXISTS statements before creation
3. **✅ Error Resilience**: Graceful handling of cleanup errors
4. **✅ Parallel Support**: Tests can run in parallel without conflicts
5. **✅ Maintainability**: Clean, predictable test execution

The unit tests should now run successfully without sequence creation errors, whether executed individually or as a complete test suite. The solution maintains full compatibility with both Oracle (production) and H2 (testing) databases while providing robust test isolation.
