package com.dell.it.hip.strategy.flows.rules;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dell.it.hip.config.rules.HIPRuleEntity;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.repository.HIPRuleRepository;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.dell.it.hip.util.redis.HIPRedisKeyUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

@ExtendWith(MockitoExtension.class)
class RuleCacheTest {

    @Mock
    private HIPRedisCompatibilityService redisService;

    @Mock
    private HIPRuleRepository hipRuleRepository;

    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private RuleCache ruleCache;

    private Rule testRule;
    private HIPRuleEntity testRuleEntity;
    private RuleRef testRuleRef;
    private String testRuleJson;

    @BeforeEach
    void setUp() {
        // Setup Redis service operations - no additional setup needed
        
        // Setup test data
        testRule = new Rule();
        testRule.setRuleName("testRule");
        testRule.setRuleVersion("1.0");
        
        testRuleEntity = new HIPRuleEntity();
        testRuleEntity.setRuleKey("test:rule:key");
        testRuleEntity.setRuleJson("{\"ruleName\":\"testRule\",\"ruleVersion\":\"1.0\"}");
        
        testRuleRef = new RuleRef();
        testRuleRef.setRuleName("testRule");
        testRuleRef.setRuleVersion("1.0");
        testRuleRef.setId(0223);
        
        testRuleJson = "{\"ruleName\":\"testRule\",\"ruleVersion\":\"1.0\"}";
    }

    @Test
    @DisplayName("Should refresh integration rules from Redis successfully")
    void testRefreshIntegrationRules_FromRedis_Success() throws Exception {
        // Arrange
        String serviceManager = "testService";
        String integration = "testIntegration";
        String version = "1.0";
        String prefix = "test:prefix:";
        String redisKey = prefix + "rule1";
        
        when(redisService.keys(prefix + "*")).thenReturn(Set.of(redisKey));
        when(redisService.get(redisKey)).thenReturn(testRuleJson);
        when(objectMapper.readValue(testRuleJson, Rule.class)).thenReturn(testRule);

        // Mock the HIPRedisKeyUtil static method
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version))
                       .thenReturn(prefix);

            // Act
            ruleCache.refreshIntegrationRules(serviceManager, integration, version);

            // Assert
            verify(redisService).keys(prefix + "*");
            verify(redisService).get(redisKey);
            verify(objectMapper).readValue(testRuleJson, Rule.class);
            verify(hipRuleRepository, never()).findByRuleKeyPrefix(anyString());
        }
    }

    @Test
    @DisplayName("Should fallback to database when Redis is empty")
    void testRefreshIntegrationRules_FallbackToDatabase_Success() throws Exception {
        // Arrange
        String serviceManager = "testService";
        String integration = "testIntegration";
        String version = "1.0";
        String prefix = "test:prefix:";
        
        when(redisService.keys(prefix + "*")).thenReturn(Collections.emptySet());
        when(hipRuleRepository.findByRuleKeyPrefix(prefix)).thenReturn(Arrays.asList(testRuleEntity));
        when(objectMapper.readValue(testRuleEntity.getRuleJson(), Rule.class)).thenReturn(testRule);
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version))
                       .thenReturn(prefix);
            
            // Act
            ruleCache.refreshIntegrationRules(serviceManager, integration, version);
            
            // Assert
            verify(hipRuleRepository).findByRuleKeyPrefix(prefix);
            verify(objectMapper).readValue(testRuleEntity.getRuleJson(), Rule.class);
        }
    }

    @Test
    @DisplayName("Should handle JSON parsing errors gracefully during refresh")
    void testRefreshIntegrationRules_JsonParsingError_HandledGracefully() throws Exception {
        // Arrange
        String serviceManager = "testService";
        String integration = "testIntegration";
        String version = "1.0";
        String prefix = "test:prefix:";
        String redisKey = prefix + "rule1";
        
        when(redisService.keys(prefix + "*")).thenReturn(Set.of(redisKey));
        when(redisService.get(redisKey)).thenReturn("invalid-json");
        when(objectMapper.readValue("invalid-json", Rule.class)).thenThrow(new RuntimeException("JSON parse error"));
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version))
                       .thenReturn(prefix);
            
            // Act & Assert - should not throw exception
            assertDoesNotThrow(() -> ruleCache.refreshIntegrationRules(serviceManager, integration, version));
        }
    }

    @Test
    @DisplayName("Should refresh explicit rules from Redis successfully")
    void testRefreshExplicitRules_FromRedis_Success() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);

        when(redisService.get(ruleKey)).thenReturn(testRuleJson);
        when(objectMapper.readValue(testRuleJson, Rule.class)).thenReturn(testRule);

        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);

            // Act
            ruleCache.refreshExplicitRules(ruleRefs);

            // Assert
            verify(redisService).get(ruleKey);
            verify(objectMapper).readValue(testRuleJson, Rule.class);
            verify(hipRuleRepository, never()).findByRuleKey(anyString());
        }
    }

    @Test
    @DisplayName("Should fallback to database for explicit rules when Redis is empty")
    void testRefreshExplicitRules_FallbackToDatabase_Success() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);

        when(redisService.get(ruleKey)).thenReturn(null);
        when(hipRuleRepository.findByRuleKey(ruleKey)).thenReturn(testRuleEntity);
        when(objectMapper.readValue(testRuleEntity.getRuleJson(), Rule.class)).thenReturn(testRule);

        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);

            // Act
            ruleCache.refreshExplicitRules(ruleRefs);

            // Assert
            verify(hipRuleRepository).findByRuleKey(ruleKey);
            verify(objectMapper).readValue(testRuleEntity.getRuleJson(), Rule.class);
        }
    }

    @Test
    @DisplayName("Should get rules for integration from cache when available")
    void testGetRulesForIntegration_FromCache_Success() throws Exception {
        // Arrange
        String serviceManager = "testService";
        String integration = "testIntegration";
        String version = "1.0";
        String prefix = "test:prefix:";
        String redisKey = prefix + "rule1";
        
        // First populate the cache
        when(redisService.keys(prefix + "*")).thenReturn(Set.of(redisKey));
        when(redisService.get(redisKey)).thenReturn(testRuleJson);
        when(objectMapper.readValue(testRuleJson, Rule.class)).thenReturn(testRule);
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version))
                       .thenReturn(prefix);
            
            ruleCache.refreshIntegrationRules(serviceManager, integration, version);
            
            // Act - second call should use cache
            List<Rule> result = ruleCache.getRulesForIntegration(serviceManager, integration, version);
            
            // Assert
            assertEquals(1, result.size());
            assertEquals(testRule, result.get(0));
            // Verify Redis was called only once during refresh, not during get
            verify(redisService, times(1)).get(redisKey);
        }
    }

    @Test
    @DisplayName("Should get rules by refs from cache when available")
    void testGetRulesByRefs_FromCache_Success() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);
        
        // First populate the cache
        when(redisService.get(ruleKey)).thenReturn(testRuleJson);
        when(objectMapper.readValue(testRuleJson, Rule.class)).thenReturn(testRule);
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);
            
            // First call to populate cache
            ruleCache.getRulesByRefs(ruleRefs, true);
            
            // Act - second call should use cache
            List<Rule> result = ruleCache.getRulesByRefs(ruleRefs, true);
            
            // Assert
            assertEquals(1, result.size());
            assertEquals(testRule, result.get(0));
            // Verify Redis was called only once
            verify(redisService, times(1)).get(ruleKey);
        }
    }

    @Test
    @DisplayName("Should not fallback to database when dbBacked is false")
    void testGetRulesByRefs_NoDatabaseFallback_WhenDbBackedFalse() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);
        
        when(redisService.get(ruleKey)).thenReturn(null);
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);
            
            // Act
            List<Rule> result = ruleCache.getRulesByRefs(ruleRefs, false);
            
            // Assert
            assertTrue(result.isEmpty());
            verify(hipRuleRepository, never()).findByRuleKey(anyString());
        }
    }

    @Test
    @DisplayName("Should preload rules for integration")
    void testPreloadRulesForIntegration_Success() throws Exception {
        // Arrange
        String serviceManager = "testService";
        String integration = "testIntegration";
        String version = "1.0";
        String prefix = "test:prefix:";
        
        when(redisService.keys(prefix + "*")).thenReturn(Collections.emptySet());
        when(hipRuleRepository.findByRuleKeyPrefix(prefix)).thenReturn(Collections.emptyList());
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowRoutingRulePrefix(serviceManager, integration, version))
                       .thenReturn(prefix);
            
            // Act
            ruleCache.preloadRulesForIntegration(serviceManager, integration, version);
            
            // Assert
            verify(redisService).keys(prefix + "*");
        }
    }

    @Test
    @DisplayName("Should preload explicit rules")
    void testPreloadExplicitRules_Success() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);
        
        when(redisService.get(ruleKey)).thenReturn(null);
        when(hipRuleRepository.findByRuleKey(ruleKey)).thenReturn(null);
        
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);
            
            // Act
            ruleCache.preloadExplicitRules(ruleRefs, true);
            
            // Assert
            verify(redisService).get(ruleKey);
            verify(hipRuleRepository).findByRuleKey(ruleKey);
        }
    }

    @Test
    @DisplayName("Should refresh specific rule by removing from cache")
    void testRefreshRule_Success() {
        // Arrange
        String ruleKey = "test:rule:key";
        
        // Act
        ruleCache.refreshRule(ruleKey);
        
        // Assert - method should complete without error
        // The actual cache removal is tested implicitly through other tests
        assertDoesNotThrow(() -> ruleCache.refreshRule(ruleKey));
    }

    @Test
    @DisplayName("Should clear all cached rules")
    void testClear_Success() {
        // Act
        ruleCache.clear();
        
        // Assert - method should complete without error
        assertDoesNotThrow(() -> ruleCache.clear());
    }

    @Test
    @DisplayName("Should handle null rule entity gracefully")
    void testRefreshExplicitRules_NullRuleEntity_HandledGracefully() throws Exception {
        // Arrange
        String ruleKey = "test:rule:key";
        List<RuleRef> ruleRefs = Arrays.asList(testRuleRef);
        
       // lenient().when(redisService.get(ruleKey)).thenReturn(null);
       // lenient().when(hipRuleRepository.findByRuleKey(ruleKey)).thenReturn(null);

        when(redisService.get(ruleKey)).thenReturn(null);
        when(hipRuleRepository.findByRuleKey(ruleKey)).thenReturn(null);
        try (var mockedStatic = mockStatic(HIPRedisKeyUtil.class)) {
            mockedStatic.when(() -> HIPRedisKeyUtil.flowTargetsRuleKey(testRuleRef.getRuleName(), testRuleRef.getRuleVersion()))
                       .thenReturn(ruleKey);

            // Act & Assert - should not throw exception
            assertDoesNotThrow(() -> ruleCache.refreshExplicitRules(ruleRefs));
        }
    }
}
