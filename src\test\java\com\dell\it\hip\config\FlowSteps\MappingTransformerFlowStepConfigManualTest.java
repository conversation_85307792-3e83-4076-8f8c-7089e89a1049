package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * Manual test class to verify MappingTransformerFlowStepConfig deserialization
 * This can be run independently to test the deserializer functionality
 */
public class MappingTransformerFlowStepConfigManualTest {

    public static void main(String[] args) {
        try {
            testBasicDeserialization();
            testActualMappingConfigFile();
            System.out.println("✅ All manual tests passed!");
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testBasicDeserialization() throws Exception {
        System.out.println("🧪 Testing basic deserialization...");
        
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"emfp-request-850\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"ContivoMapForRequest850\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].version\": \"1.0\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": true,\n"
                + "  \"docTypeConfigs[1].transformerRef\": \"emfp-request-850\",\n"
                + "  \"docTypeConfigs[1].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[1].ruleRefs[0].name\": \"ContivoMapForRequest850\",\n"
                + "  \"docTypeConfigs[1].ruleRefs[0].version\": \"1.0\",\n"
                + "  \"docTypeConfigs[1].isDbBacked\": true\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        // Verify basic structure
        assert config != null : "Config should not be null";
        assert config.getDocTypeConfigs() != null : "DocTypeConfigs should not be null";
        assert config.getDocTypeConfigs().size() == 2 : "Should have 2 docTypeConfigs";
        
        // Verify first docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig0 = config.getDocTypeConfigs().get(0);
        assert docTypeConfig0 != null : "First docTypeConfig should not be null";
        assert "emfp-request-850".equals(docTypeConfig0.getName()) : "TransformerRef should match";
        assert MappingTransformerFlowStepConfig.MappingBehavior.MAPPING.equals(docTypeConfig0.getAction()) : "Behavior should be TRANSFORM";
        assert docTypeConfig0.isDbBacked() : "Should be DB backed";
        
        // Verify first docTypeConfig's ruleRefs
        assert docTypeConfig0.getRuleRefs() != null : "RuleRefs should not be null";
        assert docTypeConfig0.getRuleRefs() != null : "Should have 1 ruleRef";
        RuleRef ruleRef0 = docTypeConfig0.getRuleRefs();
        assert ruleRef0 != null : "RuleRef should not be null";
        assert "ContivoMapForRequest850".equals(ruleRef0.getRuleName()) : "RuleName should match";
        assert "1.0".equals(ruleRef0.getRuleVersion()) : "RuleVersion should match";
        
        System.out.println("✅ Basic deserialization test passed");
    }

    private static void testActualMappingConfigFile() throws Exception {
        System.out.println("🧪 Testing actual mapping-config.json file...");
        
        // Load the actual mapping-config.json file from test resources
        InputStream inputStream = MappingTransformerFlowStepConfigManualTest.class
                .getClassLoader().getResourceAsStream("mapping-config.json");
        
        if (inputStream == null) {
            System.out.println("⚠️ mapping-config.json file not found in test resources, skipping this test");
            return;
        }
        
        // Read the JSON content
        String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        assert jsonContent != null && !jsonContent.trim().isEmpty() : "JSON content should not be empty";
        
        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(jsonContent, MappingTransformerFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assert config != null : "Config should not be null";
        assert config.getDocTypeConfigs() != null : "DocTypeConfigs should not be null";
        assert config.getDocTypeConfigs().size() == 2 : "Should have 2 docTypeConfigs as per mapping-config.json";
        
        // Verify first docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig0 = config.getDocTypeConfigs().get(0);
        assert docTypeConfig0 != null : "First docTypeConfig should not be null";
        assert "emfp-request-850".equals(docTypeConfig0.getName()) : "TransformerRef should match";
        assert MappingTransformerFlowStepConfig.MappingBehavior.MAPPING.equals(docTypeConfig0.getAction()) : "Behavior should be TRANSFORM";
        assert docTypeConfig0.isDbBacked() : "Should be DB backed";
        
        // Verify first docTypeConfig's ruleRefs
        assert docTypeConfig0.getRuleRefs() != null : "RuleRefs should not be null";
        assert docTypeConfig0.getRuleRefs() != null : "Should have 1 ruleRef";
        RuleRef ruleRef0 = docTypeConfig0.getRuleRefs();
        assert ruleRef0 != null : "RuleRef should not be null";
        assert "ContivoMapForRequest850".equals(ruleRef0.getRuleName()) : "RuleName should match";
        assert "1.0".equals(ruleRef0.getRuleVersion()) : "RuleVersion should match";
        
        System.out.println("✅ Actual mapping-config.json test passed");
        System.out.println("Found " + config.getDocTypeConfigs().size() + " docTypeConfigs");
        System.out.println("First docTypeConfig transformer: " + docTypeConfig0.getName());
        System.out.println("First docTypeConfig ruleRef: " + ruleRef0.getRuleName() + " v" + ruleRef0.getRuleVersion());
    }
}
