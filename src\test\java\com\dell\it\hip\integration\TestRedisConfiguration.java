package com.dell.it.hip.integration;

import java.util.function.Consumer;

import org.mockito.Mockito;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.connection.BitFieldSubCommands;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.ThrottlingService;

/**
 * Test-specific Redis configuration that provides mock implementations
 * when Redis is disabled for testing without Docker.
 */
@Configuration
@Import(RedisAutoConfiguration.class)
public class TestRedisConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(TestRedisConfiguration.class);

    /**
     * Provides a mock StringRedisTemplate when Redis is disabled.
     * This prevents autowiring failures in tests that depend on StringRedisTemplate.
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "false")
    public StringRedisTemplate mockStringRedisTemplate() {
        logger.info("Creating mock StringRedisTemplate for testing without Redis");
        return new MockStringRedisTemplate();
    }

    /**
     * Provides a mock RedisTemplate<String, byte[]> when Redis is disabled.
     * This prevents autowiring failures in tests that depend on byteRedisTemplate.
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "false")
    public RedisTemplate<String, byte[]> mockByteRedisTemplate() {
        logger.info("Creating mock RedisTemplate<String, byte[]> for testing without Redis");
        RedisTemplate<String, byte[]> mockTemplate = Mockito.mock(RedisTemplate.class);
        ValueOperations<String, byte[]> mockValueOps = Mockito.mock(ValueOperations.class);

        // Configure mock behavior
        Mockito.when(mockTemplate.opsForValue()).thenReturn(mockValueOps);
        Mockito.when(mockValueOps.get(Mockito.anyString())).thenReturn(null);
        Mockito.doNothing().when(mockValueOps).set(Mockito.anyString(), Mockito.any(byte[].class));

        return mockTemplate;
    }

    /**
     * Provides a mock HIPClusterCoordinationService when Redis is disabled.
     * This prevents autowiring failures in tests that depend on HIPClusterCoordinationService.
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(HIPClusterCoordinationService.class)
    public HIPClusterCoordinationService mockHIPClusterCoordinationService() {
        logger.info("Creating mock HIPClusterCoordinationService for testing without Redis");
        return new MockHIPClusterCoordinationService();
    }

    /**
     * Provides a mock ThrottlingService when Redis is disabled.
     * This prevents autowiring failures in tests that depend on ThrottlingService.
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(ThrottlingService.class)
    public ThrottlingService mockThrottlingService() {
        logger.info("Creating mock ThrottlingService for testing without Redis");
        return new MockThrottlingService();
    }

    /**
     * Mock implementation of StringRedisTemplate that does nothing.
     * This allows tests to run without Redis while preventing null pointer exceptions.
     */
    public static class MockStringRedisTemplate extends StringRedisTemplate {

        private static final Logger logger = LoggerFactory.getLogger(MockStringRedisTemplate.class);

        public MockStringRedisTemplate() {
            super();
            logger.debug("MockStringRedisTemplate created - all operations will be no-ops");
        }

        @Override
        public RedisConnectionFactory getConnectionFactory() {
            logger.debug("MockStringRedisTemplate.getConnectionFactory() called - returning null");
            return null;
        }

        // Override key methods to prevent actual Redis operations
        @Override
        public void afterPropertiesSet() {
            logger.debug("MockStringRedisTemplate.afterPropertiesSet() called - skipping Redis initialization");
            // Do nothing - skip Redis initialization
        }
    }



    /**
     * Mock implementation of HIPClusterCoordinationService that does nothing.
     * This allows tests to run without Redis while preventing null pointer exceptions.
     */
    public static class MockHIPClusterCoordinationService extends HIPClusterCoordinationService {

        private static final Logger logger = LoggerFactory.getLogger(MockHIPClusterCoordinationService.class);

        public MockHIPClusterCoordinationService() {
            super("test-service-manager", null, null, null, null);
            logger.debug("MockHIPClusterCoordinationService created - all operations will be no-ops");
        }

        @Override
        public void registerClusterEventListener(Consumer<HIPClusterEvent> listener) {
            logger.debug("MockHIPClusterCoordinationService.registerClusterEventListener() called - no-op");
            // Do nothing - no cluster events in test mode
        }

        @Override
        public void broadcastRegistration(HIPIntegrationDefinition def) {
            logger.debug("MockHIPClusterCoordinationService.broadcastRegistration() called for {}:{} - no-op",
                        def.getHipIntegrationName(), def.getVersion());
            // Do nothing - no cluster broadcasting in test mode
        }

        @Override
        public void pause(HIPIntegrationDefinition def, AdapterConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.pause() called for {}:{} adapter {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public void resume(HIPIntegrationDefinition def, AdapterConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.resume() called for {}:{} adapter {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public void pauseHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.pauseHandler() called for {}:{} handler {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public void resumeHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.resumeHandler() called for {}:{} handler {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public void shutdownHandler(HIPIntegrationDefinition def, HandlerConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.shutdownHandler() called for {}:{} handler {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public void setThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings) {
            logger.debug("MockHIPClusterCoordinationService.setThrottle() called for {}:{} adapter {} - no-op",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            // Do nothing - no cluster coordination in test mode
        }

        @Override
        public boolean isAdapterPaused(HIPIntegrationDefinition def, AdapterConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.isAdapterPaused() called for {}:{} adapter {} - returning false",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            return false; // Always return false in test mode
        }

        @Override
        public Boolean isHandlerPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.isHandlerPaused() called for {}:{} handler {} - returning false",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            return false; // Always return false in test mode
        }

        @Override
        public boolean isAdapterThrottled(HIPIntegrationDefinition def, AdapterConfigRef ref) {
            logger.debug("MockHIPClusterCoordinationService.isAdapterThrottled() called for {}:{} adapter {} - returning false",
                        def.getHipIntegrationName(), def.getVersion(), ref.getId());
            return false; // Always return false in test mode
        }
    }

    /**
     * Mock implementation of ThrottlingService that does nothing.
     * This allows tests to run without Redis while preventing null pointer exceptions.
     */
    public static class MockThrottlingService implements ThrottlingService {

        private static final Logger logger = LoggerFactory.getLogger(MockThrottlingService.class);

        @Override
        public boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion,
                                       String adapterId, ThrottleSettings settings) {
            logger.debug("MockThrottlingService.tryConsumeToken() called for {}:{} adapter {} - returning true (no throttling)",
                        integrationId, integrationVersion, adapterId);
            return true; // Always allow in test mode - no throttling
        }

        @Override
        public void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
            logger.debug("MockThrottlingService.resetThrottle() called for {}:{} adapter {} - no-op",
                        integrationId, integrationVersion, adapterId);
            // Do nothing - no throttling state to reset in test mode
        }

        @Override
        public void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion,
                                   String adapterId, ThrottleSettings settings) {
            logger.debug("MockThrottlingService.updateThrottle() called for {}:{} adapter {} - no-op",
                        integrationId, integrationVersion, adapterId);
            // Do nothing - no throttling state to update in test mode
        }

        @Override
        public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId,
                                                    String integrationVersion, String adapterId) {
            logger.debug("MockThrottlingService.getThrottleSettings() called for {}:{} adapter {} - returning disabled settings",
                        integrationId, integrationVersion, adapterId);
            // Return disabled throttle settings for test mode
            return new ThrottleSettings(Integer.MAX_VALUE, 1, false);
        }
    }
}
