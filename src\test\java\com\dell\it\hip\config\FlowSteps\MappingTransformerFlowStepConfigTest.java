package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class MappingTransformerFlowStepConfigTest {

	private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testSingleDocTypeConfig() throws Exception {
        String json = """
            {
              "configurationList[0].documentTypeId": 101,
              "configurationList[0].documentTypeName": "Invoice",
              "configurationList[0].documentTypeVersion": "1.0",
              "configurationList[0].action": "MAPPING",
              "configurationList[0].ruleRefs.name": "RuleOne",
              "configurationList[0].ruleRefs.version": "1.0",
              "configurationList[0].isDbBacked": true
            }
        """;

        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        assertNotNull(config.getDocTypeConfigs());
        assertEquals(1, config.getDocTypeConfigs().size());

        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig doc = config.getDocTypeConfigs().get(0);
        assertEquals(101, doc.getDocumentTypeId());
        assertEquals("Invoice", doc.getName());
        assertEquals("1.0", doc.getVersion());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.MAPPING, doc.getAction());
        assertTrue(doc.isDbBacked());

        RuleRef rule = doc.getRuleRefs();
        assertNotNull(rule);
        assertEquals("RuleOne", rule.getRuleName());
        assertEquals("1.0", rule.getRuleVersion());
    }

    @Test
    public void testDefaultConfigPresent() throws Exception {
        String json = """
            {
              "defaultConfig.action": "SKIP",
              "defaultConfig.isDbBacked": true,
              "defaultConfig.ruleRefs.name": "DefaultRule",
              "defaultConfig.ruleRefs.version": "2.1"
            }
        """;

        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        assertNotNull(config.getDefaultConfig());

        MappingTransformerFlowStepConfig.DefaultMappingTransformerConfig def = config.getDefaultConfig();
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.SKIP, def.getAction());
        assertTrue(def.isDbBacked());

        RuleRef rule = def.getRuleRefs();
        assertNotNull(rule);
        assertEquals("DefaultRule", rule.getRuleName());
        assertEquals("2.1", rule.getRuleVersion());
    }

    @Test
    public void testUnknownFieldsAreIgnored() throws Exception {
        String json = """
            {
              "configurationList[0].documentTypeName": "Invoice",
              "configurationList[0].unknownField": "someValue"
            }
        """;

        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        assertEquals(1, config.getDocTypeConfigs().size());
        assertEquals("Invoice", config.getDocTypeConfigs().get(0).getName());
    }

    @Test
    public void testMultipleDocTypeConfigs() throws Exception {
        String json = """
            {
              "configurationList[0].documentTypeName": "Invoice",
              "configurationList[1].documentTypeName": "Order",
              "configurationList[1].action": "TERMINATE"
            }
        """;

        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        assertEquals(2, config.getDocTypeConfigs().size());

        assertEquals("Invoice", config.getDocTypeConfigs().get(0).getName());
        assertEquals("Order", config.getDocTypeConfigs().get(1).getName());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TERMINATE, config.getDocTypeConfigs().get(1).getAction());
    }

    @Test
    public void testMissingOptionalFields() throws Exception {
        String json = """
            {
              "configurationList[0].documentTypeId": 501,
              "configurationList[0].documentTypeName": "Minimal"
            }
        """;

        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        assertEquals(1, config.getDocTypeConfigs().size());

        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig doc = config.getDocTypeConfigs().get(0);
        assertEquals(501, doc.getDocumentTypeId());
        assertEquals("Minimal", doc.getName());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.MAPPING, doc.getAction()); // default
        assertFalse(doc.isDbBacked()); // default
        assertNull(doc.getRuleRefs());
    }
}
