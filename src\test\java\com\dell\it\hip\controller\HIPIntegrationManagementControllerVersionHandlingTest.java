package com.dell.it.hip.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.never;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.math.BigDecimal;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.test.web.servlet.MvcResult;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.Tag;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.exception.GlobalExceptionHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * Integration tests for HIPIntegrationManagementController focusing on version field handling
 * in the /register endpoint. Tests the complete flow from API input to Redis storage.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerVersionHandlingTest {

    private MockMvc mockMvc;

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private HIPIntegrationRuntimeService runtimeService;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Mock
    private ServiceManager serviceManager;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        // Set up the app_config_name field using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", "test-service-manager");
        // Set up MockMvc with global exception handler
        mockMvc = MockMvcBuilders.standaloneSetup(controller)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    @Test
    void testRegisterEndpoint_SimpleDecimalVersion_VersionHandledConsistently() throws Exception {
        // Arrange
        String version = "1.0";
        HIPIntegrationRequest request = createTestRequest(version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(false);
        doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        MvcResult result = mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();

        // Assert
        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).registerHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        assertEquals(version, capturedRequest.getVersion());
    }

    @Test
    void testRegisterEndpoint_SemanticVersion_ConvertedToDecimalFormat() throws Exception {
        // Arrange
        String inputVersion = "1.2.3";
        HIPIntegrationRequest request = createTestRequest(inputVersion);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(false);
        doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Assert
        ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
        verify(orchestrationService).registerHIPIntegration(requestCaptor.capture());

        HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
        assertEquals(inputVersion, capturedRequest.getVersion());
    }

    @Test
    void testRegisterEndpoint_DuplicateRegistration_VersionConsistencyMaintained() throws Exception {
        // Arrange
        String version = "2.1";
        HIPIntegrationRequest request = createTestRequest(version);

        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.message").value("Integration with service manager 'test-service-manager', integration 'test-integration', and version '2.1' already exists"));

        // Verify that the version consistency check was performed
        verify(hipIntegrationRegistry).exists(
            eq("test-service-manager"),
            eq("test-integration"),
            eq(version) // Should use the String version for the check
        );
    }

    @Test
    void testRegisterEndpoint_NullVersion_HandledGracefully() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest(null);

        // Act & Assert - Null version should be rejected with validation error
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("VALIDATION_ERROR"));

        // Verify that orchestration service was never called due to validation failure
        verify(orchestrationService, never()).registerHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRegisterEndpoint_EdgeCaseVersions_AllHandledConsistently() throws Exception {
        String[] edgeCaseVersions = {"0", "0.0", "10.99", "1.0.0", "2.1.3", "1.99.99"};

        for (String version : edgeCaseVersions) {
            // Arrange
            HIPIntegrationRequest request = createTestRequest(version);

            when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(false);
            doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

            // Act
            mockMvc.perform(post("/hip/management/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk());

            // Assert
            ArgumentCaptor<HIPIntegrationRequest> requestCaptor = ArgumentCaptor.forClass(HIPIntegrationRequest.class);
            verify(orchestrationService).registerHIPIntegration(requestCaptor.capture());

            HIPIntegrationRequest capturedRequest = requestCaptor.getValue();
            assertNotNull(capturedRequest.getVersion(), "Version should not be null for: " + version);

            // Reset mocks for next iteration
            reset(orchestrationService, hipIntegrationRegistry);
        }
    }

    @Test
    void testRegisterEndpoint_InvalidJsonFormat_BadRequest() throws Exception {
        // Arrange
        String invalidJson = "{ \"serviceManagerName\": \"test\", \"version\": }"; // Invalid JSON

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testRegisterEndpoint_MissingRequiredFields_BadRequest() throws Exception {
        // Arrange
        HIPIntegrationRequest incompleteRequest = new HIPIntegrationRequest();
        incompleteRequest.setVersion("1.0"); // Only version set, missing required fields

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(incompleteRequest)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Helper method to create a test HIPIntegrationRequest
     */
    private HIPIntegrationRequest createTestRequest(String version) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setServiceManagerName("test-service-manager");
        request.setHipIntegrationName("test-integration");
        request.setVersion(version);
        request.setBusinessFlowName("test-business-flow");

        // Create tags as List<Tag>
        List<Tag> tags = new ArrayList<>();
        tags.add(new Tag("environment", "test"));
        tags.add(new Tag("team", "integration"));
        request.setTags(tags);

        return request;
    }


}
