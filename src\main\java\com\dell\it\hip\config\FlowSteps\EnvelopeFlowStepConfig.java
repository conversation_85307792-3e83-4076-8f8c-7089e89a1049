package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.FlowSteps.EnvelopeFlowStepConfig.DefaultEnvelopeConfig;
import com.dell.it.hip.config.FlowSteps.EnvelopeFlowStepConfig.DocTypeEnvelopeConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = EnvelopeFlowStepConfigDeserializer.class)
public class EnvelopeFlowStepConfig extends FlowStepConfig {
    // DocType-based config
	@JsonProperty("configurationList")
    private List<DocTypeEnvelopeConfig> docTypeConfigs = new ArrayList<>();
    private DefaultEnvelopeConfig defaultConfig;

    // Backward-compatible: if someone still sets these
    private String envelopeHeader;
    private String envelopeFooter;
    private String separator;
    private String dataFormat;
    private boolean validateEnvelope = true;
    private Map<String, String> dynamicHeaders;

    @Data
    public static class DocTypeEnvelopeConfig extends DocTypeConfig {
    	@JsonProperty("action")
        private EnvelopeBehavior envelopeBehavior = EnvelopeBehavior.ENVELOPE; // ENVELOPE/SKIP/TERMINATE
    	@JsonProperty("startTag")
        private String envelopeHeader;
    	@JsonProperty("endTag")
        private String envelopeFooter;
    	@JsonProperty("messageSeperator")
        private String separator;
    	@JsonProperty("validation")
        private boolean validateEnvelope = true;
        private Map<String, String> dynamicHeaders;
    }

    @Data
    public static class DefaultEnvelopeConfig extends DocTypeConfig {
    	@JsonProperty("action")
        private EnvelopeBehavior envelopeBehavior = EnvelopeBehavior.ENVELOPE;
    	@JsonProperty("startTag")
        private String envelopeHeader;
    	@JsonProperty("endTag")
        private String envelopeFooter;
    	@JsonProperty("messageSeperator")
        private String separator;
    	@JsonProperty("validation")
        private boolean validateEnvelope = true;
        private Map<String, String> dynamicHeaders;
    }

    public enum EnvelopeBehavior { ENVELOPE, SKIP, TERMINATE }
}

class EnvelopeFlowStepConfigDeserializer extends JsonDeserializer<EnvelopeFlowStepConfig> {

    @Override
    public EnvelopeFlowStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) parser.getCodec();
        JsonNode root = mapper.readTree(parser);

        EnvelopeFlowStepConfig result = new EnvelopeFlowStepConfig();

        Map<Integer, ObjectNode> configMap = new HashMap<>();
        ObjectNode defaultConfigNode = mapper.createObjectNode();

        Iterator<Map.Entry<String, JsonNode>> fields = root.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (key.startsWith("configurationList[")) {
                int indexStart = key.indexOf('[') + 1;
                int indexEnd = key.indexOf(']');
                int index = Integer.parseInt(key.substring(indexStart, indexEnd));
                String fieldName = key.substring(key.indexOf(']') + 2);

                ObjectNode node = configMap.computeIfAbsent(index, k -> mapper.createObjectNode());
                node.set(fieldName, value);

            } else if (key.startsWith("defaultConfig.")) {
                String fieldName = key.substring("defaultConfig.".length());
                defaultConfigNode.set(fieldName, value);
            }
        }

        // Deserialize configurationList entries
        List<DocTypeEnvelopeConfig> configList = new ArrayList<>();
        for (ObjectNode node : configMap.values()) {
        	DocTypeEnvelopeConfig config = mapper.treeToValue(node, DocTypeEnvelopeConfig.class);
            configList.add(config);
        }
        result.setDocTypeConfigs(configList);

        // Deserialize defaultConfig
        if (!defaultConfigNode.isEmpty()) {
        	DefaultEnvelopeConfig defaultConfig = mapper.treeToValue(defaultConfigNode, DefaultEnvelopeConfig.class);
            result.setDefaultConfig(defaultConfig);
        }

        return result;
    }
}
