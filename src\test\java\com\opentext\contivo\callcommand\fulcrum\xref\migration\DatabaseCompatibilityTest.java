package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;

import java.io.*;
import java.nio.file.Path;
import java.sql.*;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify database compatibility between Oracle and H2 syntax
 */
public class DatabaseCompatibilityTest {

    private static final String H2_DB_DRIVER = "org.h2.Driver";

    // Generate unique database URL for each test instance to avoid conflicts
    private final String h2DbUrl = "jdbc:h2:mem:testdb_" + System.nanoTime() + ";DB_CLOSE_DELAY=-1";
    
    // Test data
    private static final String TEST_FILE_CONTENT = 
        "\"wo1_CONTIVO\",\"\"\n" +
        "\"A\",\"Apple\"\n" +
        "\"MES-EMFC\",\"EMFC\"\n";
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() throws Exception {
        setupH2Database();
    }
    
    private void setupH2Database() throws Exception {
        Class.forName(H2_DB_DRIVER);

        try (Connection conn = DriverManager.getConnection(h2DbUrl)) {
            // Create database schema with proper cleanup
            String[] cleanupStatements = {
                "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_SEQ",
                "DROP SEQUENCE IF EXISTS TGTS_LOOKUP_ROW_SEQ",
                "DROP TABLE IF EXISTS TGTS_LOOKUP_COLUMN",
                "DROP TABLE IF EXISTS TGTS_LOOKUP_ROW",
                "DROP TABLE IF EXISTS TGTS_LOOKUP"
            };

            String[] createStatements = {
                "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
                "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
                
                "CREATE TABLE TGTS_LOOKUP (" +
                "  TABLE_ID BIGINT PRIMARY KEY," +
                "  SOLUTION_ID VARCHAR(255) NOT NULL," +
                "  TABLE_NAME VARCHAR(255) NOT NULL," +
                "  CREATE_DATE TIMESTAMP NOT NULL," +
                "  AVAILABLE_DATE TIMESTAMP" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_ROW (" +
                "  ROW_ID BIGINT PRIMARY KEY," +
                "  FK_TABLE_ID BIGINT NOT NULL," +
                "  ROW_TYPE INT NOT NULL," +
                "  FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_COLUMN (" +
                "  FK_ROW_ID BIGINT NOT NULL," +
                "  COL_NUM INT NOT NULL," +
                "  COL_VAL VARCHAR(4000)," +
                "  PRIMARY KEY (FK_ROW_ID, COL_NUM)," +
                "  FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID)" +
                ")"
            };

            try (Statement stmt = conn.createStatement()) {
                // Execute cleanup statements first (ignore errors for non-existent objects)
                for (String sql : cleanupStatements) {
                    try {
                        stmt.execute(sql);
                    } catch (SQLException e) {
                        // Ignore errors for non-existent objects
                    }
                }

                // Execute create statements
                for (String sql : createStatements) {
                    stmt.execute(sql);
                }
            }
        }
    }
    
    @Test
    void testDatabaseTypeDetection() throws Exception {
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            h2DbUrl, H2_DB_DRIVER, null, null);

        try (Connection conn = DriverManager.getConnection(h2DbUrl)) {
            // Test that database type detection works
            String productName = conn.getMetaData().getDatabaseProductName();
            assertTrue(productName.toLowerCase().contains("h2"), 
                      "Should detect H2 database: " + productName);
        }
    }
    
    @Test
    void testH2SequenceSyntax() throws Exception {
        // Test H2 sequence syntax directly
        try (Connection conn = DriverManager.getConnection(h2DbUrl)) {
            // Test NEXT VALUE FOR syntax (H2)
            String sql = "SELECT NEXT VALUE FOR TGTS_LOOKUP_SEQ";
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                assertTrue(rs.next());
                long value = rs.getLong(1);
                assertEquals(1000, value, "First sequence value should be 1000");
            }
            
            // Test second call
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                assertTrue(rs.next());
                long value = rs.getLong(1);
                assertEquals(1001, value, "Second sequence value should be 1001");
            }
        }
    }
    
    @Test
    void testMigrationWithH2Database() throws Exception {
        // Create test file
        Path testFile = tempDir.resolve("test_file.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write(TEST_FILE_CONTENT);
        }
        
        // Create migration utility
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            h2DbUrl, H2_DB_DRIVER, null, null);

        // Perform migration - this should work with H2 syntax
        FileToDatabaseMigrationUtility.MigrationResult result = migrator.migrateFileToDatabase(
            testFile.toString(), "TEST_SOLUTION", "test_table", true);

        assertNotNull(result, "Migration result should not be null");
        assertEquals(2, result.dataRowCount, "Should have 2 data rows");
        assertEquals(1, result.headerRowCount, "Should have 1 header row");

        // Verify data was inserted correctly
        try (Connection conn = DriverManager.getConnection(h2DbUrl)) {
            // Check table count
            String sql = "SELECT COUNT(*) FROM TGTS_LOOKUP WHERE SOLUTION_ID = ? AND TABLE_NAME = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    assertTrue(rs.next());
                    assertEquals(1, rs.getInt(1), "Should have 1 table entry");
                }
            }
            
            // Check row count
            sql = "SELECT COUNT(*) FROM TGTS_LOOKUP_ROW r " +
                  "JOIN TGTS_LOOKUP l ON r.FK_TABLE_ID = l.TABLE_ID " +
                  "WHERE l.SOLUTION_ID = ? AND l.TABLE_NAME = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    assertTrue(rs.next());
                    assertEquals(3, rs.getInt(1), "Should have 3 rows total (1 header + 2 data)");
                }
            }
            
            // Check column data
            sql = "SELECT COUNT(*) FROM TGTS_LOOKUP_COLUMN c " +
                  "JOIN TGTS_LOOKUP_ROW r ON c.FK_ROW_ID = r.ROW_ID " +
                  "JOIN TGTS_LOOKUP l ON r.FK_TABLE_ID = l.TABLE_ID " +
                  "WHERE l.SOLUTION_ID = ? AND l.TABLE_NAME = ?";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, "TEST_SOLUTION");
                stmt.setString(2, "test_table");
                try (ResultSet rs = stmt.executeQuery()) {
                    assertTrue(rs.next());
                    assertEquals(6, rs.getInt(1), "Should have 6 column entries (2 header + 4 data)");
                }
            }
        }
    }
    
    @Test
    void testSequenceGeneration() throws Exception {
        // Test that the migration utility generates proper sequence values
        Path testFile = tempDir.resolve("test_file.txt");
        try (FileWriter writer = new FileWriter(testFile.toFile())) {
            writer.write(TEST_FILE_CONTENT);
        }
        
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            h2DbUrl, H2_DB_DRIVER, null, null);
        
        // Perform first migration
        FileToDatabaseMigrationUtility.MigrationResult result1 = migrator.migrateFileToDatabase(
            testFile.toString(), "SOLUTION_1", "table_1", true);
        
        // Perform second migration
        FileToDatabaseMigrationUtility.MigrationResult result2 = migrator.migrateFileToDatabase(
            testFile.toString(), "SOLUTION_2", "table_2", true);
        
        // Verify different table IDs were generated
        assertNotEquals(result1.tableId, result2.tableId, 
                       "Different migrations should get different table IDs");
        
        // Verify sequence values are incrementing
        assertTrue(result2.tableId > result1.tableId, 
                  "Second table ID should be greater than first");
    }
    
    @Test
    void testDatabaseProductNameDetection() throws Exception {
        try (Connection conn = DriverManager.getConnection(h2DbUrl)) {
            DatabaseMetaData metaData = conn.getMetaData();
            String productName = metaData.getDatabaseProductName();
            String productVersion = metaData.getDatabaseProductVersion();
            
            System.out.println("Database Product: " + productName);
            System.out.println("Database Version: " + productVersion);
            
            assertTrue(productName.toLowerCase().contains("h2"), 
                      "Should be H2 database");
        }
    }
    
    @Test
    void testOracleCompatibilityMode() throws Exception {
        // Test that the utility defaults to Oracle syntax for unknown databases
        // This is a conceptual test since we can't easily test with actual Oracle
        
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            h2DbUrl, H2_DB_DRIVER, null, null);
        
        // The utility should work with H2 (detected) and default to Oracle for unknown
        assertNotNull(migrator, "Migration utility should be created");
        
        // If we had an unknown database type, it would default to Oracle syntax
        // This ensures backward compatibility
    }
}
