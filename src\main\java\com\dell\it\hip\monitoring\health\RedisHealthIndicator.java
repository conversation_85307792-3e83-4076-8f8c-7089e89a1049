package com.dell.it.hip.monitoring.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;

/**
 * Health indicator for Redis connectivity.
 * Only active when Redis is configured and available.
 */
@Component("redisHealthMonitor")
@ConditionalOnBean(HIPRedisCompatibilityService.class)
public class RedisHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(RedisHealthIndicator.class);

    @Autowired
    private HIPRedisCompatibilityService redisService;

    @Override
    public Health health() {
        try {
            // Test Redis connectivity with a simple ping through hash-based service
            String result = redisService.ping();

            if ("PONG".equals(result)) {
                logger.debug("Redis health check successful");
                return Health.up()
                        .withDetail("redis", "Available")
                        .withDetail("response", result)
                        .build();
            } else {
                logger.warn("Redis health check failed - unexpected response: {}", result);
                return Health.down()
                        .withDetail("redis", "Unexpected response")
                        .withDetail("response", result)
                        .build();
            }
        } catch (Exception ex) {
            logger.warn("Redis health check failed - connection error: {}", ex.getMessage());
            return Health.down()
                    .withDetail("redis", "Connection failed")
                    .withDetail("error", ex.getMessage())
                    .withDetail("type", ex.getClass().getSimpleName())
                    .build();
        }
    }
}
