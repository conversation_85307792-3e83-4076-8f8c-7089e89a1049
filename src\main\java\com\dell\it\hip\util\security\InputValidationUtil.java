package com.dell.it.hip.util.security;

import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.util.HtmlUtils;

/**
 * Utility class for validating and sanitizing user input to prevent security vulnerabilities
 * such as Cross-Site Scripting (XSS), SQL injection, and other injection attacks.
 * 
 * This utility follows OWASP guidelines for input validation and output encoding.
 */
@Component
public class InputValidationUtil {

    private static final Logger logger = LoggerFactory.getLogger(InputValidationUtil.class);

    // Regex patterns for validation
    private static final Pattern INTEGRATION_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9][a-zA-Z0-9._-]{0,63}$");
    private static final Pattern VERSION_PATTERN = Pattern.compile("^[a-zA-Z0-9][a-zA-Z0-9._-]{0,31}$");
    private static final Pattern ADAPTER_REF_PATTERN = Pattern.compile("^[a-zA-Z0-9][a-zA-Z0-9._-]{0,63}$");
    private static final Pattern HANDLER_REF_PATTERN = Pattern.compile("^[a-zA-Z0-9][a-zA-Z0-9._-]{0,63}$");
    
    // Characters and patterns that are potentially dangerous in various contexts
    private static final Pattern DANGEROUS_CHARS_PATTERN = Pattern.compile(
        "(?i)[<>\"'&;\\x00-\\x1f\\x7f-\\x9f]|" +
        "javascript:|vbscript:|" +
        "on\\w+\\s*=|" +
        "alert\\s*\\(|eval\\s*\\(|" +
        "document\\.|window\\.|" +
        "<\\s*script|<\\s*iframe|<\\s*object|<\\s*embed|<\\s*svg"
    );
    
    // Maximum lengths for different input types
    private static final int MAX_INTEGRATION_NAME_LENGTH = 255;
    private static final int MAX_VERSION_LENGTH = 32;
    private static final int MAX_REF_LENGTH = 255;

    /**
     * Validates and sanitizes an integration name.
     * 
     * @param integrationName The integration name to validate
     * @return The sanitized integration name
     * @throws IllegalArgumentException if the input is invalid
     */
    public static String validateAndSanitizeIntegrationName(String integrationName) {
        if (integrationName == null) {
            throw new IllegalArgumentException("Integration name cannot be null");
        }
        
        String trimmed = integrationName.trim();
        
        if (trimmed.isEmpty()) {
            throw new IllegalArgumentException("Integration name cannot be empty");
        }
        
        if (trimmed.length() > MAX_INTEGRATION_NAME_LENGTH) {
            throw new IllegalArgumentException("Integration name exceeds maximum length of " + MAX_INTEGRATION_NAME_LENGTH);
        }
        
        if (!INTEGRATION_NAME_PATTERN.matcher(trimmed).matches()) {
            throw new IllegalArgumentException("Integration name contains invalid characters. Only alphanumeric, dots, hyphens, and underscores are allowed");
        }
        
        // Additional check for dangerous characters
        if (DANGEROUS_CHARS_PATTERN.matcher(trimmed).find()) {
            logger.warn("Potentially dangerous characters detected in integration name: {}", trimmed);
            throw new IllegalArgumentException("Integration name contains potentially dangerous characters");
        }
        
        return trimmed;
    }

    /**
     * Validates and sanitizes a version string.
     * 
     * @param version The version to validate
     * @return The sanitized version
     * @throws IllegalArgumentException if the input is invalid
     */
    public static String validateAndSanitizeVersion(String version) {
        if (version == null) {
            throw new IllegalArgumentException("Version cannot be null");
        }
        
        String trimmed = version.trim();
        
        if (trimmed.isEmpty()) {
            throw new IllegalArgumentException("Version cannot be empty");
        }
        
        if (trimmed.length() > MAX_VERSION_LENGTH) {
            throw new IllegalArgumentException("Version exceeds maximum length of " + MAX_VERSION_LENGTH);
        }
        
        if (!VERSION_PATTERN.matcher(trimmed).matches()) {
            throw new IllegalArgumentException("Version contains invalid characters. Only alphanumeric, dots, hyphens, and underscores are allowed");
        }
        
        // Additional check for dangerous characters
        if (DANGEROUS_CHARS_PATTERN.matcher(trimmed).find()) {
            logger.warn("Potentially dangerous characters detected in version: {}", trimmed);
            throw new IllegalArgumentException("Version contains potentially dangerous characters");
        }
        
        return trimmed;
    }

    /**
     * Validates and sanitizes an adapter reference.
     * 
     * @param adapterRef The adapter reference to validate
     * @return The sanitized adapter reference
     * @throws IllegalArgumentException if the input is invalid
     */
    public static String validateAndSanitizeAdapterRef(String adapterRef) {
        if (adapterRef == null) {
            throw new IllegalArgumentException("Adapter reference cannot be null");
        }
        
        String trimmed = adapterRef.trim();
        
        if (trimmed.isEmpty()) {
            throw new IllegalArgumentException("Adapter reference cannot be empty");
        }
        
        if (trimmed.length() > MAX_REF_LENGTH) {
            throw new IllegalArgumentException("Adapter reference exceeds maximum length of " + MAX_REF_LENGTH);
        }
        
        if (!ADAPTER_REF_PATTERN.matcher(trimmed).matches()) {
            throw new IllegalArgumentException("Adapter reference contains invalid characters. Only alphanumeric, dots, hyphens, and underscores are allowed");
        }
        
        // Additional check for dangerous characters
        if (DANGEROUS_CHARS_PATTERN.matcher(trimmed).find()) {
            logger.warn("Potentially dangerous characters detected in adapter reference: {}", trimmed);
            throw new IllegalArgumentException("Adapter reference contains potentially dangerous characters");
        }
        
        return trimmed;
    }

    /**
     * Validates and sanitizes a handler reference.
     * 
     * @param handlerRef The handler reference to validate
     * @return The sanitized handler reference
     * @throws IllegalArgumentException if the input is invalid
     */
    public static String validateAndSanitizeHandlerRef(String handlerRef) {
        if (handlerRef == null) {
            throw new IllegalArgumentException("Handler reference cannot be null");
        }
        
        String trimmed = handlerRef.trim();
        
        if (trimmed.isEmpty()) {
            throw new IllegalArgumentException("Handler reference cannot be empty");
        }
        
        if (trimmed.length() > MAX_REF_LENGTH) {
            throw new IllegalArgumentException("Handler reference exceeds maximum length of " + MAX_REF_LENGTH);
        }
        
        if (!HANDLER_REF_PATTERN.matcher(trimmed).matches()) {
            throw new IllegalArgumentException("Handler reference contains invalid characters. Only alphanumeric, dots, hyphens, and underscores are allowed");
        }
        
        // Additional check for dangerous characters
        if (DANGEROUS_CHARS_PATTERN.matcher(trimmed).find()) {
            logger.warn("Potentially dangerous characters detected in handler reference: {}", trimmed);
            throw new IllegalArgumentException("Handler reference contains potentially dangerous characters");
        }
        
        return trimmed;
    }

    /**
     * Sanitizes a string for safe inclusion in error messages or logs.
     * This method HTML-escapes the input to prevent XSS attacks.
     *
     * @param input The input string to sanitize
     * @return The sanitized string safe for inclusion in HTML contexts
     */
    public static String sanitizeForErrorMessage(String input) {
        if (input == null) {
            return "null";
        }

        // HTML escape the input to prevent XSS using Spring's HtmlUtils
        String escaped = HtmlUtils.htmlEscape(input);

        // Truncate if too long to prevent log pollution
        if (escaped.length() > 200) {
            escaped = escaped.substring(0, 197) + "...";
        }

        return escaped;
    }

    /**
     * Validates that a string contains only safe characters for logging.
     * This helps prevent log injection attacks.
     * 
     * @param input The input to validate
     * @return The sanitized input safe for logging
     */
    public static String sanitizeForLogging(String input) {
        if (input == null) {
            return "null";
        }
        
        // Remove or replace dangerous characters for logging
        String sanitized = input.replaceAll("[\r\n\t]", "_")
                               .replaceAll("[\\x00-\\x1f\\x7f-\\x9f]", "?");
        
        // Truncate if too long
        if (sanitized.length() > 500) {
            sanitized = sanitized.substring(0, 497) + "...";
        }
        
        return sanitized;
    }

    /**
     * Checks if a string is potentially dangerous (contains XSS vectors).
     *
     * @param input The input to check
     * @return true if the input contains potentially dangerous characters
     */
    public static boolean containsDangerousCharacters(String input) {
        if (input == null) {
            return false;
        }

        return DANGEROUS_CHARS_PATTERN.matcher(input).find();
    }

    /**
     * Sanitizes a string for safe output in HTTP responses.
     * This method provides additional protection against XSS attacks by ensuring
     * that any output is properly encoded for the intended context.
     *
     * @param output The output string to sanitize
     * @return The sanitized string safe for HTTP response output
     */
    public static String sanitizeForOutput(String output) {
        if (output == null) {
            return null;
        }

        // For TEXT_PLAIN responses, we still want to ensure no dangerous characters
        // Even though enum values are safe, this provides defense in depth
        String sanitized = output.trim();

        // Additional validation to ensure output contains only safe characters
        if (containsDangerousCharacters(sanitized)) {
            logger.warn("Potentially dangerous characters detected in output: {}", sanitizeForLogging(sanitized));
            // For enum values, this should never happen, but if it does, return a safe default
            return "ERROR";
        }

        return sanitized;
    }
}
