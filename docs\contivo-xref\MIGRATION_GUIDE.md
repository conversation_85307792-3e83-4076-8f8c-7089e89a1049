# FCXRefCallCmd File-to-Database Migration Guide

## Overview

This guide provides step-by-step instructions for migrating FCXRefCallCmd lookup functionality from file-based storage (`fc.fs.path`) to database storage (`fc.db.*`) while ensuring identical lookup results.

## File Analysis Results

### Your Current File: "DELL.COM.01_wo1_CONTIVO 1.txt"

**File Structure:**
```
"wo1_CONTIVO",""
"A","Apple"
"MES-EMFC","EMFC"
"MES-AMFF","AMFF"
"COMPAL","D"
```

**Analysis:**
- **Format**: CSV with quoted values
- **Headers**: Line 1 contains headers: `"wo1_CONTIVO"` (column 1), `""` (column 2 - empty string)
- **Data Rows**: 4 data rows (lines 2-5)
- **Columns**: 2 columns per row
- **File Naming Issue**: Your file doesn't follow the expected naming convention

### File Naming Convention Issue

The `FileSystemLookUp` class expects files named:
- `{solutionID}_{tableName}_hdr.txt` (for files with headers)
- `{solutionID}_{tableName}.txt` (for files without headers)

Your file should be renamed to match this pattern, for example:
- `DELL.COM.01_wo1_CONTIVO_hdr.txt` (if DELL.COM.01 is your solution ID and wo1_CONTIVO is your table name)

## Migration Steps

### Step 1: Database Schema Setup

1. **Run the Oracle schema setup script:**
```sql
-- Execute oracle_schema_setup.sql in your Oracle database
@oracle_schema_setup.sql
```

2. **Verify schema creation:**
```sql
SELECT table_name FROM user_tables 
WHERE table_name IN ('TGTS_LOOKUP', 'TGTS_LOOKUP_ROW', 'TGTS_LOOKUP_COLUMN');
```

### Step 2: Configure Database Connection

Update your application properties:

**Before (File-based):**
```properties
fc.fs.path=C:\path\to\your\files
fc.solutionID=DELL.COM.01
```

**After (Database-based):**
```properties
# Remove or comment out fc.fs.path
# fc.fs.path=

# Add database configuration
fc.db.url=***********************************
fc.db.driver=oracle.jdbc.driver.OracleDriver
fc.db.user=your_username
fc.db.password=your_password
fc.solutionID=DELL.COM.01
```

### Step 3: Migrate File Data to Database

```java
public class MigrationExample {
    public static void main(String[] args) throws Exception {
        // Database configuration
        String dbUrl = "***********************************";
        String dbDriver = "oracle.jdbc.driver.OracleDriver";
        String dbUser = "your_username";
        String dbPassword = "your_password";
        
        // Migration parameters
        String filePath = "DELL.COM.01_wo1_CONTIVO 1.txt";
        String solutionId = "DELL.COM.01";
        String tableName = "wo1_CONTIVO";
        boolean hasHeaders = true;  // Your file has headers
        
        // Create migration utility
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            dbUrl, dbDriver, dbUser, dbPassword);
        
        // Perform migration
        FileToDatabaseMigrationUtility.MigrationResult result = 
            migrator.migrateFileToDatabase(filePath, solutionId, tableName, hasHeaders);
        
        System.out.println("Migration completed: " + result);
    }
}
```

### Step 4: Validation Testing

```java
public class ValidationExample {
    public static void main(String[] args) throws Exception {
        // Setup file system lookup (for comparison)
        File fileSystemPath = new File("path/to/your/files");
        
        // Setup database lookup
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(
            "***********************************", 
            "oracle.jdbc.driver.OracleDriver", 
            "username", "password");
        
        // Create validation framework
        MigrationValidationFramework validator = new MigrationValidationFramework(
            fileSystemPath, dbPool, "DELL.COM.01", "wo1_CONTIVO");
        
        // Run comprehensive validation
        MigrationValidationFramework.ValidationResult result = 
            validator.runComprehensiveValidation();
        
        // Print detailed report
        result.printReport();
        
        if (result.isSuccess()) {
            System.out.println("✅ Migration validation PASSED - Safe to switch to database mode");
        } else {
            System.out.println("❌ Migration validation FAILED - Review issues before switching");
        }
    }
}
```

## Expected Database Structure After Migration

### TGTS_LOOKUP Table
```
TABLE_ID | SOLUTION_ID | TABLE_NAME  | CREATE_DATE         | AVAILABLE_DATE
---------|-------------|-------------|--------------------|--------------
1001     | DELL.COM.01 | wo1_CONTIVO | 2024-01-15 10:00:00| NULL
```

### TGTS_LOOKUP_ROW Table
```
ROW_ID | FK_TABLE_ID | ROW_TYPE
-------|-------------|----------
2001   | 1001        | 1        -- Header row
2002   | 1001        | 0        -- Data row: A -> Apple
2003   | 1001        | 0        -- Data row: MES-EMFC -> EMFC
2004   | 1001        | 0        -- Data row: MES-AMFF -> AMFF
2005   | 1001        | 0        -- Data row: COMPAL -> D
```

### TGTS_LOOKUP_COLUMN Table
```
FK_ROW_ID | COL_NUM | COL_VAL
----------|---------|----------
2001      | 1       | wo1_CONTIVO  -- Header col 1
2001      | 2       |              -- Header col 2 (empty)
2002      | 1       | A            -- Data
2002      | 2       | Apple        -- Data
2003      | 1       | MES-EMFC     -- Data
2003      | 2       | EMFC         -- Data
2004      | 1       | MES-AMFF     -- Data
2004      | 2       | AMFF         -- Data
2005      | 1       | COMPAL       -- Data
2005      | 2       | D            -- Data
```

## Lookup Examples

### Example 1: Lookup by Column Number
```java
// Both file and database should return "Apple"
String result = lookupClient.lookupValue(
    "DELL.COM.01",           // Solution ID
    "wo1_CONTIVO",           // Table name
    new String[]{"1"},       // Find in column 1
    "2",                     // Return column 2
    new String[]{"A"},       // Find value "A"
    new Op[]{Op.EQ},        // Equals operation
    false                    // Use column numbers
);
// Result: "Apple"
```

### Example 2: Lookup by Column Name
```java
// Both file and database should return "Apple"
String result = lookupClient.lookupValue(
    "DELL.COM.01",           // Solution ID
    "wo1_CONTIVO",           // Table name
    new String[]{"wo1_CONTIVO"}, // Find in column named "wo1_CONTIVO"
    "",                      // Return column named "" (empty string)
    new String[]{"A"},       // Find value "A"
    new Op[]{Op.EQ},        // Equals operation
    true                     // Use column names
);
// Result: "Apple"
```

### Example 3: Case Insensitive Lookup
```java
// Both file and database should return "Apple" for lowercase "a"
String result = lookupClient.lookupValue(
    "DELL.COM.01",           // Solution ID
    "wo1_CONTIVO",           // Table name
    new String[]{"1"},       // Find in column 1
    "2",                     // Return column 2
    new String[]{"a"},       // Find value "a" (lowercase)
    new Op[]{Op.CI},        // Case insensitive operation
    false                    // Use column numbers
);
// Result: "Apple"
```

## Testing Strategy

### Unit Tests
Run the comprehensive test suite:
```bash
mvn test -Dtest=FCXRefMigrationTest
```

### Manual Validation
1. **Before Migration**: Test all your lookup scenarios with `fc.fs.path` configuration
2. **After Migration**: Test identical scenarios with `fc.db.*` configuration
3. **Compare Results**: Ensure all results are identical

### Performance Testing
```java
// Compare performance between file and database approaches
MigrationValidationFramework validator = new MigrationValidationFramework(...);
ValidationResult result = validator.runComprehensiveValidation();
// Check performance test results in the output
```

## Troubleshooting

### Common Issues

1. **File Naming Convention**
   - **Problem**: FileSystemLookUp can't find your file
   - **Solution**: Rename file to follow `{solutionID}_{tableName}_hdr.txt` pattern

2. **Header Detection**
   - **Problem**: Column name lookups fail
   - **Solution**: Ensure `hasHeaders=true` in migration and file ends with `_hdr.txt`

3. **Empty String Columns**
   - **Problem**: Lookups fail for empty column headers
   - **Solution**: Use empty string `""` as column name in lookups

4. **Database Connection**
   - **Problem**: Cannot connect to Oracle database
   - **Solution**: Verify JDBC URL, driver, credentials, and network connectivity

5. **Performance Differences**
   - **Problem**: Database lookups slower than file lookups
   - **Solution**: Ensure proper indexing and consider connection pooling

### Validation Failures

If validation tests fail:

1. **Check Data Integrity**: Verify migration copied all data correctly
2. **Review Parsing Logic**: Ensure file parsing matches TableParser behavior
3. **Test Edge Cases**: Verify handling of empty values, special characters
4. **Check Configuration**: Ensure identical solutionID and tableName values

## Rollback Plan

If issues occur after migration:

1. **Immediate Rollback**: Change configuration back to `fc.fs.path`
2. **Keep Original Files**: Don't delete original files until migration is fully validated
3. **Database Cleanup**: Use `CLEANUP_LOOKUP_DATA` procedure if needed

## Performance Considerations

### File-based Advantages
- Faster for small datasets
- No network latency
- Simple deployment

### Database-based Advantages
- Better for large datasets
- Centralized data management
- Concurrent access support
- Data versioning capabilities
- Better integration with enterprise systems

## Next Steps

1. **Test Migration**: Run migration utility on your actual file
2. **Validate Results**: Execute comprehensive validation framework
3. **Performance Test**: Compare performance characteristics
4. **Deploy Gradually**: Consider phased rollout if you have multiple files
5. **Monitor**: Watch for any behavioral differences in production

## Support

For issues or questions:
1. Review the comprehensive test results
2. Check database logs for connection/query issues
3. Verify file parsing matches expected format
4. Ensure all configuration properties are correctly set

The migration framework provides extensive validation to ensure identical behavior between file and database approaches. Follow this guide carefully and run all validation tests before switching to database mode in production.
