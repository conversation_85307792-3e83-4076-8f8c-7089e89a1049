package com.dell.it.hip.strategy;

import static org.junit.jupiter.api.Assertions.*;

import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Comprehensive test suite for special character handling validation.
 * Tests core character encoding functionality that should be consistent
 * across all HIP adapters and handlers.
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("Special Character Handling Validation Tests")
class SpecialCharacterHandlingTest {

    @Test
    @DisplayName("Should preserve Unicode characters in UTF-8 encoding")
    void testUnicodeCharacterPreservation() {
        String unicodeText = SpecialCharacterTestData.UNICODE_BASIC;
        
        // Test byte conversion and reconstruction
        byte[] bytes = unicodeText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(unicodeText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(unicodeText, reconstructed));
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters(reconstructed));
    }

    @Test
    @DisplayName("Should preserve emoji characters in UTF-8 encoding")
    void testEmojiCharacterPreservation() {
        String emojiText = SpecialCharacterTestData.EMOJI_STATUS;
        
        // Test byte conversion and reconstruction
        byte[] bytes = emojiText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(emojiText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateByteConversion(emojiText, bytes));
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters(reconstructed));
    }

    @Test
    @DisplayName("Should handle multi-language text correctly")
    void testMultiLanguageTextHandling() {
        String multiLangText = SpecialCharacterTestData.UNICODE_EXTENDED;
        
        // Test byte conversion and reconstruction
        byte[] bytes = multiLangText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(multiLangText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(multiLangText, reconstructed));
        
        // Verify it's not ASCII-only (contains non-ASCII characters)
        assertFalse(SpecialCharacterTestData.isAsciiOnly(multiLangText));
    }

    @Test
    @DisplayName("Should handle special XML/HTML characters")
    void testXmlHtmlCharacterHandling() {
        String xmlText = SpecialCharacterTestData.XML_SPECIAL_CHARS;
        
        // Test byte conversion and reconstruction
        byte[] bytes = xmlText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(xmlText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateByteConversion(xmlText, bytes));
        
        // Verify special characters are preserved
        assertTrue(reconstructed.contains("<tag"));
        assertTrue(reconstructed.contains("&amp;"));
        assertTrue(reconstructed.contains("&lt;"));
        assertTrue(reconstructed.contains("&gt;"));
    }

    @Test
    @DisplayName("Should handle JSON special characters")
    void testJsonCharacterHandling() {
        String jsonText = SpecialCharacterTestData.JSON_SPECIAL_CHARS;
        
        // Test byte conversion and reconstruction
        byte[] bytes = jsonText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(jsonText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(jsonText, reconstructed));
        
        // Verify JSON structure is preserved
        assertTrue(reconstructed.contains("{\"message\":"));
        assertTrue(reconstructed.contains("\\\"World\\\""));
        assertTrue(reconstructed.contains("\"symbols\":"));
    }

    @Test
    @DisplayName("Should handle zero-width and combining characters")
    void testZeroWidthCharacterHandling() {
        String zwText = SpecialCharacterTestData.ZERO_WIDTH_CHARS;
        
        // Test byte conversion and reconstruction
        byte[] bytes = zwText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(zwText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateByteConversion(zwText, bytes));
        
        // Zero-width characters should increase byte length but not visual length
        assertTrue(SpecialCharacterTestData.getUtf8ByteLength(zwText) > zwText.replaceAll("[\u200B\u200C\u200D\u2060]", "").length());
    }

    @Test
    @DisplayName("Should handle mixed content with various character types")
    void testMixedContentHandling() {
        String mixedText = SpecialCharacterTestData.MIXED_CONTENT;
        
        // Test byte conversion and reconstruction
        byte[] bytes = mixedText.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(mixedText, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(mixedText, reconstructed));
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters(reconstructed));
        
        // Verify mixed content elements are preserved
        assertTrue(reconstructed.contains("Hello"));
        assertTrue(reconstructed.contains("世界"));
        assertTrue(reconstructed.contains("🌍"));
        assertTrue(reconstructed.contains("©®™"));
        assertTrue(reconstructed.contains("∑∏∆"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "Hello World with accents: àáâãäåæçèéêë",
        "Status: ✅ Success! 🎉 Processing complete 🚀",
        "English, Español, Français, Deutsch, 中文, 日本語, العربية, Русский",
        "<tag attr=\"value\">&amp;&lt;&gt;&quot;&#39;</tag>",
        "{\"message\": \"Hello \\\"World\\\"\", \"symbols\": \"{}[]:,\"}",
        "Mixed content: Hello 世界 🌍 with symbols: ©®™"
    })
    @DisplayName("Should handle various special character patterns")
    void testVariousSpecialCharacterPatterns(String testString) {
        // Test byte conversion and reconstruction
        byte[] bytes = testString.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(testString, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(testString, reconstructed));
        assertTrue(SpecialCharacterTestData.validateByteConversion(testString, bytes));
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters(reconstructed));
    }

    @Test
    @DisplayName("Should handle large Unicode content efficiently")
    void testLargeUnicodeContentHandling() {
        String largeContent = SpecialCharacterTestData.generateLargeUnicodeContent(100);
        
        // Test byte conversion and reconstruction
        byte[] bytes = largeContent.getBytes(StandardCharsets.UTF_8);
        String reconstructed = new String(bytes, StandardCharsets.UTF_8);
        
        assertEquals(largeContent, reconstructed);
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(largeContent, reconstructed));
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters(reconstructed));
        
        // Verify content size and structure
        assertTrue(largeContent.length() > 1000);
        assertTrue(largeContent.contains("Unicode text 中文 🌍"));
        assertTrue(largeContent.contains("Arabic: مرحبا"));
        assertTrue(largeContent.contains("Russian: Привет"));
    }

    @Test
    @DisplayName("Should handle null and empty strings gracefully")
    void testNullAndEmptyStringHandling() {
        // Test null handling
        assertTrue(SpecialCharacterTestData.validateUnicodeIntegrity(null, null));
        assertFalse(SpecialCharacterTestData.validateUnicodeIntegrity("test", null));
        assertFalse(SpecialCharacterTestData.validateUnicodeIntegrity(null, "test"));
        
        // Test empty string handling
        String empty = "";
        byte[] emptyBytes = empty.getBytes(StandardCharsets.UTF_8);
        String reconstructedEmpty = new String(emptyBytes, StandardCharsets.UTF_8);
        
        assertEquals(empty, reconstructedEmpty);
        assertTrue(SpecialCharacterTestData.validateByteConversion(empty, emptyBytes));
        assertEquals(0, emptyBytes.length);
    }

    @Test
    @DisplayName("Should correctly count Unicode code points")
    void testUnicodeCodePointCounting() {
        // Test basic ASCII
        assertEquals(5, SpecialCharacterTestData.countCodePoints("Hello"));
        
        // Test Unicode characters
        assertEquals(1, SpecialCharacterTestData.countCodePoints("🌍")); // Single emoji
        assertEquals(2, SpecialCharacterTestData.countCodePoints("🌍🚀")); // Two emojis
        
        // Test mixed content
        String mixed = "Hello 🌍";
        assertEquals(7, SpecialCharacterTestData.countCodePoints(mixed)); // 6 ASCII + 1 emoji
        
        // Test combining characters
        String combining = "e\u0301"; // e + combining acute accent
        assertEquals(2, SpecialCharacterTestData.countCodePoints(combining)); // Base + combining
    }

    @Test
    @DisplayName("Should validate test data utility methods")
    void testUtilityMethods() {
        // Test ASCII detection
        assertTrue(SpecialCharacterTestData.isAsciiOnly("Hello World"));
        assertFalse(SpecialCharacterTestData.isAsciiOnly("Hello 世界"));
        assertFalse(SpecialCharacterTestData.isAsciiOnly("Hello 🌍"));
        
        // Test UTF-8 byte length calculation
        assertEquals(5, SpecialCharacterTestData.getUtf8ByteLength("Hello"));
        assertTrue(SpecialCharacterTestData.getUtf8ByteLength("Hello 世界") > 8); // Chinese chars are multi-byte
        assertTrue(SpecialCharacterTestData.getUtf8ByteLength("Hello 🌍") > 7); // Emoji is multi-byte
        
        // Test replacement character detection
        assertFalse(SpecialCharacterTestData.containsReplacementCharacters("Hello World"));
        assertTrue(SpecialCharacterTestData.containsReplacementCharacters("Hello \uFFFD World"));
    }

    @Test
    @DisplayName("Should provide comprehensive test data collections")
    void testDataCollections() {
        // Verify test data collections are not empty
        assertFalse(SpecialCharacterTestData.ALL_BASIC_TESTS.isEmpty());
        assertFalse(SpecialCharacterTestData.ALL_LANGUAGE_TESTS.isEmpty());
        assertFalse(SpecialCharacterTestData.ALL_EMOJI_TESTS.isEmpty());
        assertFalse(SpecialCharacterTestData.ALL_SPECIAL_CHAR_TESTS.isEmpty());
        
        // Verify all test data can be processed without errors
        for (String testData : SpecialCharacterTestData.ALL_BASIC_TESTS) {
            assertNotNull(testData);
            assertFalse(testData.isEmpty());
            assertTrue(SpecialCharacterTestData.validateByteConversion(testData, 
                      testData.getBytes(StandardCharsets.UTF_8)));
        }
    }
}
