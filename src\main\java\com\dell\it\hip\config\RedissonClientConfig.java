package com.dell.it.hip.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson client configuration for distributed Redis operations.
 * Only created when Redis is enabled via spring.data.redis.enabled=true.
 */
@Configuration
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class RedissonClientConfig {

	private static final Logger logger = LoggerFactory.getLogger(RedissonClientConfig.class);

	@Value("${spring.data.redis.url}")
	private String redisUrl;

	@Value("${spring.data.redis.password:}")
	private String redisPassword;

	@Bean
    public RedissonClient redissonClient(Config config) {
		logger.info("Creating RedissonClient with Redis URL: {}", redisUrl);
		try {
			RedissonClient redissonClient = Redisson.create(config);
			logger.info("RedissonClient created successfully");
			return redissonClient;
		} catch (Exception e) {
			logger.error("Failed to create RedissonClient: {}", e.getMessage(), e);
			throw new RuntimeException("Redisson client creation failed", e);
		}
    }

	@Bean
    public Config config() {
		logger.info("Creating Redisson Config for URL: {}", redisUrl);
		Config config = new Config();

		// Configure single server with password from configuration
		// Use empty password if not configured (for development/testing environments)
		if (redisPassword != null && !redisPassword.trim().isEmpty()) {
			config.useSingleServer()
				.setPassword(redisPassword)
				.setKeepAlive(true)
				.setAddress(redisUrl);
			logger.info("Redis password configured from properties");
		} else {
			config.useSingleServer()
				.setKeepAlive(true)
				.setAddress(redisUrl);
			logger.info("Redis configured without password");
		}

        return config;
    }

}
