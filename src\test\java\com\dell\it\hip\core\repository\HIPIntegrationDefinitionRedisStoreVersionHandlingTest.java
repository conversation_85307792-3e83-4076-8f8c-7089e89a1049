package com.dell.it.hip.core.repository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Unit tests for HIPIntegrationDefinitionRedisStore focusing on version field handling.
 * Tests the consistency between BigDecimal storage and String conversion for version fields.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationDefinitionRedisStoreVersionHandlingTest {

    @Mock
    private HIPRedisCompatibilityService redisService;

    @InjectMocks
    private HIPIntegrationDefinitionRedisStore redisStore;

    private static final String SERVICE_MANAGER = "test-service-manager";
    private static final String INTEGRATION_NAME = "test-integration";
    private static final String VERSION_SIMPLE = "1.0";
    private static final String VERSION_SEMANTIC = "1.2.3";
    private static final String VERSION_COMPLEX = "2.1.5";

    @BeforeEach
    void setUp() {
        // Setup lenient mode to avoid unnecessary stubbing exceptions
        lenient().doNothing().when(redisService).set(anyString(), anyString());
        lenient().when(redisService.get(anyString())).thenReturn(null);
        lenient().when(redisService.hasKey(anyString())).thenReturn(false);
        lenient().when(redisService.delete(anyString())).thenReturn(true);
        lenient().when(redisService.keys(anyString())).thenReturn(new HashSet<>());
    }

    @Test
    void testSave_SimpleDecimalVersion_ConsistentSerialization() {
        // Arrange
        HIPIntegrationRequestEntity entity = createTestEntity(VERSION_SIMPLE);
        String expectedKey = "hip_integration:" + SERVICE_MANAGER + ":" + INTEGRATION_NAME + ":" + VERSION_SIMPLE;

        // Act
        redisStore.save(entity);

        // Assert
        verify(redisService).set(eq(expectedKey), anyString());

        // Verify that the entity's version handling is consistent
        assertEquals(VERSION_SIMPLE, entity.getVersion());
        assertEquals(new BigDecimal(VERSION_SIMPLE), entity.getVersionBigDecimal());
    }

    @Test
    void testSave_SemanticVersion_ConsistentSerialization() {
        // Arrange
        HIPIntegrationRequestEntity entity = createTestEntity(VERSION_SEMANTIC);
        String expectedKey = "hip_integration:" + SERVICE_MANAGER + ":" + INTEGRATION_NAME + ":" + "1.0203"; // Converted format

        // Act
        redisStore.save(entity);

        // Assert
        verify(redisService).set(eq(expectedKey), anyString());

        // Verify semantic version conversion
        assertEquals("1.0203", entity.getVersion()); // Should be converted to decimal format
        assertEquals(new BigDecimal("1.0203"), entity.getVersionBigDecimal());
    }

    @Test
    void testFind_SimpleDecimalVersion_ConsistentDeserialization() throws Exception {
        // Arrange
        HIPIntegrationRequestEntity originalEntity = createTestEntity(VERSION_SIMPLE);
        ObjectMapper mapper = new ObjectMapper();
        String serializedEntity = mapper.writeValueAsString(originalEntity);
        String redisKey = "hip_integration:" + SERVICE_MANAGER + ":" + INTEGRATION_NAME + ":" + VERSION_SIMPLE;

        when(redisService.get(redisKey)).thenReturn(serializedEntity);

        // Act
        HIPIntegrationRequestEntity retrievedEntity = redisStore.find(SERVICE_MANAGER, INTEGRATION_NAME, VERSION_SIMPLE);

        // Assert
        assertNotNull(retrievedEntity);
        assertEquals(originalEntity.getServiceManagerName(), retrievedEntity.getServiceManagerName());
        assertEquals(originalEntity.getHipIntegrationName(), retrievedEntity.getHipIntegrationName());
        assertEquals(originalEntity.getVersion(), retrievedEntity.getVersion());
        assertEquals(originalEntity.getVersionBigDecimal(), retrievedEntity.getVersionBigDecimal());
    }

    @Test
    void testFind_SemanticVersion_ConsistentDeserialization() throws Exception {
        // Arrange
        HIPIntegrationRequestEntity originalEntity = createTestEntity(VERSION_SEMANTIC);
        ObjectMapper mapper = new ObjectMapper();
        String serializedEntity = mapper.writeValueAsString(originalEntity);
        String convertedVersion = "1.0203"; // Expected converted format
        String redisKey = "hip_integration:" + SERVICE_MANAGER + ":" + INTEGRATION_NAME + ":" + convertedVersion;

        when(redisService.get(redisKey)).thenReturn(serializedEntity);

        // Act
        HIPIntegrationRequestEntity retrievedEntity = redisStore.find(SERVICE_MANAGER, INTEGRATION_NAME, convertedVersion);

        // Assert
        assertNotNull(retrievedEntity);
        assertEquals(originalEntity.getServiceManagerName(), retrievedEntity.getServiceManagerName());
        assertEquals(originalEntity.getHipIntegrationName(), retrievedEntity.getHipIntegrationName());
        assertEquals(convertedVersion, retrievedEntity.getVersion());
        assertEquals(new BigDecimal(convertedVersion), retrievedEntity.getVersionBigDecimal());
    }

    @Test
    void testFind_NonExistentEntity_ReturnsNull() {
        // Arrange
        String redisKey = "hip_integration:" + SERVICE_MANAGER + ":" + INTEGRATION_NAME + ":" + VERSION_SIMPLE;
        when(redisService.get(redisKey)).thenReturn(null);

        // Act
        HIPIntegrationRequestEntity result = redisStore.find(SERVICE_MANAGER, INTEGRATION_NAME, VERSION_SIMPLE);

        // Assert
        assertNull(result);
    }

    @Test
    void testVersionFieldConsistency_NullVersion_HandledGracefully() {
        // Arrange
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();
        entity.setServiceManagerName(SERVICE_MANAGER);
        entity.setHipIntegrationName(INTEGRATION_NAME);
        entity.setBusinessFlowName("test-flow");
        entity.setVersion(null); // Null version

        // Act & Assert - should throw exception for null version since it's required for key generation
        assertThrows(RuntimeException.class, () -> {
            redisStore.save(entity);
        });

        assertNull(entity.getVersion());
        assertNull(entity.getVersionBigDecimal());
    }

    /**
     * Helper method to create a test entity with consistent version handling
     */
    private HIPIntegrationRequestEntity createTestEntity(String version) {
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();
        entity.setServiceManagerName(SERVICE_MANAGER);
        entity.setHipIntegrationName(INTEGRATION_NAME);
        entity.setVersion(version); // This will trigger BigDecimal conversion
        entity.setBusinessFlowName("test-business-flow");
        entity.setTags("test,tags");
        return entity;
    }
}
