package com.dell.it.hip.util.redis;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Security-focused tests for HIPRedisCompatibilityService to verify protection against
 * regex injection attacks and proper handling of malicious patterns.
 */
@ExtendWith(MockitoExtension.class)
public class HIPRedisCompatibilityServiceSecurityTest {

    @Mock
    private HIPRedisHashService hashService;

    private HIPRedisCompatibilityService redisService;

    @BeforeEach
    void setUp() {
        redisService = new HIPRedisCompatibilityService(hashService, null, null);
    }

    @Test
    @DisplayName("Should safely handle regex metacharacters in patterns")
    void testRegexMetacharactersSafety() {
        // Arrange - create test data with keys that could match if regex injection succeeds
        Set<Object> testFields = new HashSet<>();
        testFields.add("normal:key:1.0:flowrule:test");
        testFields.add("another:key:2.0:flowrule:data");
        testFields.add("special.key.with.dots");
        
        when(hashService.getFields()).thenReturn(testFields);

        // Test various regex metacharacters that could be used for injection
        String[] maliciousPatterns = {
            ".*",           // Should match everything if not properly escaped
            ".+",           // Should match everything if not properly escaped  
            "^.*$",         // Anchors that could match everything
            "normal.*",     // Partial pattern with metacharacter
            "normal.+",     // Partial pattern with metacharacter
            "(normal|another)", // Alternation that could match multiple
            "normal[.]key", // Character class
            "normal\\.*",   // Escaped metacharacter
            "normal{1,}",   // Quantifier
            "normal?",      // Optional quantifier
            "normal+",      // Plus quantifier
            "normal|another" // Alternation without parentheses
        };

        // Act & Assert - all malicious patterns should be safely handled
        for (String pattern : maliciousPatterns) {
            assertDoesNotThrow(() -> {
                Set<Object> result = redisService.keys(pattern);
                assertNotNull(result, "Result should not be null for pattern: " + pattern);
                // The result should be empty or contain only literal matches, not regex matches
                assertTrue(result.size() <= testFields.size(), 
                    "Result should not exceed total fields for pattern: " + pattern);
            }, "Should safely handle malicious pattern: " + pattern);
        }
    }

    @Test
    @DisplayName("Should properly handle Redis wildcard patterns")
    void testRedisWildcardPatterns() {
        // Arrange
        Set<Object> testFields = new HashSet<>();
        testFields.add("service:integration:1.0:flowrule:rule1");
        testFields.add("service:integration:2.0:flowrule:rule2");
        testFields.add("service:other:1.0:flowrule:rule3");
        testFields.add("different:integration:1.0:flowrule:rule4");
        
        when(hashService.getFields()).thenReturn(testFields);

        // Test legitimate Redis patterns
        Set<Object> result1 = redisService.keys("service:integration:*");
        assertEquals(2, result1.size(), "Should match keys with service:integration prefix");
        assertTrue(result1.contains("service:integration:1.0:flowrule:rule1"));
        assertTrue(result1.contains("service:integration:2.0:flowrule:rule2"));

        Set<Object> result2 = redisService.keys("*:integration:1.0:*");
        assertEquals(2, result2.size(), "Should match keys with integration:1.0 in middle");
        assertTrue(result2.contains("service:integration:1.0:flowrule:rule1"));
        assertTrue(result2.contains("different:integration:1.0:flowrule:rule4"));

        Set<Object> result3 = redisService.keys("service:?ther:*");
        assertEquals(1, result3.size(), "Should match single character wildcard");
        assertTrue(result3.contains("service:other:1.0:flowrule:rule3"));
    }

    @Test
    @DisplayName("Should handle catastrophic backtracking patterns safely")
    void testCatastrophicBacktrackingPrevention() {
        // Arrange
        Set<Object> testFields = new HashSet<>();
        testFields.add("test:key:1.0:flowrule:data");
        
        when(hashService.getFields()).thenReturn(testFields);

        // Patterns that could cause catastrophic backtracking (ReDoS)
        String[] redosPatterns = {
            "(a+)+b",           // Nested quantifiers
            "(a|a)*b",          // Alternation with overlap
            "a*a*a*a*a*a*b",    // Multiple quantifiers
            "(a+)+$",           // Nested quantifiers with anchor
            "^(a+)+",           // Nested quantifiers with anchor
            "a{1,10}{1,10}",    // Nested quantifiers with bounds
        };

        // Act & Assert - should complete quickly without hanging
        for (String pattern : redosPatterns) {
            long startTime = System.currentTimeMillis();
            
            assertDoesNotThrow(() -> {
                Set<Object> result = redisService.keys(pattern);
                assertNotNull(result, "Result should not be null for ReDoS pattern: " + pattern);
            }, "Should safely handle ReDoS pattern: " + pattern);
            
            long duration = System.currentTimeMillis() - startTime;
            assertTrue(duration < 1000, 
                "Pattern should complete quickly (< 1s), took " + duration + "ms: " + pattern);
        }
    }

    @Test
    @DisplayName("Should return empty set for invalid regex patterns")
    void testInvalidPatternHandling() {
        // Arrange
        Set<Object> testFields = new HashSet<>();
        testFields.add("test:key");
        
        when(hashService.getFields()).thenReturn(testFields);

        // Invalid regex patterns that should be handled gracefully
        // Note: *, +, ? are valid Redis patterns, so we only test truly invalid regex constructs
        String[] invalidPatterns = {
            "[",            // Unclosed character class
            "(",            // Unclosed group
            "\\",           // Trailing backslash
            "(?",           // Invalid group syntax
        };

        // Act & Assert
        for (String pattern : invalidPatterns) {
            Set<Object> result = redisService.keys(pattern);
            assertTrue(result.isEmpty(), 
                "Should return empty set for invalid pattern: " + pattern);
        }
    }

    @Test
    @DisplayName("Should handle null and empty patterns safely")
    void testNullAndEmptyPatterns() {
        // Arrange
        Set<Object> testFields = new HashSet<>();
        testFields.add("test:key");
        
        when(hashService.getFields()).thenReturn(testFields);

        // Act & Assert
        Set<Object> nullResult = redisService.keys(null);
        assertTrue(nullResult.isEmpty(), "Should return empty set for null pattern");

        Set<Object> emptyResult = redisService.keys("");
        assertTrue(emptyResult.isEmpty(), "Should return empty set for empty pattern");
    }

    @Test
    @DisplayName("Should handle large field sets efficiently")
    void testLargeFieldSetPerformance() {
        // Arrange - create a large set of fields
        Set<Object> largeFieldSet = new HashSet<>();
        for (int i = 0; i < 10000; i++) {
            largeFieldSet.add("service" + i + ":integration:1.0:flowrule:rule" + i);
        }
        
        when(hashService.getFields()).thenReturn(largeFieldSet);

        // Act & Assert - should complete in reasonable time
        long startTime = System.currentTimeMillis();
        
        Set<Object> result = redisService.keys("service*:integration:*");
        
        long duration = System.currentTimeMillis() - startTime;
        
        assertNotNull(result);
        assertEquals(10000, result.size(), "Should match all fields");
        assertTrue(duration < 5000, "Should complete within 5 seconds for large dataset, took " + duration + "ms");
    }
}
