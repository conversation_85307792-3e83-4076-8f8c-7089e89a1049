package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.FlowTargetsRoutingConfig;
import com.dell.it.hip.config.FlowSteps.HandlerTarget;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.WiretapService;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;

@Component("flowTargetsRouting")
public class FlowTargetsRoutingFlowStepStrategy implements FlowStepStrategy {
    private static final Logger logger = LoggerFactory.getLogger(FlowTargetsRoutingFlowStepStrategy.class);

    @Autowired private RuleProcessor ruleProcessor;
    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired private ThreadPoolTaskExecutor flowExecutor;
    @Autowired private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;
    @Autowired private Tracer tracer;

    public FlowTargetsRoutingFlowStepStrategy(
            RuleProcessor ruleProcessor,
            ServiceManager serviceManager,
            WiretapService wiretapService,
            ThreadPoolTaskExecutor flowExecutor
    ) {
        this.ruleProcessor = ruleProcessor;
        this.serviceManager = serviceManager;
        this.wiretapService = wiretapService;
        this.flowExecutor = flowExecutor;
    }

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        FlowTargetsRoutingConfig config = (FlowTargetsRoutingConfig) def.getConfig(stepConfigRef.getPropertyRef(), FlowTargetsRoutingConfig.class);
        List<RuleRef> ruleRefs = config.getRuleRefs();
        boolean dbBacked = config.isDbBacked();

        Map<String, Object> context = new HashMap<>();
        Message<?> processed = ruleProcessor.processRules(message, ruleRefs, def, stepConfigRef, context, dbBacked);

        @SuppressWarnings("unchecked")
        List<HandlerTarget> handlerTargets = (List<HandlerTarget>) context.get("handlerTargets");

        if (handlerTargets == null || handlerTargets.isEmpty()) {
            wiretapService.tap(processed, def, stepConfigRef, "error", "FlowTargetsRoutingFlowStep: No handler targets found for message, cannot route.");
            return List.of();
        }

        // === Pattern 2: Fire-and-forget parallel routing, never block ===
        for (HandlerTarget target : handlerTargets) {
            flowExecutor.execute(() -> {
                // Each handler gets a new trace context for visibility
                Span span = tracer.spanBuilder("FlowTargetsRouting.Target")
                        .setNoParent()
                        .startSpan();
                try (Scope scope = span.makeCurrent()) {
                    // Inject new trace context to headers
                    Message<?> msgWithNewTrace = openTelemetryPropagationUtil.injectNewTraceParentContext(processed);

                    RoutingResult result = routeToHandlerTarget(msgWithNewTrace, def, stepConfigRef, target);

                    if (result.isSuccess()) {
                        wiretapService.tap(msgWithNewTrace, def, stepConfigRef, "completed", "Delivered to handlerTarget (primary=" + target.getPrimaryHandler() + ")");
                    } else {
                        wiretapService.tap(msgWithNewTrace, def, stepConfigRef, "error", "HandlerTarget failed for primary=" + target.getPrimaryHandler() + ": " + result.getErrorMsg());
                    }
                } catch (Exception ex) {
                    wiretapService.tap(processed, def, stepConfigRef, "error", "Exception routing handlerTarget " + target.getPrimaryHandler() + ": " + ex.getMessage());
                    logger.error("Exception routing to handlerTarget: {}", ex.getMessage(), ex);
                } finally {
                    span.end();
                }
            });
        }
        // Fire-and-forget; nothing to pass to downstream flow steps (terminal step)
        wiretapService.tap(processed, def, stepConfigRef, "info", "FlowTargetsRoutingFlowStep: Routed in parallel to " + handlerTargets.size() + " handler targets.");
        return Collections.emptyList();
    }

    private RoutingResult routeToHandlerTarget(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, HandlerTarget target) {
        String primaryId = target.getPrimaryHandler();
        String fallbackId = target.getFallbackHandler();

        HandlerConfigRef primaryRef = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(primaryId))
                .findFirst()
                .orElse(null);
        HandlerConfigRef fallbackRef = (fallbackId != null)
                ? def.getHandlerConfigRefs().stream().filter(r -> r.getPropertyRef().equals(fallbackId)).findFirst().orElse(null)
                : null;

        HandlerStrategy primaryHandler = (primaryRef != null) ? serviceManager.getHandlerStrategy(primaryRef.getType()) : null;
        HandlerStrategy fallbackHandler = (fallbackRef != null) ? serviceManager.getHandlerStrategy(fallbackRef.getType()) : null;

        try {
            if (primaryHandler != null) {
                primaryHandler.handle(message, def, primaryRef);
                return RoutingResult.success(target);
            } else {
                wiretapService.tap(message, def, stepConfigRef, "warn", "Primary handler not found: " + primaryId);
            }
        } catch (Exception ex) {
            wiretapService.tap(message, def, primaryRef, "error", "Primary handler failed: " + primaryId + " ex=" + ex.getMessage());
            if (fallbackHandler != null) {
                try {
                    fallbackHandler.handle(message, def, fallbackRef);
                    return RoutingResult.success(target);
                } catch (Exception fbEx) {
                    wiretapService.tap(message, def, fallbackRef, "error", "Fallback handler failed: " + fallbackId + " ex=" + fbEx.getMessage());
                    return RoutingResult.failed(target, "Fallback handler failed: " + fbEx.getMessage());
                }
            }
        }
        wiretapService.tap(message, def, stepConfigRef, "error",
                "FlowTargetsRoutingFlowStep: Both primary and fallback handlers failed for handlerTarget [primary=" + primaryId + ", fallback=" + fallbackId + "]");
        return RoutingResult.failed(target, "Both handlers failed");
    }

    @Override
    public String getType() {
        return "flowTargetsRouting";
    }

    // --- Simple result wrapper for routing outcome ---
    private static class RoutingResult {
        private final HandlerTarget target;
        private final boolean success;
        private final String errorMsg;

        public static RoutingResult success(HandlerTarget t) {
            return new RoutingResult(t, true, null);
        }
        public static RoutingResult failed(HandlerTarget t, String err) {
            return new RoutingResult(t, false, err);
        }
        private RoutingResult(HandlerTarget target, boolean success, String errorMsg) {
            this.target = target;
            this.success = success;
            this.errorMsg = errorMsg;
        }
        public boolean isSuccess() { return success; }
        public String getErrorMsg() { return errorMsg; }
        public HandlerTarget getTarget() { return target; }
    }
}
