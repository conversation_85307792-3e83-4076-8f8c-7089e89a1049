package com.dell.it.hip.core.repository;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;

@Repository
public interface HIPIntegrationDefinitionRepository extends JpaRepository<HIPIntegrationRequestEntity, Long> {
    List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName);

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END FROM HIPIntegrationRequestEntity e " +
           "WHERE e.serviceManagerName = :serviceManagerName " +
           "AND e.hipIntegrationName = :hipIntegrationName " +
           "AND e.version = :versionBigDecimal")
    boolean existsByServiceManagerNameAndHipIntegrationNameAndVersion(
            @Param("serviceManagerName") String serviceManagerName,
            @Param("hipIntegrationName") String hipIntegrationName,
            @Param("versionBigDecimal") BigDecimal version);

    @Query("SELECT e FROM HIPIntegrationRequestEntity e " +
           "WHERE e.serviceManagerName = :serviceManagerName " +
           "AND e.hipIntegrationName = :hipIntegrationName " +
           "AND e.version = :versionBigDecimal")
    HIPIntegrationRequestEntity findByServiceManagerNameAndHipIntegrationNameAndVersion(
            @Param("serviceManagerName") String serviceManagerName,
            @Param("hipIntegrationName") String hipIntegrationName,
            @Param("versionBigDecimal") BigDecimal version);
}
