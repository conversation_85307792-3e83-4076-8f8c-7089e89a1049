package com.dell.it.hip.util;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.TransactionStatus;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.ibm.mq.MQMessage;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.propagation.TextMapGetter;
import io.opentelemetry.context.propagation.TextMapSetter;
import jakarta.jms.JMSException;
import jakarta.servlet.http.HttpServletRequest;
@Component
public class OpenTelemetryPropagationUtil {
    private static final Logger logger = LoggerFactory.getLogger(OpenTelemetryPropagationUtil.class);

    // --- Kafka Getter ---
    public static final TextMapGetter<ConsumerRecord<String, byte[]>> KAFKA_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(ConsumerRecord<String, byte[]> record) {
            List<String> keyList = new ArrayList<>();
            if (record != null && record.headers() != null) {
                for (Header header : record.headers()) {
                    keyList.add(header.key());
                }
            }
            return keyList;
        }
        @Override
        public String get(ConsumerRecord<String, byte[]> record, String key) {
            if (record.headers() == null) return null;
            Header header = record.headers().lastHeader(key);
            return header != null ? new String(header.value()) : null;
        }
    };
    
    public static final TextMapGetter<Headers> KAFKA_HEADER_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(Headers headers) {
            List<String> keyList = new ArrayList<>();
            headers.forEach(h -> keyList.add(h.key()));
            return keyList;
        }

        @Override
        public String get(Headers headers, String key) {
            Header header = headers.lastHeader(key);
            return header != null ? new String(header.value(), StandardCharsets.UTF_8) : null;
        }
    };

    // --- AMQP (RabbitMQ) Getter ---
    public static final TextMapGetter<MessageProperties> AMQP_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(MessageProperties properties) {
            return properties.getHeaders() != null ? properties.getHeaders().keySet() : Collections.emptySet();
        }
        @Override
        public String get(MessageProperties properties, String key) {
            Object val = properties.getHeaders().get(key);
            return val != null ? val.toString() : null;
        }
    };

    // --- IBM MQ JMS Getter ---
    public static final TextMapGetter<jakarta.jms.Message> JMS_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(jakarta.jms.Message message) {
            try {
                @SuppressWarnings("unchecked")
                Enumeration<String> names = message.getPropertyNames();
                return names != null ? Collections.list(names) : Collections.emptyList();
            } catch (JMSException e) {
                return Collections.emptyList();
            }
        }
        @Override
        public String get(jakarta.jms.Message message, String key) {
            try {
                String val = message.getStringProperty(key);
                return val;
            } catch (JMSException e) {
                return null;
            }
        }
    };

    // --- SFTP/NAS File Metadata Getter ---
    public static final TextMapGetter<Map<String, Object>> FILE_METADATA_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(Map<String, Object> metadata) {
            return metadata != null ? metadata.keySet() : Collections.emptySet();
        }
        @Override
        public String get(Map<String, Object> metadata, String key) {
            Object val = metadata != null ? metadata.get(key) : null;
            return val != null ? val.toString() : null;
        }
    };

    // --- HTTP Headers Getter (Spring) ---
    public static final TextMapGetter<HttpHeaders> HTTP_HEADERS_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(HttpHeaders headers) {
            return headers != null ? headers.keySet() : Collections.emptySet();
        }
        @Override
        public String get(HttpHeaders headers, String key) {
            if (headers == null) return null;
            return headers.getFirst(key);
        }
    };

    // --- HTTP Servlet Request Getter ---
    public static final TextMapGetter<HttpServletRequest> SERVLET_REQUEST_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(HttpServletRequest request) {
            return request != null ? Collections.list(request.getHeaderNames()) : Collections.emptySet();
        }
        @Override
        public String get(HttpServletRequest request, String key) {
            return request != null ? request.getHeader(key) : null;
        }
    };

    // --- WebFlux ServerHttpRequest Getter ---
    public static final TextMapGetter<ServerHttpRequest> WEBFLUX_REQUEST_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(ServerHttpRequest request) {
            return request.getHeaders() != null ? request.getHeaders().keySet() : Collections.emptySet();
        }
        @Override
        public String get(ServerHttpRequest request, String key) {
            if (request.getHeaders() == null) return null;
            return request.getHeaders().getFirst(key);
        }
    };

    // --- Spring Integration MessageHeaders Getter ---
    public static final TextMapGetter<MessageHeaders> SPRING_MESSAGE_HEADERS_GETTER = new TextMapGetter<>() {
        @Override
        public Iterable<String> keys(MessageHeaders headers) {
            return headers != null ? headers.keySet() : Collections.emptySet();
        }
        @Override
        public String get(MessageHeaders headers, String key) {
            Object val = headers != null ? headers.get(key) : null;
            return val != null ? val.toString() : null;
        }
    };

    // --- Spring MessageBuilder Setter ---
    public static final TextMapSetter<MessageBuilder<?>> SPRING_MSG_SETTER = (carrier, key, value) -> {
        if (carrier != null && key != null && value != null) carrier.setHeader(key, value);
    };

    // --- Map Setter ---
    public static final TextMapSetter<Map<String, Object>> MAP_SETTER = (carrier, key, value) -> {
        if (carrier != null && key != null && value != null) carrier.put(key, value);
    };

    // --- Extraction Methods ---

    public static Context extractContextFromKafka(ConsumerRecord<String, byte[]> record) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), record, KAFKA_GETTER);
    }
    
    public static Context extractContextFromKafkaHeaders(ConsumerRecord<?, ?> record) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), record.headers(), KAFKA_HEADER_GETTER);
    }


    public static Context extractContextFromAmqp(MessageProperties properties) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), properties, AMQP_GETTER);
    }

    public static Context extractContextFromJms(jakarta.jms.Message jmsMessage) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), jmsMessage, JMS_GETTER);
    }

    public static Context extractContextFromFileMetadata(Map<String, Object> metadata) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), metadata, FILE_METADATA_GETTER);
    }

    public static Context extractContextFromHttpHeaders(HttpHeaders headers) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), headers, HTTP_HEADERS_GETTER);
    }

    public static Context extractContextFromServletRequest(HttpServletRequest request) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), request, SERVLET_REQUEST_GETTER);
    }

    public static Context extractContextFromServerHttpRequest(ServerHttpRequest request) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), request, WEBFLUX_REQUEST_GETTER);
    }

    /**
     * Extract OpenTelemetry context from Spring Integration Message headers.
     * Use this in any handler/step to restore tracing context, especially after ExecutorChannel.
     */
    public static Context extractContextFromMessage(org.springframework.messaging.Message<?> message) {
        return GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .extract(Context.current(), message.getHeaders(), SPRING_MESSAGE_HEADERS_GETTER);
    }

    // --- Injection Methods ---

    /**
     * Inject OpenTelemetry context into a Spring MessageBuilder (for sending via Spring Integration channels).
     */
    public static void injectContext(Context ctx, MessageBuilder<?> builder) {
        GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .inject(ctx, builder, SPRING_MSG_SETTER);
    }

    /**
     * Inject OpenTelemetry context into a Map (for file metadata, custom headers, etc).
     */
    public static void injectContext(Context ctx, Map<String, Object> headers) {
        GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .inject(ctx, headers, MAP_SETTER);
    }

    // --- Utility: Start a new span with parent context (can be used by adapters for tracing) ---
    public static Span startAdapterSpan(String name, Context parent) {
        Tracer tracer = GlobalOpenTelemetry.getTracer("hip-adapter");
        return tracer.spanBuilder(name).setParent(parent).startSpan();
    }
    /**
     * Injects current OpenTelemetry context into message headers, if not already present.
     */
    public Message<?> injectTraceContext(Message<?> message) {
        Map<String, Object> headers = new HashMap<>(message.getHeaders());
        Span currentSpan = Span.current();
        // Only inject if not already present
        if (!headers.containsKey("traceparent")) {
            Map<String, String> traceHeaders = new HashMap<>();
            GlobalOpenTelemetry.getPropagators().getTextMapPropagator()
                    .inject(Context.current(), traceHeaders, MapSetter.INSTANCE);

            traceHeaders.forEach(headers::putIfAbsent);
        }

        // Optionally, you may want to add traceId/spanId explicitly as well
        
        if (currentSpan != null && currentSpan.getSpanContext().isValid()) {
            headers.put("traceId", currentSpan.getSpanContext().getTraceId());
            headers.put("spanId", currentSpan.getSpanContext().getSpanId());
            headers.put("rootTraceId", currentSpan.getSpanContext().getTraceId());
            headers.put("parentTraceIds", "");
        }
        return MessageBuilder.fromMessage(message)
                .copyHeaders(headers)
                .build();
    }
    
    public Message<?> injectNewTraceParentContext(Message<?> message) {
        Map<String, Object> headers = new HashMap<>(message.getHeaders());
        Span currentSpan = Span.current();
       
        // Always inject traceparent from current context (overwriting previous traceparent)
        Map<String, String> traceHeaders = new HashMap<>();
        GlobalOpenTelemetry.getPropagators().getTextMapPropagator()
                .inject(Context.current(), traceHeaders, MapSetter.INSTANCE);
        traceHeaders.forEach(headers::put); 

        // Optionally, you may want to add traceId/spanId explicitly as well
        if( headers.containsKey("traceId") && headers.get("traceId") != null ) {
        	String parentTrace = headers.get("traceId").toString();
        	headers.put("parentTraceIds", parentTrace);
        }
        if (currentSpan != null && currentSpan.getSpanContext().isValid()) {
            headers.put("traceId", currentSpan.getSpanContext().getTraceId());
            headers.put("spanId", currentSpan.getSpanContext().getSpanId());
        }
        return MessageBuilder.fromMessage(message)
                .copyHeaders(headers)
                .build();
    }
    
      /**
       *  TextMapSetter for Map<String, String> for OpenTelemetry context propagation.
       */
    enum MapSetter implements TextMapSetter<Map<String, String>> {
        INSTANCE;
        @Override
        public void set(Map<String, String> carrier, String key, String value) {
            if (carrier != null && key != null && value != null) {
                carrier.put(key, value);
            }
        }

    }
    
    public enum KafkaHeaderSetter implements TextMapSetter<Headers> {
        INSTANCE;

        @Override
        public void set(Headers headers, String key, String value) {
            if (headers != null && key != null && value != null) {
                headers.remove(key); // Avoid duplicates
                headers.add(key, value.getBytes(StandardCharsets.UTF_8));
            }
        }
    }
    
    public static final TextMapSetter<MQMessage> MQ_MESSAGE_SETTER = new TextMapSetter<>() {
        @Override
        public void set(MQMessage carrier, String key, String value) {
            try {
                carrier.setStringProperty(key, value);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    };
    
    /**
     * Propagates the current OpenTelemetry trace context into the message headers.
     * Returns a new Message with the trace context headers injected.
     */
    public Message<?> propagate(Message<?> message) {
        // Get the current Span context
        Context context = Span.current().storeInContext(Context.current());

        // Build a modifiable copy of headers
        Map<String, Object> headers = new HashMap<>(message.getHeaders());

        // Use OpenTelemetry propagator to inject context into headers
        GlobalOpenTelemetry.getPropagators()
                .getTextMapPropagator()
                .inject(context, headers, SETTER);

        // Build a new message with the updated headers
        return MessageBuilder.fromMessage(message)
                .copyHeaders(headers)
                .build();
    }

    public static void logTermination(
            String eventType,              // e.g. HANDLER, ADAPTER, FLOWSTEP
            String eventName,              // e.g. KafkaHandler, Splitter, SftpAdapter
            String eventRole,              // e.g. primary/fallback
            HIPIntegrationDefinition def,  // the integration definition context
            Object eventConfigRef,         // HandlerConfigRef, AdapterConfigRef, StepConfig, etc.
            TransactionStatus status,
            String errorMsg,
            Message<?> message
    ) {
        try {
            GenericTransactionLogEntry entry = new GenericTransactionLogEntry();
            entry.setTimestamp(Instant.now());
            entry.setServiceManagerName(def != null ? def.getServiceManagerName() : null);
            entry.setIntegrationName(def != null ? def.getHipIntegrationName() : null);
            entry.setIntegrationVersion(def != null ? def.getVersion() : null);
            entry.setEventType(eventType);
            entry.setEventName(eventName);
            entry.setEventRole(eventRole);
            entry.setEventConfigRef(eventConfigRef);
            entry.setStatus(status);
            entry.setErrorMessage(errorMsg);
            entry.setMessageId(message != null && message.getHeaders().containsKey("id")
                    ? message.getHeaders().get("id").toString()
                    : null);
            entry.setPayload(message != null ? message.getPayload() : null);

            // Log or persist as needed
            logger.info("[TRANSACTION-TERMINATION] {}", entry);

        } catch (Exception e) {
            logger.error("TransactionLoggingUtil failed to log generic termination: {}", e.getMessage(), e);
        }
    }

    public static class GenericTransactionLogEntry {
        private Instant timestamp;
        private String serviceManagerName;
        private String integrationName;
        private String integrationVersion;
        private String eventType;
        private String eventName;
        private String eventRole;
        private Object eventConfigRef;
        private TransactionStatus status;
        private String errorMessage;
        private String messageId;
        private Object payload;

        // --- Getters and Setters ---

        public Instant getTimestamp() { return timestamp; }
        public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }

        public String getServiceManagerName() { return serviceManagerName; }
        public void setServiceManagerName(String serviceManagerName) { this.serviceManagerName = serviceManagerName; }

        public String getIntegrationName() { return integrationName; }
        public void setIntegrationName(String integrationName) { this.integrationName = integrationName; }

        public String getIntegrationVersion() { return integrationVersion; }
        public void setIntegrationVersion(String integrationVersion) { this.integrationVersion = integrationVersion; }

        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }

        public String getEventName() { return eventName; }
        public void setEventName(String eventName) { this.eventName = eventName; }

        public String getEventRole() { return eventRole; }
        public void setEventRole(String eventRole) { this.eventRole = eventRole; }

        public Object getEventConfigRef() { return eventConfigRef; }
        public void setEventConfigRef(Object eventConfigRef) { this.eventConfigRef = eventConfigRef; }

        public TransactionStatus getStatus() { return status; }
        public void setStatus(TransactionStatus status) { this.status = status; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }

        public Object getPayload() { return payload; }
        public void setPayload(Object payload) { this.payload = payload; }

        @Override
        public String toString() {
            return "GenericTransactionLogEntry{" +
                    "timestamp=" + timestamp +
                    ", serviceManagerName='" + serviceManagerName + '\'' +
                    ", integrationName='" + integrationName + '\'' +
                    ", integrationVersion='" + integrationVersion + '\'' +
                    ", eventType='" + eventType + '\'' +
                    ", eventName='" + eventName + '\'' +
                    ", eventRole='" + eventRole + '\'' +
                    ", eventConfigRef=" + (eventConfigRef != null ? eventConfigRef.toString() : "null") +
                    ", status=" + status +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", messageId='" + messageId + '\'' +
                    ", payload=" + (payload != null ? payload.toString() : "null") +
                    '}';
        }
    }
    /**
     * TextMapSetter for Spring Message headers.
     */
    private static final TextMapSetter<Map<String, Object>> SETTER = new TextMapSetter<Map<String, Object>>() {
        @Override
        public void set(Map<String, Object> carrier, String key, String value) {
            carrier.put(key, value);
        }
    };

}