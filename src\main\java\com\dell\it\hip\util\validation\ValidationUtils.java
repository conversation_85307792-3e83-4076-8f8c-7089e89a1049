package com.dell.it.hip.util.validation;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleActionDescriptor;
import com.dell.it.hip.util.contivoUtils.CacheClassLoader;
import com.dell.it.hip.util.redis.ContivoMapCache;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ValidationUtils {
	
	@Autowired
	private ObjectMapper objectMapper;
	
	@Autowired
	private ContivoMapCache contivoMapCache;
	
	@Autowired
	private ApplicationContext applicationContext;
	
	public boolean crossMapFilePathValidatorAndMapLoading(List<Rule> list, String crossRefFilePath, boolean RegloadMap) throws Exception {
		
		for(Rule rule : list) {
			for(RuleActionDescriptor action : rule.getActions()) {
				ContivoMapConfig mapdata = objectMapper.convertValue(action.getParams(), ContivoMapConfig.class);
				if(RegloadMap)//Load all maps in memory if it is true.
				   cacheClassLoader(mapdata);
				if(mapdata.getCrossReferenceData()!=null && mapdata.getCrossReferenceData().getCrossRefMapParams() != null 
						&& !mapdata.getCrossReferenceData().getCrossRefMapParams().isEmpty()) {// If Map having crossMap details.
					String folder_name = findKeyContaining(mapdata.getCrossReferenceData().getCrossRefMapParams(), "path").toString();
					String file_path = crossRefFilePath+"/"+folder_name+"/"+mapdata.getCrossReferenceData().getCrossReferenceFileName();
					File file = new File(file_path);
					 if (!file.exists()) {
			                return false; // Return false if any file doesn't exist
			           }
				}
				
			}
		}
		return true; // All files exist
	}
	
	private CacheClassLoader cacheClassLoader(ContivoMapConfig contivoMap) throws Exception {
		CacheClassLoader cacheClassLoader = new CacheClassLoader(Thread.currentThread().getContextClassLoader());
		byte[] classCode = contivoMapCache.loadMapData(contivoMap.getRedisMapDataKey());
		//saveByteArrayToFile(classCode,"C:\\Users\\<USER>\\Downloads\\Transform_TRANSACTION_to_85.jar");
		cacheClassLoader.addJAR(contivoMap.getMapName() + ".jar", classCode);
		ConfigurableListableBeanFactory beanFactory = ((ConfigurableApplicationContext) applicationContext).getBeanFactory();
		if (beanFactory.containsSingleton("cacheClassLoader")) {
            ((DefaultListableBeanFactory) beanFactory).destroySingleton("cacheClassLoader");
        }
        // Register the new one
        beanFactory.registerSingleton("cacheClassLoader", cacheClassLoader); // "childClassLoader" is the bean name
		return cacheClassLoader;
	}
	
	public Object findKeyContaining(Map<String, String> map, String substring) {
	    for (String key : map.keySet()) {
	        if (key.contains(substring)) {
	            return map.get(key); // return first match
	        }
	    }
	    return null; // no match found
	}

}
