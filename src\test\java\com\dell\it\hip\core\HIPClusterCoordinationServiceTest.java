package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.function.Consumer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.redis.HIPRedisCompatibilityService;

@ExtendWith(MockitoExtension.class)
class HIPClusterCoordinationServiceTest {

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private HIPRedisCompatibilityService redisService;

    @Mock
    private RedisTemplate<String, HIPClusterEvent> hipEventRedisTemplate;

    @Mock
    private RedisMessageListenerContainer redisListenerContainer;

    @Mock
    private ValueOperations<String, String> valueOperations;
    
    @Mock
    private HIPIntegrationDefinition integrationDefinition;
    
    @Mock
    private AdapterConfigRef adapterConfigRef;
    
    @Mock
    private HandlerConfigRef handlerConfigRef;
    
    private HIPClusterCoordinationService clusterCoordinationService;

    private final String serviceManagerName = "test-service-manager";
    private final String integrationName = "test-integration";
    private final String version = "1.0.0";
    private final String adapterId = "adapter-1";
    private final String handlerId = "handler-1";

    @BeforeEach
    void setUp() {
        // Initialize the service with test values - use mocked dependencies
        clusterCoordinationService = new HIPClusterCoordinationService(
            serviceManagerName, redisTemplate, redisService, hipEventRedisTemplate, redisListenerContainer
        );

        // Setup common mock behaviors
        lenient().when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        lenient().when(integrationDefinition.getHipIntegrationName()).thenReturn(integrationName);
        lenient().when(integrationDefinition.getVersion()).thenReturn(version);
        lenient().when(adapterConfigRef.getId()).thenReturn(adapterId);
        lenient().when(adapterConfigRef.getPropertyRef()).thenReturn(adapterId); // Add missing mock for getPropertyRef()
        lenient().when(handlerConfigRef.getId()).thenReturn(handlerId);
        lenient().when(handlerConfigRef.getPropertyRef()).thenReturn(handlerId); // Add missing mock for getPropertyRef()
    }

    @Test
    @DisplayName("Should pause adapter and publish cluster event")
    void testPause_AdapterPausedSuccessfully() {
        // Act
        clusterCoordinationService.pause(integrationDefinition, adapterConfigRef);
        
        // Assert
        String expectedKey = "hip:runtime:pause:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).set(expectedKey, "PAUSED");
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("PAUSE", capturedEvent.getEventType());
        assertEquals(integrationName, capturedEvent.getIntegrationName());
        assertEquals(version, capturedEvent.getIntegrationVersion());
        assertEquals(adapterId, capturedEvent.getTargetId());
        assertTrue((Boolean) capturedEvent.getPayload().get("paused"));
    }

    @Test
    @DisplayName("Should resume adapter and publish cluster event")
    void testResume_AdapterResumedSuccessfully() {
        // Act
        clusterCoordinationService.resume(integrationDefinition, adapterConfigRef);
        
        // Assert
        String expectedKey = "hip:runtime:pause:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).delete(expectedKey);
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("RESUME", capturedEvent.getEventType());
        assertFalse((Boolean) capturedEvent.getPayload().get("paused"));
    }

    @Test
    @DisplayName("Should pause handler and publish cluster event")
    void testPauseHandler_HandlerPausedSuccessfully() {
        // Act
        clusterCoordinationService.pauseHandler(integrationDefinition, handlerConfigRef);
        
        // Assert
        String expectedKey = "hip:runtime:handler:pause:test-service-manager:test-integration:1.0.0:handler-1";
        verify(redisService).set(expectedKey, "PAUSED");
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("PAUSE", capturedEvent.getEventType());
        assertEquals("HANDLER", capturedEvent.getTargetType());
        assertTrue((Boolean) capturedEvent.getPayload().get("paused"));
    }

    @Test
    @DisplayName("Should resume handler and publish cluster event")
    void testResumeHandler_HandlerResumedSuccessfully() {
        // Act
        clusterCoordinationService.resumeHandler(integrationDefinition, handlerConfigRef);
        
        // Assert
        String expectedKey = "hip:runtime:handler:pause:test-service-manager:test-integration:1.0.0:handler-1";
        verify(redisService).delete(expectedKey);

        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("RESUME", capturedEvent.getEventType());
        assertEquals("HANDLER", capturedEvent.getTargetType());
        assertFalse((Boolean) capturedEvent.getPayload().get("paused"));
    }

    @Test
    @DisplayName("Should shutdown handler after resuming")
    void testShutdownHandler_HandlerShutdownSuccessfully() {
        // Act
        clusterCoordinationService.shutdownHandler(integrationDefinition, handlerConfigRef);
        
        // Assert - Should first resume, then shutdown
        String expectedKey = "hip:runtime:handler:pause:test-service-manager:test-integration:1.0.0:handler-1";
        verify(redisService).delete(expectedKey);

        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate, times(2)).convertAndSend(anyString(), eventCaptor.capture());
        
        // Check the shutdown event (second call)
        HIPClusterEvent shutdownEvent = eventCaptor.getAllValues().get(1);
        assertEquals("SHUTDOWN", shutdownEvent.getEventType());
        assertTrue((Boolean) shutdownEvent.getPayload().get("shutdown"));
    }

    @Test
    @DisplayName("Should shutdown adapter after removing throttle and resuming")
    void testShutdownAdapter_AdapterShutdownSuccessfully() {
        // Act
        clusterCoordinationService.shutdownAdapter(integrationDefinition, adapterConfigRef);
        
        // Assert - Should remove throttle, resume, then shutdown
        String throttleKey = "hip:runtime:throttle:test-service-manager:test-integration:1.0.0:adapter-1";
        String pauseKey = "hip:runtime:pause:test-service-manager:test-integration:1.0.0:adapter-1";
        
        verify(redisService, times(2)).delete(anyString()); // throttle and pause keys
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate, times(3)).convertAndSend(anyString(), eventCaptor.capture());
        
        // Check the shutdown event (third call)
        HIPClusterEvent shutdownEvent = eventCaptor.getAllValues().get(2);
        assertEquals("SHUTDOWN", shutdownEvent.getEventType());
        assertTrue((Boolean) shutdownEvent.getPayload().get("shutdown"));
    }

    @Test
    @DisplayName("Should set throttle settings and publish event")
    void testSetThrottle_ThrottleSetSuccessfully() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(60);
        
        // Act
        clusterCoordinationService.setThrottle(integrationDefinition, adapterConfigRef, settings);
        
        // Assert
        String expectedKey = "hip:runtime:throttle:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).set(expectedKey, settings.toString());
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("THROTTLE_UPDATE", capturedEvent.getEventType());
        assertEquals(settings, capturedEvent.getPayload().get("settings"));
    }

    @Test
    @DisplayName("Should remove throttle when settings is null")
    void testSetThrottle_NullSettingsRemovesThrottle() {
        // Act
        clusterCoordinationService.setThrottle(integrationDefinition, adapterConfigRef, null);
        
        // Assert
        String expectedKey = "hip:runtime:throttle:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).delete(expectedKey);

        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("THROTTLE_UPDATE", capturedEvent.getEventType());
        assertNull(capturedEvent.getPayload().get("settings"));
    }

    @Test
    @DisplayName("Should remove throttle and publish event")
    void testRemoveThrottle_ThrottleRemovedSuccessfully() {
        // Act
        clusterCoordinationService.removeThrottle(integrationDefinition, adapterConfigRef);
        
        // Assert
        String expectedKey = "hip:runtime:throttle:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).delete(expectedKey);

        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("THROTTLE_REMOVED", capturedEvent.getEventType());
        assertTrue(capturedEvent.getPayload().isEmpty());
    }

    @Test
    @DisplayName("Should allow input when throttle settings are null")
    void testIsInputAllowed_NullSettings_ReturnsTrue() {
        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, null);
        
        // Assert
        assertTrue(result);
        // No additional verifications needed for hash-based operations
    }

    @Test
    @DisplayName("Should allow input when max messages per period is zero or negative")
    void testIsInputAllowed_InvalidMaxMessages_ReturnsTrue() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(0);
        settings.setPeriodSeconds(60);
        
        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);
        
        // Assert
        assertTrue(result);
        // No additional verifications needed for hash-based operations
    }

    @Test
    @DisplayName("Should allow input when period seconds is zero or negative")
    void testIsInputAllowed_InvalidPeriodSeconds_ReturnsTrue() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(0);
        
        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);
        
        // Assert
        assertTrue(result);
        // No additional verifications needed for hash-based operations
    }

    @Test
    @DisplayName("Should allow input when count is within limit")
    void testIsInputAllowed_WithinLimit_ReturnsTrue() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(60);
        
        when(redisService.increment(anyString())).thenReturn(50L);
        
        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);
        
        // Assert
        assertTrue(result);
        verify(redisService).increment(anyString());
        verifyNoMoreInteractions(hipEventRedisTemplate);
    }

    @Test
    @DisplayName("Should deny input and publish throttled event when limit exceeded")
    void testIsInputAllowed_ExceedsLimit_ReturnsFalseAndPublishesEvent() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(60);
        
        when(redisService.increment(anyString())).thenReturn(150L);
        
        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);
        
        // Assert
        assertFalse(result);
        
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());
        
        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("THROTTLED", capturedEvent.getEventType());
        assertEquals(150L, capturedEvent.getPayload().get("currentCount"));
    }

    @Test
    @DisplayName("Should set expiration when count is 1")
    void testIsInputAllowed_FirstIncrement_SetsExpiration() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(60);

        when(redisService.increment(anyString())).thenReturn(1L);

        // Act
        clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);

        // Assert
        verify(redisService).expire(anyString(), eq(Duration.ofSeconds(60)));
    }

    @Test
    @DisplayName("Should return false for duplicate when key already exists")
    void testIsDuplicate_KeyExists_ReturnsTrue() {
        // Arrange
        String dedupId = "unique-id-123";
        long ttlSeconds = 3600;

        when(redisService.hasKey(anyString())).thenReturn(true);

        // Act
        boolean result = clusterCoordinationService.isDuplicate(integrationDefinition, adapterConfigRef, dedupId, ttlSeconds);

        // Assert
        assertTrue(result);
        verify(redisService).hasKey(anyString());
        // No additional verifications needed for hash-based operations
    }

    @Test
    @DisplayName("Should return false for new message and set key")
    void testIsDuplicate_KeyDoesNotExist_ReturnsFalseAndSetsKey() {
        // Arrange
        String dedupId = "unique-id-123";
        long ttlSeconds = 3600;

        when(redisService.hasKey(anyString())).thenReturn(false);

        // Act
        boolean result = clusterCoordinationService.isDuplicate(integrationDefinition, adapterConfigRef, dedupId, ttlSeconds);

        // Assert
        assertFalse(result);
        verify(redisService).hasKey(anyString());
        verify(redisService).set(anyString(), eq("1"), eq(Duration.ofSeconds(ttlSeconds)));
    }

    @Test
    @DisplayName("Should handle null hasKey result as false")
    void testIsDuplicate_NullHasKeyResult_ReturnsFalse() {
        // Arrange
        String dedupId = "unique-id-123";
        long ttlSeconds = 3600;

        when(redisService.hasKey(anyString())).thenReturn(null);

        // Act
        boolean result = clusterCoordinationService.isDuplicate(integrationDefinition, adapterConfigRef, dedupId, ttlSeconds);

        // Assert
        assertFalse(result);
        verify(redisService).set(anyString(), eq("1"), eq(Duration.ofSeconds(ttlSeconds)));
    }

    @Test
    @DisplayName("Should broadcast registration event")
    void testBroadcastRegistration_PublishesRegistrationEvent() {
        // Act
        clusterCoordinationService.broadcastRegistration(integrationDefinition);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("REGISTERED", capturedEvent.getEventType());
        assertEquals("ADAPTER", capturedEvent.getTargetType());
        assertEquals(integrationName, capturedEvent.getIntegrationName());
        assertEquals(version, capturedEvent.getIntegrationVersion());
        assertEquals("*", capturedEvent.getTargetId());
        assertTrue(capturedEvent.getPayload().isEmpty());
    }

    @Test
    @DisplayName("Should broadcast unregistration event")
    void testBroadcastUnregistration_PublishesUnregistrationEvent() {
        // Act
        clusterCoordinationService.broadcastUnregistration(integrationDefinition);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("UNREGISTERED", capturedEvent.getEventType());
        assertEquals("ADAPTER", capturedEvent.getTargetType());
        assertEquals("*", capturedEvent.getTargetId());
    }

    @Test
    @DisplayName("Should broadcast refill event")
    void testBroadcastRefill_PublishesRefillEvent() {
        // Act
        clusterCoordinationService.broadcastRefill(integrationDefinition, adapterConfigRef);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("REFILL", capturedEvent.getEventType());
        assertEquals(adapterId, capturedEvent.getTargetId());
        assertTrue(capturedEvent.getPayload().isEmpty());
    }

    @Test
    @DisplayName("Should broadcast custom event with payload")
    void testBroadcastCustomEvent_WithPayload_PublishesEvent() {
        // Arrange
        String eventType = "CUSTOM_EVENT";
        Map<String, Object> payload = Map.of("key1", "value1", "key2", 42);

        // Act
        clusterCoordinationService.broadcastCustomEvent(eventType, integrationDefinition, adapterConfigRef, payload);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals(eventType, capturedEvent.getEventType());
        assertEquals(adapterId, capturedEvent.getTargetId());
        assertEquals(payload, capturedEvent.getPayload());
    }

    @Test
    @DisplayName("Should broadcast custom event with null adapter ref")
    void testBroadcastCustomEvent_NullAdapterRef_UsesWildcardTargetId() {
        // Arrange
        String eventType = "CUSTOM_EVENT";
        Map<String, Object> payload = Map.of("key", "value");

        // Act
        clusterCoordinationService.broadcastCustomEvent(eventType, integrationDefinition, null, payload);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertEquals("*", capturedEvent.getTargetId());
    }

    @Test
    @DisplayName("Should broadcast custom event with null payload")
    void testBroadcastCustomEvent_NullPayload_UsesEmptyMap() {
        // Arrange
        String eventType = "CUSTOM_EVENT";

        // Act
        clusterCoordinationService.broadcastCustomEvent(eventType, integrationDefinition, adapterConfigRef, null);

        // Assert
        ArgumentCaptor<HIPClusterEvent> eventCaptor = ArgumentCaptor.forClass(HIPClusterEvent.class);
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eventCaptor.capture());

        HIPClusterEvent capturedEvent = eventCaptor.getValue();
        assertTrue(capturedEvent.getPayload().isEmpty());
    }

    @Test
    @DisplayName("Should publish cluster event")
    void testPublishClusterEvent_PublishesEvent() {
        // Arrange
        HIPClusterEvent event = HIPClusterEvent.forAdapterTarget("TEST", "integration", "1.0", "adapter", Collections.emptyMap());

        // Act
        clusterCoordinationService.publishClusterEvent(event);

        // Assert
        verify(hipEventRedisTemplate).convertAndSend(anyString(), eq(event));
    }

    @Test
    @DisplayName("Should register cluster event listener")
    void testRegisterClusterEventListener_RegistersListener() {
        // Arrange
        Consumer<HIPClusterEvent> callback = mock(Consumer.class);

        // Act
        clusterCoordinationService.registerClusterEventListener(callback);

        // Assert
        ArgumentCaptor<MessageListener> listenerCaptor = ArgumentCaptor.forClass(MessageListener.class);
        ArgumentCaptor<ChannelTopic> topicCaptor = ArgumentCaptor.forClass(ChannelTopic.class);
        verify(redisListenerContainer).addMessageListener(listenerCaptor.capture(), topicCaptor.capture());

        assertNotNull(listenerCaptor.getValue());
        assertEquals("hip:runtime:cluster:control:test-service-manager", topicCaptor.getValue().getTopic());
    }

    @Test
    @DisplayName("Should return true when adapter is paused")
    void testIsAdapterPaused_KeyExists_ReturnsTrue() {
        // Arrange
        when(redisService.hasKey(anyString())).thenReturn(true);

        // Act
        boolean result = clusterCoordinationService.isAdapterPaused(integrationDefinition, adapterConfigRef);

        // Assert
        assertTrue(result);
        String expectedKey = "hip:runtime:pause:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).hasKey(expectedKey);
    }

    @Test
    @DisplayName("Should return false when adapter is not paused")
    void testIsAdapterPaused_KeyDoesNotExist_ReturnsFalse() {
        // Arrange
        lenient().when(redisService.hasKey(anyString())).thenReturn(false);

        // Act
        boolean result = clusterCoordinationService.isAdapterPaused(integrationDefinition, adapterConfigRef);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should handle null hasKey result for adapter pause check")
    void testIsAdapterPaused_NullHasKeyResult_ReturnsFalse() {
        // Arrange
        when(redisService.hasKey(anyString())).thenReturn(null);

        // Act
        boolean result = clusterCoordinationService.isAdapterPaused(integrationDefinition, adapterConfigRef);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return true when handler is paused")
    void testIsHandlerPaused_KeyExists_ReturnsTrue() {
        // Arrange
        when(redisService.hasKey(anyString())).thenReturn(true);

        // Act
        Boolean result = clusterCoordinationService.isHandlerPaused(integrationDefinition, handlerConfigRef);

        // Assert
        assertTrue(result);
        String expectedKey = "hip:runtime:handler:pause:test-service-manager:test-integration:1.0.0:handler-1";
        verify(redisService).hasKey(expectedKey);
    }

    @Test
    @DisplayName("Should return false when handler is not paused")
    void testIsHandlerPaused_KeyDoesNotExist_ReturnsFalse() {
        // Arrange
        lenient().when(redisService.hasKey(anyString())).thenReturn(false);

        // Act
        Boolean result = clusterCoordinationService.isHandlerPaused(integrationDefinition, handlerConfigRef);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return true when adapter is throttled")
    void testIsAdapterThrottled_KeyExists_ReturnsTrue() {
        // Arrange
        when(redisService.hasKey(anyString())).thenReturn(true);

        // Act
        boolean result = clusterCoordinationService.isAdapterThrottled(integrationDefinition, adapterConfigRef);

        // Assert
        assertTrue(result);
        String expectedKey = "hip:runtime:throttle:test-service-manager:test-integration:1.0.0:adapter-1";
        verify(redisService).hasKey(expectedKey);
    }

    @Test
    @DisplayName("Should return false when adapter is not throttled")
    void testIsAdapterThrottled_KeyDoesNotExist_ReturnsFalse() {
        // Arrange
        lenient().when(redisService.hasKey(anyString())).thenReturn(false);

        // Act
        boolean result = clusterCoordinationService.isAdapterThrottled(integrationDefinition, adapterConfigRef);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should handle null hasKey result for adapter throttle check")
    void testIsAdapterThrottled_NullHasKeyResult_ReturnsFalse() {
        // Arrange
        when(redisService.hasKey(anyString())).thenReturn(null);

        // Act
        boolean result = clusterCoordinationService.isAdapterThrottled(integrationDefinition, adapterConfigRef);

        // Assert
        assertFalse(result);
    }

    @Test
    @DisplayName("Should generate integration adapter key")
    void testIntegrationAdapterKey_GeneratesCorrectKey() {
        // Act
        String result = clusterCoordinationService.integrationAdapterKey(integrationDefinition, adapterConfigRef);

        // Assert
        assertEquals("test-integration:1.0.0:adapter-1", result);
    }

    @Test
    @DisplayName("Should handle null count in isInputAllowed")
    void testIsInputAllowed_NullCount_ReturnsFalse() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(100);
        settings.setPeriodSeconds(60);

        when(redisService.increment(anyString())).thenReturn(null);

        // Act
        boolean result = clusterCoordinationService.isInputAllowed(integrationDefinition, adapterConfigRef, settings);

        // Assert
        assertFalse(result);
        // Note: The service publishes a throttled event when count is null, so we expect one interaction
        verify(hipEventRedisTemplate).convertAndSend(anyString(), any(HIPClusterEvent.class));
    }
}
