package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.exception.IntegrationRegistrationException;
import com.dell.it.hip.strategy.adapters.DynamicSFTPInputAdapter;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.StrictOrderProcessorFlowStepStrategy;
import com.dell.it.hip.strategy.flows.rules.RuleCache;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.logging.WiretapService;

import io.opentelemetry.api.trace.Tracer;

/**
 * Comprehensive unit tests for HIPIntegrationOrchestrationService class.
 * Tests all methods and edge cases to improve code coverage.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationOrchestrationServiceTest {

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @Mock
    private HIPClusterCoordinationService clusterCoordinationService;

    @Mock
    private HIPIntegrationMapper hipIntegrationMapper;

    @Mock
    private RuleCache ruleCache;

    @Mock
    private Tracer tracer;

    @Mock
    private WiretapService wiretapService;

    @Mock
    private MessageChannel inputChannel;

    @Mock
    private MessageChannel deadLetterChannel;

    @Mock
    private HIPIntegrationDefinition integrationDefinition;

    @Mock
    private HIPIntegrationRequest integrationRequest;

    @Mock
    private HIPIntegrationRequestEntity integrationRequestEntity;

    @Mock
    private AdapterConfigRef adapterConfigRef;

    @Mock
    private HandlerConfigRef handlerConfigRef;

    @Mock
    private FlowStepConfigRef flowStepConfigRef;

    @Mock
    private InputAdapterStrategy inputAdapterStrategy;

    @Mock
    private DynamicSFTPInputAdapter dynamicSFTPInputAdapter;

    @Mock
    private StrictOrderProcessorFlowStepStrategy strictOrderProcessor;

    @Mock
    private StrictOrderConfig strictOrderConfig;

    @InjectMocks
    private HIPIntegrationOrchestrationService orchestrationService;

    private final String TEST_INTEGRATION_NAME = "test-integration";
    private final String TEST_VERSION = "1.0";
    private final String TEST_SERVICE_MANAGER_NAME = "test-service-manager";
    private final String TEST_ADAPTER_ID = "test-adapter";
    private final String TEST_HANDLER_ID = "test-handler";

    @BeforeEach
    void setUp() throws Exception {
        // Set private field serviceManagerName using reflection
        setPrivateField(orchestrationService, "serviceManagerName", TEST_SERVICE_MANAGER_NAME);

        // Setup mock integration request
        lenient().when(integrationRequest.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        lenient().when(integrationRequest.getVersion()).thenReturn(TEST_VERSION);
        lenient().when(integrationRequest.getServiceManagerName()).thenReturn(TEST_SERVICE_MANAGER_NAME);

        // Setup mock integration definition
        lenient().when(integrationDefinition.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        lenient().when(integrationDefinition.getVersion()).thenReturn(TEST_VERSION);
        lenient().when(integrationDefinition.getServiceManagerName()).thenReturn(TEST_SERVICE_MANAGER_NAME);
        lenient().when(integrationDefinition.getAdapterConfigRefs()).thenReturn(Arrays.asList(adapterConfigRef));
        lenient().when(integrationDefinition.getHandlerConfigRefs()).thenReturn(Arrays.asList(handlerConfigRef));
        lenient().when(integrationDefinition.getFlowStepConfigRefs()).thenReturn(Arrays.asList(flowStepConfigRef));

        // Setup adapter and handler config refs
        lenient().when(adapterConfigRef.getId()).thenReturn(TEST_ADAPTER_ID);
        lenient().when(adapterConfigRef.getPropertyRef()).thenReturn(TEST_ADAPTER_ID); // Use TEST_ADAPTER_ID for consistency
        lenient().when(handlerConfigRef.getId()).thenReturn(TEST_HANDLER_ID);
        lenient().when(handlerConfigRef.getPropertyRef()).thenReturn(TEST_HANDLER_ID); // Use TEST_HANDLER_ID for consistency

        // Setup mapper
        lenient().when(hipIntegrationMapper.toEntity(integrationRequest)).thenReturn(integrationRequestEntity);

        // Setup service manager
        lenient().when(serviceManager.getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(inputChannel);
        lenient().when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(integrationDefinition);
        lenient().when(serviceManager.getAllDefinitions()).thenReturn(Arrays.asList(integrationDefinition));

        // Setup runtime service
        lenient().when(hipIntegrationRuntimeService.getHIPIntegrationStatus(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION))
                .thenReturn(IntegrationStatus.RUNNING);
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    @Test
    @DisplayName("Test deadLetterChannel_ReturnsNullChannel")
    void testDeadLetterChannel_ReturnsNullChannel() {
        // Act
        MessageChannel result = orchestrationService.deadLetterChannel();

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof org.springframework.integration.channel.NullChannel);
    }

    @Test
    @DisplayName("Test registerHIPIntegration_ValidRequest_RegistersSuccessfully")
    void testRegisterHIPIntegration_ValidRequest_RegistersSuccessfully() throws Exception {
        // Arrange
        doNothing().when(hipIntegrationRegistry).save(integrationRequestEntity);
        when(hipIntegrationMapper.mapToDefinition(integrationRequest)).thenReturn(integrationDefinition);
        when(integrationDefinition.getFlowStepConfigRefs()).thenReturn(Collections.emptyList());
        when(integrationDefinition.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        when(integrationDefinition.getVersion()).thenReturn(TEST_VERSION);

        // Mock the flow executor
        TaskExecutor mockExecutor = mock(TaskExecutor.class);
        when(serviceManager.getFlowExecutor()).thenReturn(mockExecutor);

        // Act
        orchestrationService.registerHIPIntegration(integrationRequest);

        // Assert
        verify(integrationRequest).setServiceManagerName(TEST_SERVICE_MANAGER_NAME);
        verify(hipIntegrationRegistry).save(integrationRequestEntity);
        verify(hipIntegrationMapper).toEntity(integrationRequest);
    }

    @Test
    @DisplayName("Test registerHIPIntegration_RegistryException_ThrowsIntegrationRegistrationException")
    void testRegisterHIPIntegration_RegistryException_ThrowsIntegrationRegistrationException() throws Exception {
        // Arrange
        doNothing().when(hipIntegrationRegistry).save(integrationRequestEntity);
        when(hipIntegrationMapper.mapToDefinition(integrationRequest)).thenThrow(new RuntimeException("Mapping error"));

        // Act & Assert
        assertThrows(IntegrationRegistrationException.class, () -> {
            orchestrationService.registerHIPIntegration(integrationRequest);
        });
    }

    @Test
    @DisplayName("Test processHIPIntegrationMessage_ValidMessage_SendsToInputChannel")
    void testProcessHIPIntegrationMessage_ValidMessage_SendsToInputChannel() {
        // Arrange
        Message<?> message = new GenericMessage<>("test message");
        when(inputChannel.send(message)).thenReturn(true);

        // Act
        orchestrationService.processHIPIntegrationMessage(TEST_INTEGRATION_NAME, TEST_VERSION, message);

        // Assert
        verify(serviceManager).getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(inputChannel).send(message);
    }

    @Test
    @DisplayName("Test processHIPIntegrationMessage_NoInputChannel_LogsWarning")
    void testProcessHIPIntegrationMessage_NoInputChannel_LogsWarning() {
        // Arrange
        Message<?> message = new GenericMessage<>("test message");
        when(serviceManager.getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.processHIPIntegrationMessage(TEST_INTEGRATION_NAME, TEST_VERSION, message);

        // Assert
        verify(serviceManager).getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(inputChannel);
    }

    @Test
    @DisplayName("Test getInputChannel_DelegatesToServiceManager")
    void testGetInputChannel_DelegatesToServiceManager() {
        // Act
        MessageChannel result = orchestrationService.getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(inputChannel, result);
        verify(serviceManager).getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);
    }

    @Test
    @DisplayName("Test unregisterHIPIntegration_ExistingIntegration_UnregistersSuccessfully")
    void testUnregisterHIPIntegration_ExistingIntegration_UnregistersSuccessfully() throws Exception {
        // Act
        orchestrationService.unregisterHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(serviceManager).unregisterIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(hipIntegrationRegistry).deleteByServiceManagerNameAndHipIntegrationNameAndVersion(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, IntegrationStatus.UNREGISTERED);
    }

    @Test
    @DisplayName("Test unregisterHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException")
    void testUnregisterHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act & Assert
        assertThrows(IntegrationNotFoundException.class, () -> {
            orchestrationService.unregisterHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test unregisterHIPIntegration_RepositoryException_ThrowsIntegrationOperationException")
    void testUnregisterHIPIntegration_RepositoryException_ThrowsIntegrationOperationException() {
        // Arrange
        doThrow(new RuntimeException("Database error")).when(hipIntegrationRegistry)
                .deleteByServiceManagerNameAndHipIntegrationNameAndVersion(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Act & Assert
        assertThrows(IntegrationOperationException.class, () -> {
            orchestrationService.unregisterHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test pauseHIPIntegration_ExistingIntegration_PausesSuccessfully")
    void testPauseHIPIntegration_ExistingIntegration_PausesSuccessfully() throws Exception {
        // Act
        orchestrationService.pauseHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).pause(integrationDefinition, adapterConfigRef);
        verify(clusterCoordinationService).pauseHandler(integrationDefinition, handlerConfigRef);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(
                TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, IntegrationStatus.PAUSED);
    }

    @Test
    @DisplayName("Test pauseHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException")
    void testPauseHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act & Assert
        assertThrows(IntegrationNotFoundException.class, () -> {
            orchestrationService.pauseHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test pauseHIPIntegration_ClusterException_ThrowsIntegrationOperationException")
    void testPauseHIPIntegration_ClusterException_ThrowsIntegrationOperationException() {
        // Arrange
        doThrow(new RuntimeException("Cluster error")).when(clusterCoordinationService).pause(integrationDefinition, adapterConfigRef);

        // Act & Assert
        assertThrows(IntegrationOperationException.class, () -> {
            orchestrationService.pauseHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test resumeHIPIntegration_ExistingIntegration_ResumesSuccessfully")
    void testResumeHIPIntegration_ExistingIntegration_ResumesSuccessfully() throws Exception {
        // Act
        orchestrationService.resumeHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).resume(integrationDefinition, adapterConfigRef);
        verify(clusterCoordinationService).resumeHandler(integrationDefinition, handlerConfigRef);
        verify(hipIntegrationRuntimeService).updateHIPIntegrationStatus(
                TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, IntegrationStatus.RUNNING);
    }

    @Test
    @DisplayName("Test resumeHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException")
    void testResumeHIPIntegration_NonExistentIntegration_ThrowsIntegrationNotFoundException() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act & Assert
        assertThrows(IntegrationNotFoundException.class, () -> {
            orchestrationService.resumeHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test resumeHIPIntegration_ClusterException_ThrowsIntegrationOperationException")
    void testResumeHIPIntegration_ClusterException_ThrowsIntegrationOperationException() {
        // Arrange
        doThrow(new RuntimeException("Cluster error")).when(clusterCoordinationService).resume(integrationDefinition, adapterConfigRef);

        // Act & Assert
        assertThrows(IntegrationOperationException.class, () -> {
            orchestrationService.resumeHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);
        });
    }

    @Test
    @DisplayName("Test applyThrottle_ValidSettings_AppliesThrottleSuccessfully")
    void testApplyThrottle_ValidSettings_AppliesThrottleSuccessfully() {
        // Arrange
        ThrottleSettings settings = new ThrottleSettings();
        settings.setMaxMessagesPerPeriod(10);

        // Act
        orchestrationService.applyThrottle(TEST_INTEGRATION_NAME, TEST_VERSION, settings);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).setThrottle(integrationDefinition, adapterConfigRef, settings);
        verify(hipIntegrationRuntimeService).updateThrottle(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID, settings);
    }

    @Test
    @DisplayName("Test removeThrottle_ExistingIntegration_RemovesThrottleSuccessfully")
    void testRemoveThrottle_ExistingIntegration_RemovesThrottleSuccessfully() {
        // Act
        orchestrationService.removeThrottle(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).removeThrottle(integrationDefinition, adapterConfigRef);
        verify(hipIntegrationRuntimeService).updateThrottle(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID, null);
    }

    @Test
    @DisplayName("Test strictOrderManualRelease_ValidConfig_ReleasesMessages")
    void testStrictOrderManualRelease_ValidConfig_ReleasesMessages() {
        // Arrange
        String stepPropertyRef = "strict-order-step";
        List<String> orderingKeyValues = Arrays.asList("key1", "key2");
        Long uptoSeq = 100L;
        List<Message<?>> releasedMessages = Arrays.asList(new GenericMessage<>("msg1"), new GenericMessage<>("msg2"));

        when(integrationDefinition.getConfig(stepPropertyRef, StrictOrderConfig.class)).thenReturn(strictOrderConfig);
        when(strictOrderConfig.getDefaultConfig()).thenReturn(mock(StrictOrderConfig.DefaultStrictOrderConfig.class));
        when(strictOrderProcessor.manualRelease(eq(integrationDefinition), any(), eq(orderingKeyValues), eq(uptoSeq))).thenReturn(releasedMessages);

        // Act
        int result = orchestrationService.strictOrderManualRelease(TEST_INTEGRATION_NAME, TEST_VERSION, stepPropertyRef, orderingKeyValues, uptoSeq);

        // Assert
        assertEquals(2, result);
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(integrationDefinition).getConfig(stepPropertyRef, StrictOrderConfig.class);
        verify(strictOrderProcessor).manualRelease(eq(integrationDefinition), any(), eq(orderingKeyValues), eq(uptoSeq));
    }

    @Test
    @DisplayName("Test strictOrderManualRelease_IntegrationNotFound_ThrowsIllegalArgumentException")
    void testStrictOrderManualRelease_IntegrationNotFound_ThrowsIllegalArgumentException() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            orchestrationService.strictOrderManualRelease(TEST_INTEGRATION_NAME, TEST_VERSION, "step", Arrays.asList("key"), 100L);
        });
    }

    @Test
    @DisplayName("Test strictOrderManualRelease_ConfigNotFound_ThrowsIllegalArgumentException")
    void testStrictOrderManualRelease_ConfigNotFound_ThrowsIllegalArgumentException() {
        // Arrange
        String stepPropertyRef = "non-existent-step";
        when(integrationDefinition.getConfig(stepPropertyRef, StrictOrderConfig.class)).thenReturn(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            orchestrationService.strictOrderManualRelease(TEST_INTEGRATION_NAME, TEST_VERSION, stepPropertyRef, Arrays.asList("key"), 100L);
        });
    }

    @Test
    @DisplayName("Test getAllHIPIntegrationsWithStatus_ReturnsIntegrationsWithStatus")
    void testGetAllHIPIntegrationsWithStatus_ReturnsIntegrationsWithStatus() {
        // Act
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> result = orchestrationService.getAllHIPIntegrationsWithStatus();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        HIPIntegrationOrchestrationService.HIPIntegrationInfo info = result.get(0);
        assertEquals(TEST_INTEGRATION_NAME, info.getHipIntegrationName());
        assertEquals(TEST_VERSION, info.getVersion());
        assertEquals(IntegrationStatus.RUNNING, info.getStatus());

        verify(serviceManager).getAllDefinitions();
        verify(hipIntegrationRuntimeService).getHIPIntegrationStatus(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION);
    }

    @Test
    @DisplayName("Test getAllHIPIntegrationsWithStatus_EmptyDefinitions_ReturnsEmptyList")
    void testGetAllHIPIntegrationsWithStatus_EmptyDefinitions_ReturnsEmptyList() {
        // Arrange
        when(serviceManager.getAllDefinitions()).thenReturn(Collections.emptyList());

        // Act
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> result = orchestrationService.getAllHIPIntegrationsWithStatus();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(serviceManager).getAllDefinitions();
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    @DisplayName("Test HIPIntegrationInfo_Constructor_InitializesCorrectly")
    void testHIPIntegrationInfo_Constructor_InitializesCorrectly() {
        // Act
        HIPIntegrationOrchestrationService.HIPIntegrationInfo info =
            new HIPIntegrationOrchestrationService.HIPIntegrationInfo(TEST_INTEGRATION_NAME, TEST_VERSION, IntegrationStatus.RUNNING);

        // Assert
        assertEquals(TEST_INTEGRATION_NAME, info.getHipIntegrationName());
        assertEquals(TEST_VERSION, info.getVersion());
        assertEquals(IntegrationStatus.RUNNING, info.getStatus());
    }

    @Test
    @DisplayName("Test triggerSftpForcePoll_ValidAdapter_TriggersPoll")
    void testTriggerSftpForcePoll_ValidAdapter_TriggersPoll() {
        // Arrange
        Map<String, InputAdapterStrategy> strategyMap = new HashMap<>();
        strategyMap.put("sftp", dynamicSFTPInputAdapter);
        when(serviceManager.getInputAdapterStrategyMap()).thenReturn(strategyMap);
        when(adapterConfigRef.getType()).thenReturn("sftp");

        // Act
        orchestrationService.triggerSftpForcePoll(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(dynamicSFTPInputAdapter).triggerForcePoll(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);
        verify(hipIntegrationRuntimeService).recordSftpCallback(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);
    }

    @Test
    @DisplayName("Test triggerSftpForcePoll_AdapterNotFound_ThrowsIllegalArgumentException")
    void testTriggerSftpForcePoll_AdapterNotFound_ThrowsIllegalArgumentException() {
        // Arrange
        when(integrationDefinition.getAdapterConfigRefs()).thenReturn(Collections.emptyList());

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            orchestrationService.triggerSftpForcePoll(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-adapter");
        });
    }

    @Test
    @DisplayName("Test shutdownHandler_ValidHandler_ShutsDownSuccessfully")
    void testShutdownHandler_ValidHandler_ShutsDownSuccessfully() {
        // Act
        orchestrationService.shutdownHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).shutdownHandler(integrationDefinition, handlerConfigRef);
    }

    @Test
    @DisplayName("Test shutdownHandler_IntegrationNotFound_LogsWarning")
    void testShutdownHandler_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.shutdownHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test shutdownHandler_HandlerNotFound_LogsWarning")
    void testShutdownHandler_HandlerNotFound_LogsWarning() {
        // Arrange
        when(integrationDefinition.getHandlerConfigRefs()).thenReturn(Collections.emptyList());

        // Act
        orchestrationService.shutdownHandler(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-handler");

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test shutdownHIPIntegration_ValidIntegration_ShutsDownSuccessfully")
    void testShutdownHIPIntegration_ValidIntegration_ShutsDownSuccessfully() {
        // Act
        orchestrationService.shutdownHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).shutdownAdapter(integrationDefinition, adapterConfigRef);
        verify(clusterCoordinationService).shutdownHandler(integrationDefinition, handlerConfigRef);
    }

    @Test
    @DisplayName("Test shutdownHIPIntegration_IntegrationNotFound_LogsWarning")
    void testShutdownHIPIntegration_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.shutdownHIPIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test refreshIntegrationRules_DelegatesToRuleCache")
    void testRefreshIntegrationRules_DelegatesToRuleCache() {
        // Act
        orchestrationService.refreshIntegrationRules(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        verify(ruleCache).refreshIntegrationRules(TEST_SERVICE_MANAGER_NAME, TEST_INTEGRATION_NAME, TEST_VERSION);
    }

    @Test
    @DisplayName("Test getRecentClusterEvents_ReturnsEventList")
    void testGetRecentClusterEvents_ReturnsEventList() {
        // Act
        List<HIPClusterEvent> result = orchestrationService.getRecentClusterEvents();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Initially empty
    }

    @Test
    @DisplayName("Test pauseAdapter_ValidAdapter_PausesSuccessfully")
    void testPauseAdapter_ValidAdapter_PausesSuccessfully() {
        // Act
        orchestrationService.pauseAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).pause(integrationDefinition, adapterConfigRef);
    }

    @Test
    @DisplayName("Test pauseAdapter_IntegrationNotFound_LogsWarning")
    void testPauseAdapter_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.pauseAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test pauseAdapter_AdapterNotFound_LogsWarning")
    void testPauseAdapter_AdapterNotFound_LogsWarning() {
        // Arrange
        when(integrationDefinition.getAdapterConfigRefs()).thenReturn(Collections.emptyList());

        // Act
        orchestrationService.pauseAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-adapter");

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test resumeAdapter_ValidAdapter_ResumesSuccessfully")
    void testResumeAdapter_ValidAdapter_ResumesSuccessfully() {
        // Act
        orchestrationService.resumeAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).resume(integrationDefinition, adapterConfigRef);
    }

    @Test
    @DisplayName("Test resumeAdapter_IntegrationNotFound_LogsWarning")
    void testResumeAdapter_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.resumeAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_ADAPTER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test resumeAdapter_AdapterNotFound_LogsWarning")
    void testResumeAdapter_AdapterNotFound_LogsWarning() {
        // Arrange
        when(integrationDefinition.getAdapterConfigRefs()).thenReturn(Collections.emptyList());

        // Act
        orchestrationService.resumeAdapter(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-adapter");

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test pauseHandler_ValidHandler_PausesSuccessfully")
    void testPauseHandler_ValidHandler_PausesSuccessfully() {
        // Act
        orchestrationService.pauseHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).pauseHandler(integrationDefinition, handlerConfigRef);
    }

    @Test
    @DisplayName("Test pauseHandler_IntegrationNotFound_LogsWarning")
    void testPauseHandler_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.pauseHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test pauseHandler_HandlerNotFound_LogsWarning")
    void testPauseHandler_HandlerNotFound_LogsWarning() {
        // Arrange
        when(integrationDefinition.getHandlerConfigRefs()).thenReturn(Collections.emptyList());

        // Act
        orchestrationService.pauseHandler(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-handler");

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test resumeHandler_ValidHandler_ResumesSuccessfully")
    void testResumeHandler_ValidHandler_ResumesSuccessfully() {
        // Act
        orchestrationService.resumeHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verify(clusterCoordinationService).resumeHandler(integrationDefinition, handlerConfigRef);
    }

    @Test
    @DisplayName("Test resumeHandler_IntegrationNotFound_LogsWarning")
    void testResumeHandler_IntegrationNotFound_LogsWarning() {
        // Arrange
        when(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION)).thenReturn(null);

        // Act
        orchestrationService.resumeHandler(TEST_INTEGRATION_NAME, TEST_VERSION, TEST_HANDLER_ID);

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test resumeHandler_HandlerNotFound_LogsWarning")
    void testResumeHandler_HandlerNotFound_LogsWarning() {
        // Arrange
        when(integrationDefinition.getHandlerConfigRefs()).thenReturn(Collections.emptyList());

        // Act
        orchestrationService.resumeHandler(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-handler");

        // Assert
        verify(serviceManager).getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        verifyNoInteractions(clusterCoordinationService);
    }

    @Test
    @DisplayName("Test mapReqToDefinition_ValidRequest_MapsCorrectly")
    void testMapReqToDefinition_ValidRequest_MapsCorrectly() {
        // Arrange
        when(integrationRequest.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        when(integrationRequest.getServiceManagerName()).thenReturn(TEST_SERVICE_MANAGER_NAME);
        when(integrationRequest.getVersion()).thenReturn(TEST_VERSION);

        // Act
        HIPIntegrationDefinition result = orchestrationService.mapReqToDefinition(integrationRequest);

        // Assert
        assertNotNull(result);
        assertEquals(TEST_INTEGRATION_NAME, result.getHipIntegrationName());
        assertEquals(TEST_SERVICE_MANAGER_NAME, result.getServiceManagerName());
        assertEquals(TEST_VERSION, result.getVersion());
    }

    @Test
    @DisplayName("Test mapInputToDefinition_ValidInput_MapsCorrectly")
    void testMapInputToDefinition_ValidInput_MapsCorrectly() {
        // Act
        HIPIntegrationDefinition result = orchestrationService.mapInputToDefinition(
                TEST_INTEGRATION_NAME, TEST_VERSION, TEST_SERVICE_MANAGER_NAME);

        // Assert
        assertNotNull(result);
        assertEquals(TEST_INTEGRATION_NAME, result.getHipIntegrationName());
        assertEquals(TEST_SERVICE_MANAGER_NAME, result.getServiceManagerName());
        assertEquals(TEST_VERSION, result.getVersion());
    }
}
