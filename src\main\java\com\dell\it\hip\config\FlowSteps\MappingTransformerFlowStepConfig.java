package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = MappingTransformerFlowStepConfigDeserializer.class)
public class MappingTransformerFlowStepConfig extends FlowStepConfig implements RuleEnabledStepConfig{

	@JsonProperty("configurationList")
	private List<DocTypeMappingTransformerConfig> docTypeConfigs = new ArrayList<>();
	private DefaultMappingTransformerConfig defaultConfig;

	@Data
	public static class DocTypeMappingTransformerConfig extends DocTypeConfig {
		private MappingBehavior action = MappingBehavior.MAPPING;
		private RuleRef ruleRefs;
		private boolean isDbBacked;
	}

	@Data
	public static class DefaultMappingTransformerConfig {
		private MappingBehavior action = MappingBehavior.MAPPING;
		private RuleRef ruleRefs;
		private boolean isDbBacked;
	}

	public enum MappingBehavior { MAPPING, SKIP, TERMINATE }

	@Override
	public List<RuleRef> getRuleRefs() {
		// TODO Auto-generated method stub
		List<RuleRef> rules = new ArrayList<RuleRef>();
		if(docTypeConfigs!=null)
			for(DocTypeMappingTransformerConfig item : docTypeConfigs) {
				rules.add(item.getRuleRefs());
			}
		return rules;
	}

}

/**
 * Custom deserializer to handle dot notation with array indices like:
 * "docTypeConfigs[0].transformerRef": "emfp-request-850"
 * "docTypeConfigs[0].behavior": "TRANSFORM"
 * "docTypeConfigs[0].ruleRefs[0].name": "ContivoMapForRequest850"
 * "docTypeConfigs[0].ruleRefs[0].version": "1.0"
 * "docTypeConfigs[0].isDbBacked": true
 *
 * Converts them to proper List<DocTypeMappingTransformerConfig> structure.
 */
class MappingTransformerFlowStepConfigDeserializer extends JsonDeserializer<MappingTransformerFlowStepConfig> {

	private static final Pattern DOC_TYPE_CONFIG_PATTERN =
            Pattern.compile("configurationList\\[(\\d+)]\\.(.+)");

    private static final Pattern DEFAULT_CONFIG_PATTERN =
            Pattern.compile("defaultConfig\\.(.+)");

    private static final String RULE_REFS_PREFIX = "ruleRefs.";

    @Override
    public MappingTransformerFlowStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {

        ObjectCodec codec = parser.getCodec();
        JsonNode node = codec.readTree(parser);

        MappingTransformerFlowStepConfig config = new MappingTransformerFlowStepConfig();

        Map<Integer, Map<String, Object>> docTypeConfigData = new HashMap<>();
        Map<String, Object> defaultConfigData = new HashMap<>();

        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            Matcher docTypeMatcher = DOC_TYPE_CONFIG_PATTERN.matcher(fieldName);
            Matcher defaultMatcher = DEFAULT_CONFIG_PATTERN.matcher(fieldName);

            if (docTypeMatcher.matches()) {
                int index = Integer.parseInt(docTypeMatcher.group(1));
                String property = docTypeMatcher.group(2);
                Map<String, Object> configMap = docTypeConfigData.computeIfAbsent(index, k -> new HashMap<>());

                if (property.startsWith(RULE_REFS_PREFIX)) {
                    configMap.computeIfAbsent("ruleRefs", k -> new HashMap<String, Object>());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> ruleRefs = (Map<String, Object>) configMap.get("ruleRefs");
                    String ruleField = property.substring(RULE_REFS_PREFIX.length());
                    ruleRefs.put(ruleField, parseNodeValue(fieldValue));
                } else {
                    configMap.put(property, parseNodeValue(fieldValue));
                }

            } else if (defaultMatcher.matches()) {
                String property = defaultMatcher.group(1);
                if (property.startsWith(RULE_REFS_PREFIX)) {
                    defaultConfigData.computeIfAbsent("ruleRefs", k -> new HashMap<String, Object>());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> ruleRefs = (Map<String, Object>) defaultConfigData.get("ruleRefs");
                    String ruleField = property.substring(RULE_REFS_PREFIX.length());
                    ruleRefs.put(ruleField, parseNodeValue(fieldValue));
                } else {
                    defaultConfigData.put(property, parseNodeValue(fieldValue));
                }
            }
        }

        // Build docTypeConfigs
        List<MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig> docTypeConfigs = new ArrayList<>();
        for (int i = 0; i <= docTypeConfigData.keySet().stream().mapToInt(Integer::intValue).max().orElse(-1); i++) {
            Map<String, Object> map = docTypeConfigData.get(i);
            if (map == null) continue;

            MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig dtc =
                    new MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig();

            if (map.containsKey("documentTypeId"))
                dtc.setDocumentTypeId(toInt(map.get("documentTypeId")));
            if (map.containsKey("documentTypeName"))
                dtc.setName((String) map.get("documentTypeName"));
            if (map.containsKey("documentTypeVersion"))
                dtc.setVersion((String) map.get("documentTypeVersion"));
            if (map.containsKey("action"))
                dtc.setAction(MappingTransformerFlowStepConfig.MappingBehavior.valueOf((String) map.get("action")));
            if (map.containsKey("isDbBacked"))
                dtc.setDbBacked((Boolean) map.get("isDbBacked"));

            if (map.containsKey("ruleRefs")) {
                Map<String, Object> rr = (Map<String, Object>) map.get("ruleRefs");
                RuleRef ref = new RuleRef();
                if (rr.containsKey("name")) ref.setRuleName((String) rr.get("name"));
                if (rr.containsKey("version")) ref.setRuleVersion((String) rr.get("version"));
                if (rr.containsKey("id")) ref.setId(toInt(rr.get("id")));
                dtc.setRuleRefs(ref);
            }

            docTypeConfigs.add(dtc);
        }

        config.setDocTypeConfigs(docTypeConfigs);

        // Build defaultConfig
        if (!defaultConfigData.isEmpty()) {
            MappingTransformerFlowStepConfig.DefaultMappingTransformerConfig def =
                    new MappingTransformerFlowStepConfig.DefaultMappingTransformerConfig();

            if (defaultConfigData.containsKey("action"))
                def.setAction(MappingTransformerFlowStepConfig.MappingBehavior.valueOf((String) defaultConfigData.get("action")));
            if (defaultConfigData.containsKey("isDbBacked"))
                def.setDbBacked((Boolean) defaultConfigData.get("isDbBacked"));

            if (defaultConfigData.containsKey("ruleRefs")) {
                Map<String, Object> rr = (Map<String, Object>) defaultConfigData.get("ruleRefs");
                RuleRef ref = new RuleRef();
                if (rr.containsKey("name")) ref.setRuleName((String) rr.get("name"));
                if (rr.containsKey("version")) ref.setRuleVersion((String) rr.get("version"));
                if (rr.containsKey("id")) ref.setId(toInt(rr.get("id")));
                def.setRuleRefs(ref);
            }

            config.setDefaultConfig(def);
        }

        return config;
    }

    private Object parseNodeValue(JsonNode node) {
        if (node.isTextual()) return node.asText();
        if (node.isBoolean()) return node.asBoolean();
        if (node.isInt()) return node.asInt();
        if (node.isLong()) return node.asLong();
        if (node.isDouble()) return node.asDouble();
        return node.toString();
    }

    private int toInt(Object val) {
        if (val instanceof Integer) return (int) val;
        if (val instanceof Long) return ((Long) val).intValue();
        if (val instanceof String) return Integer.parseInt((String) val);
        throw new IllegalArgumentException("Cannot convert to int: " + val);
    }
}
