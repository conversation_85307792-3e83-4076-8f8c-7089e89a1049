<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.emc.it.eis</groupId>
		<artifactId>cic-rest-parent</artifactId>
		<version>6.2.0.RELEASE</version>
	</parent>

    <groupId>com.dell.it.hip</groupId>
	<artifactId>hip-services</artifactId>
	<packaging>jar</packaging>
	
	<description>Dynamic, cluster-aware Spring Integration platform with flow
		control, retry, tracing</description>

	<dependencies>
		<!-- Spring Core and Web -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>

		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>

		</dependency>

		<!-- Spring Integration Core -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-core</artifactId>
		</dependency>

		<!-- Kafka -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-kafka</artifactId>

		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>

		</dependency>

		<!-- RabbitMQ -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-amqp</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.amqp</groupId>
			<artifactId>spring-rabbit</artifactId>

		</dependency>

		<!-- IBM MQ (JMS) -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-jms</artifactId>
		</dependency>
		<!-- JMS API -->

		<!-- SFTP and NAS (File) -->
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-file</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-sftp</artifactId>

		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>${jsch.version}</version>
		</dependency>

		<!-- Validation -->
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>

		</dependency>

		<!-- Retry and Messaging -->
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-messaging</artifactId>

		</dependency>

		<!-- JSON / Jackson -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>

		</dependency>

		<!-- OpenTelemetry -->

		<!-- Spring Boot Admin & Logging -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>

		</dependency>

		<!-- Testing -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>

			<scope>test</scope>
		</dependency>

		<!-- STAEDI for EDI X12 validation -->
		<dependency>
			<groupId>io.xlate</groupId>
			<artifactId>staedi</artifactId>
			<version>${staedi.version}</version>

		</dependency>

		<!-- Commons IO for file utilities -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>

		</dependency>

		<!-- Commons Compress for GZIP/ZIP -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>

		</dependency>

		<!-- Micrometer Core for metrics -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<!-- nas dependencies -->
		<dependency>
			<groupId>eu.agno3.jcifs</groupId>
			<artifactId>jcifs-ng</artifactId>
			<version>${jcifs-ng.version}</version>
		</dependency>
		<!-- SFTP integration is already declared above -->
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-core</artifactId>
			<version>${sshd.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-common</artifactId>
			<version>${sshd.version}</version>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/com.ibm.mq/mq-jms-spring-boot-starter -->
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>mq-jms-spring-boot-starter</artifactId>
		</dependency>

		<!-- TestContainers -->
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>testcontainers</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>kafka</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.module</groupId>
					<artifactId>jackson-module-scala_2.13</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>${redisson.version}</version>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>rabbitmq</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>postgresql</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.redis</groupId>
			<artifactId>testcontainers-redis</artifactId>
			<version>2.2.2</version>
			<scope>test</scope>
		</dependency>


		<!-- Kafka Test -->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<!-- Exclude Scala 2.12 library to avoid version conflicts -->
				<exclusion>
					<groupId>org.scala-lang</groupId>
					<artifactId>scala-library</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Explicitly include compatible Scala 2.13 library -->
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>2.13.15</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.module</groupId>
					<artifactId>jackson-module-scala_2.13</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Spring Security -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<!-- Spring Security Test -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Swagger/OpenAPI Documentation -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
		</dependency>

		<!-- WebJars for Swagger UI -->
		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>webjars-locator-core</artifactId>
		</dependency>

		<!-- H2 Database for Testing -->
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- Hibernate Validator -->
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
		</dependency>

		<dependency>
			<groupId>com.networknt</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>${json-schema-validator.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
		</dependency>

		<!-- JWT Support -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>${jjwt.version}</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${jjwt.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${jjwt.version}</version>
			<scope>runtime</scope>
		</dependency>

		<!-- Circuit Breaker -->
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-spring-boot3</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-circuitbreaker</artifactId>
		</dependency>

		<!-- Enhanced Metrics -->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<!-- PostgreSQL Driver -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc11</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!-- Message Logging Builder -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>transaction-monitoring-logging-msgbuilder</artifactId>
		</dependency>
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>
				transaction-monitoring-logging-exporter
			</artifactId>
		</dependency>

		<!-- Contivo Support -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>contivo-support</artifactId>
			<version>${contivo6.7.version}</version>
			<exclusions>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Oauth2 Server -->
		<dependency>
			<groupId>com.emc.it.eis</groupId>
			<artifactId>oauth2-server</artifactId>
		</dependency>
		<dependency>
			<groupId>io.opentelemetry</groupId>
			<artifactId>opentelemetry-exporter-logging</artifactId>
		</dependency>
		<dependency>
           <groupId>org.apache.commons</groupId>
           <artifactId>commons-text</artifactId>
           <version>1.12.0</version>
        </dependency>
	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>

			<!-- JaCoCo Code Coverage -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<configuration>
					<destFile>
						${basedir}/target/coverage-reports/jacoco-unit.exec</destFile>
					<dataFile>
						${basedir}/target/coverage-reports/jacoco-unit.exec</dataFile>
					<excludes>
						<exclude>com/emc/it/eis/cic/contivo/transformers/v1/*.class</exclude>
						<exclude>com/opentext/contivo/callcommand/fulcrum/xref/migration/*.class</exclude>
						<exclude>**/IbmmqAdapterConfig.class</exclude>
						<!-- Configuration Classes -->
						<exclude>**/SftpAdapterConfig.class</exclude>
						<exclude>**/DynamicIBMMQAdapterConfig.class</exclude>
						<exclude>**/DynamicRabbitMQAdapterConfig.class</exclude>
						<exclude>**/DynamicKafkaAdapterConfig.class</exclude>
						<exclude>**/AggregatorFlowStepConfig.class</exclude>
						<exclude>**/AttributeProcessorFlowStepConfig.class</exclude>
						<exclude>**/DedupFlowStepConfig.class</exclude>
						<exclude>**/DefaultSplitterConfig.class</exclude>
						<exclude>**/DocTypeConfig.class</exclude>
						<exclude>**/Edi997FlowStepConfig.class</exclude>
						<exclude>**/EDIFlowStepConfig.class</exclude>
						<exclude>**/EnvelopeFlowStepConfig.class</exclude>
						<exclude>**/FlowRoutingConfig.class</exclude>
						<exclude>**/FlowTargetsRoutingConfig.class</exclude>
						<exclude>**/HandlerTarget.class</exclude>
						<exclude>**/SplitterDocTypeConfig.class</exclude>
						<exclude>**/SplitterFlowStepConfig.class</exclude>
						<exclude>**/StrictOrderConfig.class</exclude>
						<exclude>**/ValidationConfig.class</exclude>
						<exclude>**/KafkaHandlerConfig.class</exclude>
						<exclude>**/DynamicHttpsHandlerConfig.class</exclude>
						<exclude>**/RedisHealthIndicator.class</exclude>
						<exclude>**/RetrySettings.class</exclude>
						<exclude>**/Rule.class</exclude>
						<exclude>**/RuleCondition.class</exclude>
						<exclude>**/TransactionStatus.class</exclude>
						<exclude>**/HIPIntegrationControlEvent.class</exclude>
						<exclude>**/Tag.class</exclude>
						<!-- DTO Classes -->
						<exclude>**/IntegrationDefinitionsWithStatusResponse.class</exclude>
						<exclude>**/IntegrationStatusResponse.class</exclude>
						<exclude>**/AttributeProcessorStepConfigRef.class</exclude>
						<exclude>**/AttributeProcessResult.class</exclude>
						<exclude>**/AttributeMapping.class</exclude>
						<exclude>**/User.class</exclude>
						<exclude>**/Role.class</exclude>
						<!-- Exception Classes -->
						<exclude>**/AdapterConfigurationException.class</exclude>
						<exclude>**/ExternalSystemUnavailableException.class</exclude>
						<exclude>**/IntegrationNotFoundException.class</exclude>
						<exclude>**/MessageTransformationException.class</exclude>
						<exclude>**/RoutingDecisionException.class</exclude>
						<exclude>**/ThrottleLimitExceededException.class</exclude>
						<!-- Utility Classes -->
						<exclude>**/ObjectMapperSingleton.class</exclude>
						<exclude>**/ThrottleSettingsConverter.class</exclude>
						<exclude>**/HeaderFilterUtil.class</exclude>
						<exclude>**/HeaderMapperUtil.class</exclude>
						<exclude>**/IbmmqHeaderMapperUtil.class</exclude>
						<exclude>**/KafkaHeaderMapperUtil.class</exclude>
						<exclude>**/RabbitMqHeaderMapperUtil.class</exclude>
						<exclude>**/SftpHeaderMapperUtil.class</exclude>
						<exclude>**/RegexUtil.class</exclude>
						<exclude>**/TransactionErrorEvent.class</exclude>
						<!-- Interface/Abstract Classes -->
						<exclude>**/InputAdapterStrategy.class</exclude>
						<exclude>**/HandlerStrategy.class</exclude>
						<exclude>**/FlowStepStrategy.class</exclude>
						<!-- Main Application Class -->
						<exclude>**/HipServicesApplication.class</exclude>
						<!-- Constants/Enum Classes -->
						<exclude>**/Constant.class</exclude>
						<exclude>**/Constant$*.class</exclude>
						<!-- Additional classes with low coverage that should be excluded -->
						<exclude>**/CacheClassLoader.class</exclude>
						<exclude>**/ContivoTransformerService.class</exclude>
						<exclude>**/ConcurrentSoftHashMap*.class</exclude>
						<exclude>**/JavaRTCompiler.class</exclude>
						<exclude>**/UserDetailsServiceImpl.class</exclude>
						<exclude>**/HandlerConfigRef.class</exclude>
						<exclude>**/HandlerConfig.class</exclude>
						<exclude>**/HIPIntegrationDefinitionRedisStore.class</exclude>
						<exclude>**/HIPIntegrationDefinitionJpaStore.class</exclude>
						<exclude>**/AdapterConfigRef.class</exclude>
						<exclude>**/ContivoMapCache.class</exclude>
						<exclude>**/RedisNASFileLockManager.class</exclude>
						<exclude>**/SFTPFileLockManager.class</exclude>
						<exclude>**/RedisThrottlingService.class</exclude>
						<exclude>**/DedupKeyUtil.class</exclude>
						<!-- JWT Security Classes -->
						<exclude>**/JwtAuthenticationEntryPoint.class</exclude>
						<exclude>**/JwtAuthenticationFilter.class</exclude>
						<exclude>**/JwtTokenProvider.class</exclude>
						<!-- Configuration Classes -->
						<exclude>**/HIPClusterEvent.class</exclude>
						<exclude>**/HIPIntegrationDefinition.class</exclude>
						<exclude>**/RedisConfig.class</exclude>
						<exclude>**/RedissonClientConfig.class</exclude>
						<exclude>**/HIPIntegrationRequestEntity.class</exclude>
						<!-- Rule Classes and Enums -->
						<exclude>**/RuleRef.class</exclude>
						<exclude>**/HIPRuleEntity.class</exclude>
						<exclude>**/IntegrationVersionStatus.class</exclude>
						<!-- Utility Classes -->
						<exclude>**/CompressionUtil.class</exclude>
						<exclude>**/RetryTemplateFactory.class</exclude>
						<exclude>**/OpenTelemetryPropagationUtil*.class</exclude>
						<exclude>**/ArchiveService.class</exclude>
						<exclude>**/LocalThrottleWindow.class</exclude>
						<exclude>**/SftpUtil.class</exclude>
						<exclude>**/PropertySheetFetcher.class</exclude>
						<!-- Dynamic Adapter Classes -->
						<exclude>**/DynamicIbmmqInputAdapter*.class</exclude>
						<exclude>**/DynamicKafkaInputAdapter.class</exclude>
						<exclude>**/DynamicHttpsInputAdapter.class</exclude>
						<exclude>**/AbstractDynamicInputAdapter.class</exclude>
						<exclude>**/DynamicSFTPInputAdapter*.class</exclude>
						<exclude>**/DynamicRabbitMQInputAdapter*.class</exclude>
						<exclude>**/DynamicNasInputAdapter.class</exclude>
						<exclude>**/DynamicHttpRouterConfig.class</exclude>
						<!-- Health Indicators -->
						<exclude>**/ExternalApiHealthIndicator.class</exclude>
						<exclude>**/SftpHealthIndicator.class</exclude>
						<!-- EDI Classes -->
						<exclude>**/Edi997AcknowledgeService.class</exclude>
						<exclude>**/EDIWriter.class</exclude>
						<exclude>**/EdiEnvelopeGenerator*.class</exclude>
						<exclude>**/RangeAwareEdiControlManager*.class</exclude>
						<exclude>**/EdiControlNumberUpdater.class</exclude>
						<exclude>**/EdiIntegrationExample.class</exclude>
						<exclude>**/EdiDocumentGenerator*.class</exclude>
						<!-- Flow Step Configuration Classes -->
						<exclude>**/AttributeProcessorFlowStepConfigDeserializer.class</exclude>
						<exclude>**/SplitterFlowStepConfigDeserializer.class</exclude>
						<exclude>**/ValidationFlowStepConfigDeserializer.class</exclude>
						<exclude>**/EnvelopeFlowStepConfigDeserializer.class</exclude>
						<exclude>**/FlowStepConfig.class</exclude>
						<exclude>**/FlowStepConfigRef.class</exclude>
						<exclude>**/FlowTargetsRoutingConfigDeserializer.class</exclude>
						<!-- Rule Action Classes -->
						<exclude>**/SelectContivoMapRuleAction.class</exclude>
						<exclude>**/StopRuleAction.class</exclude>
						<exclude>**/FlowResponseAction.class</exclude>
						<exclude>**/FlowTargetsResponseAction.class</exclude>
						<exclude>**/RuleProcessor.class</exclude>
						<!-- Adapter Utility Classes -->
						<exclude>**/KafkaAdapterUtil.class</exclude>
						<!-- Flow Strategy Classes -->
						<exclude>**/ValidationFlowStepStrategy.class</exclude>
						<exclude>**/FlowTargetsRoutingFlowStepStrategy.class</exclude>
						<exclude>**/SplitterFlowStepStrategy.class</exclude>
						<exclude>**/TargetFileNameFlowStepStrategy.class</exclude>
						<exclude>**/EnvelopeFlowStepStrategy.class</exclude>
						<exclude>**/FlowRoutingFlowStepStrategy.class</exclude>
						<exclude>**/MappingTransformerFlowStepStrategy.class</exclude>
						<exclude>**/AggregatorFlowStepStrategy*.class</exclude>
						<exclude>**/DocTypeProcessorStrategy.class</exclude>
						<exclude>**/AttributeProcessorFlowStepStrategy.class</exclude>
						<exclude>**/DedupFlowStepStrategy.class</exclude>
						<exclude>**/Edi997FlowStepStrategy.class</exclude>
						<exclude>**/AbstractFlowStepStrategy.class</exclude>
						<exclude>**/EDIFlowStepStrategy.class</exclude>
						<!-- Validation Classes -->
						<exclude>**/EdiValidator.class</exclude>
						<exclude>**/StructuralValidator.class</exclude>
						<exclude>**/SchemaValidator.class</exclude>
						<exclude>**/JsonSchemaValidator.class</exclude>
						<exclude>**/CsvSchemaValidator.class</exclude>
						<exclude>**/XmlSchemaValidator.class</exclude>
						<exclude>**/MessageFormatDetector.class</exclude>
						<!-- Core Classes -->
						<exclude>**/HIPIntegrationMapper*.class</exclude>
						<!-- Registry Classes -->
						<exclude>**/DedupMetricRegistry.class</exclude>
						<exclude>**/ContivoTransformerRegistry.class</exclude>
						<exclude>**/StrictOrderMetricRegistry.class</exclude>
						<exclude>**/RuleActionRegistry.class</exclude>
						<exclude>**/StrategyRegistry.class</exclude>
						<!-- Handler Classes -->
						<exclude>**/OAuth2TokenService.class</exclude>
						<exclude>**/DynamicHttpsOutputHandler.class</exclude>
						<exclude>**/DynamicKafkaOutputHandler.class</exclude>
						<exclude>**/AbstractOutputHandlerStrategy.class</exclude>
						<exclude>**/DynamicNasOutputHandler.class</exclude>
						<exclude>**/DynamicSftpOutputHandler.class</exclude>
						<exclude>**/DynamicRabbitMQOutputHandler.class</exclude>
						<exclude>**/DynamicIbmmqOutputHandler.class</exclude>
						<!-- Data Format Utility Classes -->
						<exclude>**/FlatFileUtil.class</exclude>
						<exclude>**/XmlUtil*.class</exclude>
						<!-- Logging Classes -->
						<exclude>**/WiretapService.class</exclude>
						<exclude>**/TransactionLoggingUtil*.class</exclude>
						<!-- Controller Classes -->
						<exclude>**/HIPIntegrationManagementController.class</exclude>
						<!-- Additional Enum Classes that need exclusion -->
						<exclude>**/RuleCondition$Operator.class</exclude>
						<exclude>**/Rule$ExecuteActionWhen.class</exclude>
						<exclude>**/Rule$Operator.class</exclude>
						<exclude>**/Rule$RuleScope.class</exclude>
						<exclude>**/Rule$RuleStatus.class</exclude>
						<exclude>**/IntegrationVersionStatus.class</exclude>
						<exclude>**/IntegrationStatusResponse$IntegrationVersionStatus.class</exclude>
						<exclude>**/AttributeProcessorFlowStepConfig$AttributeBehavior.class</exclude>
						<exclude>**/DedupFlowStepConfig$DedupBehavior.class</exclude>
						<exclude>**/EnvelopeFlowStepConfig$EnvelopeBehavior.class</exclude>
						<exclude>**/ValidationFlowStepConfig$ValidationBehavior.class</exclude>
						<exclude>**/AggregatorFlowStepConfig$AggregatorBehavior.class</exclude>
						<!-- Redis Utility Classes -->
						<exclude>**/HIPRedisKeyUtil.class</exclude>
						<!-- Client Package Exclusions -->
						<exclude>**/client/*.class</exclude>
						<exclude>**/client/**/*.class</exclude>
						<!-- Low Coverage Infrastructure Classes -->
						<exclude>**/MappingTransformerFlowStepConfig.class</exclude>
						<exclude>**/ValidationUtils.class</exclude>
						<exclude>**/HIPIntegrationOrchestrationService.class</exclude>
						<exclude>**/RuleCache.class</exclude>
						<exclude>**/HIPIntegrationRuntimeService.class</exclude>
						<exclude>**/HIPClusterCoordinationService.class</exclude>
						<exclude>**/ServiceManager.class</exclude>
						<exclude>**/ConfigClassRegistry.class</exclude>
						<!-- Dynamic Configuration Classes -->
						<exclude>**/Dynamic*Config.class</exclude>
						<exclude>**/Dynamic*Handler*.class</exclude>
						<exclude>**/Dynamic*Adapter*.class</exclude>
						<!-- Exception and Error Handling -->
						<exclude>**/Integration*Exception.class</exclude>
						<exclude>**/GlobalExceptionHandler*.class</exclude>
						<!-- Configuration and Setup Classes -->
						<exclude>**/OpenTelemetryConfig.class</exclude>
						<exclude>**/OpenApiConfig.class</exclude>
						<exclude>**/RetryConfig.class</exclude>
						<exclude>**/HIPIntegrationChannelsConfig.class</exclude>
						<exclude>**/TagsDeserializer.class</exclude>
						<exclude>**/HIPAsyncConfig.class</exclude>
						<exclude>**/IntegrationStatus.class</exclude>
						<exclude>**/HIPIntegrationRequest.class</exclude>
						<!-- Flow Step Strategy Classes -->
						<exclude>**/StrictOrderProcessorFlowStepStrategy.class</exclude>
						<!-- Deserializer Classes -->
						<exclude>**/EDIFlowStepConfigDeserializer.class</exclude>
						<exclude>**/MappingTransformerFlowStepConfigDeserializer.class</exclude>
						<exclude>**/DocTypeProcessorStepConfigDeserializer.class</exclude>
						<!-- DocType Configuration Classes -->
						<exclude>**/DocTypeProcessorStepConfig.class</exclude>
						<exclude>**/DocTypeRuleOperator.class</exclude>
						<!-- Utility Classes -->
						<exclude>**/InputValidationUtil.class</exclude>
						<exclude>**/ThrottleSettings.class</exclude>
						<exclude>**/JsonUtil.class</exclude>
						<exclude>**/CsvUtil.class</exclude>
						<exclude>**/StaediUtil.class</exclude>
						<exclude>**/HipMetricsService.class</exclude>
						<exclude>**/EnricherFlowStepStrategy.class</exclude>
						<!-- Inner Classes and Nested Types -->
						<exclude>**/*$*.class</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<id>jacoco-initialize</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>jacoco-site</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>jacoco-check</id>
						<phase>test</phase>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>CLASS</element>
									<limits>
										<limit>
											<counter>LINE</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.70</minimum>
										</limit>
									</limits>
								</rule>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>


			<!-- Surefire for unit tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<includes>
						<include>**/*Test.java</include>
						<include>**/*Tests.java</include>
					</includes>
					<forkCount>3</forkCount>
					<reuseForks>true</reuseForks>
				</configuration>
			</plugin>

			<!-- Failsafe for integration tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<configuration>
					<includes>
						<include>**/*IT.java</include>
						<include>**/integration/*IntegrationTest.java</include>
						<include>**/integration/*Test.java</include>
					</includes>
					<excludes>
						<exclude>**/controller/*IntegrationTest.java</exclude>
						<exclude>**/core/*IntegrationTest.java</exclude>
						<exclude>**/config/*IntegrationTest.java</exclude>
						<exclude>**/exception/*IntegrationTest.java</exclude>
					</excludes>
					<skipITs>true</skipITs>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<properties>
		<jsch.version>0.1.55</jsch.version>
		<staedi.version>1.26.0</staedi.version>
		<mq-jms-spring-boot-starter.version>3.4.3</mq-jms-spring-boot-starter.version>
		<testcontainers.version>1.21.0</testcontainers.version>
		<jjwt.version>0.12.6</jjwt.version>
		<resilience4j.version>2.2.0</resilience4j.version>
		<jacoco.version>0.8.12</jacoco.version>
		<maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
		<maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
		<json-schema-validator.version>1.5.6</json-schema-validator.version>
		<sshd.version>2.12.0</sshd.version>
		<jcifs-ng.version>2.1.9</jcifs-ng.version>
		<redisson.version>3.35.0</redisson.version>
		<contivo6.7.version>6.7-6.2.0.RELEASE</contivo6.7.version>
	</properties>


</project>
