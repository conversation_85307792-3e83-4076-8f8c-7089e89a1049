FileName   Line Coverage     Uncovered Lines
---------  --------------    ----------------
src/main/java/com/dell/it/hip/config/FlowSteps/AggregatorFlowStepConfig.java 0.0%	1
src/main/java/com/dell/it/hip/config/FlowSteps/AttributeProcessorFlowStepConfig.java0.0%	67
src/main/java/com/dell/it/hip/config/FlowSteps/AttributeProcessorStepConfigRef.java0.0%	7
src/main/java/com/dell/it/hip/config/FlowSteps/AttributeProcessResult.java .0%	15
src/main/java/com/dell/it/hip/util/contivoUtils/CacheClassLoader.java 0.0%	182
src/main/java/com/dell/it/hip/util/contivoUtils/ConcurrentSoftHashMap.java 0.0%	56
src/main/java/com/dell/it/hip/util/contivoUtils/Constant.java 0.0%	7
src/main/java/com/dell/it/hip/util/validation/CsvSchemaValidator.java 0.0%	52
src/main/java/com/dell/it/hip/util/dataformatUtils/CsvUtil.java 0.0%	26
src/main/java/com/dell/it/hip/config/FlowSteps/DedupFlowStepConfig.java 0.0%	1
src/main/java/com/dell/it/hip/util/redis/DedupKeyUtil.java 0.0%	14
src/main/java/com/dell/it/hip/config/FlowSteps/DefaultSplitterConfig.java 0.0%	5
src/main/java/com/dell/it/hip/config/FlowSteps/DocTypeConfig.java 0.0%	1
src/main/java/com/dell/it/hip/config/FlowSteps/Edi997FlowStepConfig.java 0.0%	5
src/main/java/com/dell/it/hip/util/ediUtils/EdiControlNumberUpdater.java 0.0%	138
src/main/java/com/dell/it/hip/util/ediUtils/EdiDocumentGenerator.java 0.0%	109
src/main/java/com/dell/it/hip/util/ediUtils/EdiEnvelopeGenerator.java 0.0%	99
src/main/java/com/dell/it/hip/config/FlowSteps/EDIFlowStepConfig.java 0.0%	79
src/main/java/com/dell/it/hip/util/ediUtils/EdiIntegrationExample.java 0.0%	32
src/main/java/com/dell/it/hip/util/validation/EdiValidator.java 0.0%	30
src/main/java/com/dell/it/hip/strategy/edi/EDIWriter.java 0.0%	55
src/main/java/com/dell/it/hip/config/FlowSteps/EnvelopeFlowStepConfig.java 0.0%	33
src/main/java/com/dell/it/hip/util/dataformatUtils/FlatFileUtil.java 0.0%	50
src/main/java/com/dell/it/hip/config/FlowSteps/FlowRoutingConfig.java 0.0%	3
src/main/java/com/dell/it/hip/strategy/flows/FlowStepStrategy.java 0.0%	1
src/main/java/com/dell/it/hip/strategy/flows/rules/FlowTargetsResponseAction.java 0.0%	16
src/main/java/com/dell/it/hip/config/FlowSteps/FlowTargetsRoutingConfig.java 0.0%	52
src/main/java/com/dell/it/hip/strategy/handlers/HandlerStrategy.java 0.0%	3
src/main/java/com/dell/it/hip/config/FlowSteps/HandlerTarget.java 0.0%	10
src/main/java/com/dell/it/hip/util/HeaderFilterUtil.java 0.0%	5
src/main/java/com/dell/it/hip/util/adapters/HeaderMapperUtil.java 0.0%	6
src/main/java/com/dell/it/hip/config/HIPIntegrationControlEvent.java 0.0%	19
src/main/java/com/dell/it/hip/core/repository/HIPIntegrationDefinitionRedisStore.java 0.0%	30
src/main/java/com/dell/it/hip/config/adapters/IbmmqAdapterConfig.java 0.0%	36
src/main/java/com/dell/it/hip/util/adapters/IbmmqHeaderMapperUtil.java 0.0%	23
src/main/java/com/dell/it/hip/strategy/adapters/InputAdapterStrategy.java 0.0%	3
src/main/java/com/dell/it/hip/controller/dto/IntegrationDefinitionsWithStatusResponse.java 0.0%	15
src/main/java/com/dell/it/hip/util/contivoUtils/JavaRTCompiler.java 0.0%	43
src/main/java/com/dell/it/hip/util/validation/JsonSchemaValidator.java 0.0%	15
src/main/java/com/dell/it/hip/util/dataformatUtils/JsonUtil.java 0.0%	45
src/main/java/com/dell/it/hip/util/adapters/KafkaAdapterUtil.java 0.0%	22
src/main/java/com/dell/it/hip/config/Handlers/KafkaHandlerConfig.java
0.0%	10
src/main/java/com/dell/it/hip/util/adapters/KafkaHeaderMapperUtil.java
0.0%	17
src/main/java/com/dell/it/hip/util/LocalThrottleWindow.java
0.0%	20
src/main/java/com/dell/it/hip/util/validation/MessageFormatDetector.java
0.0%	36
src/main/java/com/dell/it/hip/util/ObjectMapperSingleton.java
0.0%	2
src/main/java/com/dell/it/hip/util/adapters/RabbitMqHeaderMapperUtil.java
0.0%	16
src/main/java/com/dell/it/hip/util/ediUtils/RangeAwareEdiControlManager.java
0.0%	65
src/main/java/com/dell/it/hip/config/RedisHealthIndicator.java
0.0%	19
src/main/java/com/dell/it/hip/config/RedissonClientConfig.java
0.0%	15
src/main/java/com/dell/it/hip/util/redis/RedisThrottlingService.java
0.0%	25
src/main/java/com/dell/it/hip/util/dataformatUtils/RegexUtil.java
0.0%	9
src/main/java/com/dell/it/hip/config/RetrySettings.java
0.0%	7
src/main/java/com/dell/it/hip/config/rules/Rule.java
0.0%	4
src/main/java/com/dell/it/hip/config/rules/RuleCondition.java
0.0%	1
src/main/java/com/dell/it/hip/strategy/flows/rules/SelectContivoMapRuleAction.java
0.0%	25
src/main/java/com/dell/it/hip/config/adapters/SftpAdapterConfig.java
0.0%	45
src/main/java/com/dell/it/hip/util/adapters/SftpHeaderMapperUtil.java
0.0%	20
src/main/java/com/dell/it/hip/util/SftpUtil.java
0.0%	22
src/main/java/com/dell/it/hip/config/FlowSteps/SplitterDocTypeConfig.java
0.0%	1
src/main/java/com/dell/it/hip/config/FlowSteps/SplitterFlowStepConfig.java
0.0%	69
src/main/java/com/dell/it/hip/util/dataformatUtils/StaediUtil.java
0.0%	125
src/main/java/com/dell/it/hip/strategy/flows/rules/StopRuleAction.java
0.0%	5
src/main/java/com/dell/it/hip/config/FlowSteps/StrictOrderConfig.java
0.0%	1
src/main/java/com/dell/it/hip/util/validation/StructuralValidator.java
0.0%	8
src/main/java/com/dell/it/hip/util/ThrottleSettingsConverter.java
0.0%	8
src/main/java/com/dell/it/hip/util/logging/TransactionErrorEvent.java
0.0%	25
src/main/java/com/dell/it/hip/config/TransactionStatus.java
0.0%	7
src/main/java/com/dell/it/hip/config/FlowSteps/ValidationConfig.java
0.0%	13
src/main/java/com/dell/it/hip/util/validation/XmlSchemaValidator.java
0.0%	16
src/main/java/com/dell/it/hip/util/dataformatUtils/XmlUtil.java
0.0%	122
src/main/java/com/dell/it/hip/util/PropertySheetFetcher.java
0.7%	143
src/main/java/com/dell/it/hip/util/logging/TransactionLoggingUtil.java
0.7%	135
src/main/java/com/dell/it/hip/strategy/flows/DocTypeProcessorStrategy.java
0.9%	113
src/main/java/com/dell/it/hip/strategy/edi/Edi997AcknowledgeService.java
1.0%	199
src/main/java/com/dell/it/hip/strategy/flows/SplitterFlowStepStrategy.java
1.0%	96
src/main/java/com/dell/it/hip/strategy/flows/ValidationFlowStepStrategy.java
1.1%	92
src/main/java/com/dell/it/hip/strategy/flows/EDIFlowStepStrategy.java
1.5%	65
src/main/java/com/dell/it/hip/strategy/flows/rules/RuleProcessor.java
1.5%	64
src/main/java/com/dell/it/hip/strategy/flows/DedupFlowStepStrategy.java
1.6%	61
src/main/java/com/dell/it/hip/strategy/flows/TargetFileNameFlowStepStrategy.java
2.1%	47
src/main/java/com/dell/it/hip/strategy/flows/AttributeProcessorFlowStepStrategy.java
2.2%	91
src/main/java/com/dell/it/hip/strategy/flows/EnvelopeFlowStepStrategy.java
2.5%	77
src/main/java/com/dell/it/hip/strategy/adapters/DynamicNasInputAdapter.java
2.8%	241
src/main/java/com/dell/it/hip/util/CompressionUtil.java
2.9%	34
src/main/java/com/dell/it/hip/strategy/flows/FlowRoutingFlowStepStrategy.java
2.9%	34
src/main/java/com/dell/it/hip/strategy/flows/AggregatorFlowStepStrategy.java
3.5%	111
src/main/java/com/dell/it/hip/strategy/adapters/DynamicIbmmqInputAdapter.java
3.8%	126
src/main/java/com/dell/it/hip/strategy/adapters/DynamicRabbitMQInputAdapter.java
4.0%	120
src/main/java/com/dell/it/hip/security/jwt/JwtTokenProvider.java
4.1%	47
src/main/java/com/dell/it/hip/strategy/handlers/DynamicHttpsOutputHandler.java
4.2%	92
src/main/java/com/dell/it/hip/strategy/adapters/DynamicSFTPInputAdapter.java
4.6%	165
src/main/java/com/dell/it/hip/monitoring/health/SftpHealthIndicator.java
4.8%	40
src/main/java/com/dell/it/hip/strategy/flows/StrictOrderProcessorFlowStepStrategy.java
4.9%	116
src/main/java/com/dell/it/hip/util/contivoUtils/ContivoTransformerService.java
5.3%	72
src/main/java/com/dell/it/hip/strategy/handlers/DynamicNasOutputHandler.java
5.3%	72
src/main/java/com/dell/it/hip/core/HIPClusterCoordinationService.java
5.3%	126
src/main/java/com/dell/it/hip/config/FlowSteps/FlowStepConfig.java
5.4%	35
src/main/java/com/dell/it/hip/strategy/adapters/DynamicHttpsInputAdapter.java
5.6%	118
src/main/java/com/dell/it/hip/strategy/flows/rules/RuleCache.java
5.7%	99
src/main/java/com/dell/it/hip/strategy/handlers/DynamicSftpOutputHandler.java
6.2%	91
src/main/java/com/dell/it/hip/strategy/handlers/DynamicKafkaOutputHandler.java
6.3%	90
src/main/java/com/dell/it/hip/strategy/handlers/DynamicRabbitMQOutputHandler.java
6.3%	89
src/main/java/com/dell/it/hip/config/RedisConfig.java
6.5%	29
src/main/java/com/dell/it/hip/security/service/UserDetailsServiceImpl.java
6.7%	14
src/main/java/com/dell/it/hip/strategy/handlers/DynamicIbmmqOutputHandler.java
6.8%	82
src/main/java/com/dell/it/hip/strategy/handlers/OAuth2TokenService.java
7.1%	26
src/main/java/com/dell/it/hip/strategy/flows/Edi997FlowStepStrategy.java
7.7%	24
src/main/java/com/dell/it/hip/strategy/flows/MappingTransformerFlowStepStrategy.java
8.0%	46
src/main/java/com/dell/it/hip/util/OpenTelemetryPropagationUtil.java
8.2%	167
src/main/java/com/dell/it/hip/util/validation/SchemaValidator.java
8.3%	11
src/main/java/com/dell/it/hip/security/entity/User.java
8.3%	33
src/main/java/com/dell/it/hip/monitoring/health/RedisHealthIndicator.java
8.7%	21
src/main/java/com/dell/it/hip/security/jwt/JwtAuthenticationFilter.java
10.5%	17
src/main/java/com/dell/it/hip/strategy/flows/FlowTargetsRoutingFlowStepStrategy.java
10.9%	57
src/main/java/com/dell/it/hip/config/rules/HIPRuleEntity.java
11.1%	24
src/main/java/com/dell/it/hip/util/RetryTemplateFactory.java
11.1%	8
src/main/java/com/dell/it/hip/strategy/handlers/AbstractOutputHandlerStrategy.java
11.2%	79
src/main/java/com/dell/it/hip/util/ArchiveService.java
12.5%	14
src/main/java/com/dell/it/hip/strategy/flows/rules/FlowResponseAction.java
12.5%	14
src/main/java/com/dell/it/hip/util/redis/RedisNASFileLockManager.java
12.5%	7
src/main/java/com/dell/it/hip/core/registry/DedupMetricRegistry.java
13.0%	20
src/main/java/com/dell/it/hip/core/registry/StrictOrderMetricRegistry.java
13.6%	19
src/main/java/com/dell/it/hip/strategy/adapters/AbstractDynamicInputAdapter.java
14.0%	104
src/main/java/com/dell/it/hip/security/jwt/JwtAuthenticationEntryPoint.java
14.3%	12
src/main/java/com/dell/it/hip/core/registry/RuleActionRegistry.java
14.3%	6
src/main/java/com/dell/it/hip/util/redis/SFTPFileLockManager.java
14.3%	6
src/main/java/com/dell/it/hip/monitoring/health/ExternalApiHealthIndicator.java
14.7%	29
src/main/java/com/dell/it/hip/strategy/flows/AbstractFlowStepStrategy.java
18.2%	9
src/main/java/com/dell/it/hip/util/redis/ContivoMapCache.java
20.0%	16
src/main/java/com/dell/it/hip/config/rules/RuleRef.java
20.0%	4
src/main/java/com/dell/it/hip/util/logging/WiretapService.java
20.4%	39
src/main/java/com/dell/it/hip/core/HIPIntegrationRuntimeService.java
20.5%	35
src/main/java/com/dell/it/hip/util/redis/HIPRedisKeyUtil.java
23.3%	23
src/main/java/com/dell/it/hip/core/registry/ContivoTransformerRegistry.java
25.0%	6
src/main/java/com/dell/it/hip/core/HIPIntegrationOrchestrationService.java
26.3%	334
src/main/java/com/dell/it/hip/config/Handlers/HandlerConfig.java
27.3%	8
src/main/java/com/dell/it/hip/core/ServiceManager.java
30.9%	56
src/main/java/com/dell/it/hip/exception/MessageTransformationException.java
31.6%	13
src/main/java/com/dell/it/hip/exception/RoutingDecisionException.java
31.6%	13
src/main/java/com/dell/it/hip/config/HIPClusterEvent.java
32.6%	29
src/main/java/com/dell/it/hip/exception/AdapterConfigurationException.java
33.3%	4
src/main/java/com/dell/it/hip/config/adapters/DynamicIBMMQAdapterConfig.java
33.3%	4
src/main/java/com/dell/it/hip/config/adapters/DynamicRabbitMQAdapterConfig.java
33.3%	4
src/main/java/com/dell/it/hip/exception/ExternalSystemUnavailableException.java
33.3%	10
src/main/java/com/dell/it/hip/HipServicesApplication.java
33.3%	2
src/main/java/com/dell/it/hip/exception/IntegrationNotFoundException.java
33.3%	4
src/main/java/com/dell/it/hip/config/Tag.java
33.3%	12
src/main/java/com/dell/it/hip/config/FlowSteps/AttributeMapping.java
35.5%	20
src/main/java/com/dell/it/hip/security/entity/Role.java
35.7%	9
src/main/java/com/dell/it/hip/strategy/adapters/DynamicHttpRouterConfig.java
41.7%	7
src/main/java/com/dell/it/hip/exception/ThrottleLimitExceededException.java
42.9%	8
src/main/java/com/dell/it/hip/strategy/adapters/DynamicKafkaInputAdapter.java
43.4%	82
src/main/java/com/dell/it/hip/controller/dto/IntegrationStatusResponse.java
44.4%	15
src/main/java/com/dell/it/hip/config/FlowSteps/FlowStepConfigRef.java
46.3%	22
src/main/java/com/dell/it/hip/config/Handlers/DynamicHttpsHandlerConfig.java
50.0%	1
src/main/java/com/dell/it/hip/controller/HIPIntegrationManagementController.java
50.8%	32
src/main/java/com/dell/it/hip/config/FlowSteps/ValidationFlowStepConfig.java
55.9%	15
src/main/java/com/dell/it/hip/core/registry/StrategyRegistry.java
56.3%	7
src/main/java/com/dell/it/hip/config/adapters/DynamicKafkaAdapterConfig.java
59.3%	46
src/main/java/com/dell/it/hip/config/adapters/AdapterConfigRef.java
60.0%	6
src/main/java/com/dell/it/hip/core/HIPIntegrationMapper.java
60.6%	41
src/main/java/com/dell/it/hip/util/ThrottleSettings.java
61.5%	5
src/main/java/com/dell/it/hip/core/repository/HIPIntegrationDefinitionJpaStore.java
63.6%	8
src/main/java/com/dell/it/hip/config/Handlers/HandlerConfigRef.java
64.7%	6
src/main/java/com/dell/it/hip/config/HIPIntegrationDefinition.java
65.8%	25
src/main/java/com/dell/it/hip/config/HIPIntegrationRequestEntity.java
69.3%	