package com.dell.it.hip.util.dataformatUtils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("FlatFileUtil Tests")
class FlatFileUtilTest {

    @Test
    @DisplayName("Should split flat file by regex start pattern")
    void testSplitByRegexStart_ValidPattern_ReturnsCorrectRecords() {
        // Arrange
        String payload = "1TEST001\nData line 1\nData line 2\n1TEST002\nData line 3\n1TEST003\nData line 4\nData line 5";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(3, result.size());
        assertTrue(result.get(0).startsWith("1TEST001"));
        assertTrue(result.get(1).startsWith("1TEST002"));
        assertTrue(result.get(2).startsWith("1TEST003"));
        assertTrue(result.get(0).contains("Data line 1"));
        assertTrue(result.get(0).contains("Data line 2"));
    }

    @Test
    @DisplayName("Should handle single record")
    void testSplitByRegexStart_SingleRecord_ReturnsOneRecord() {
        // Arrange
        String payload = "1TEST001\nData line 1\nData line 2";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(1, result.size());
        assertTrue(result.get(0).startsWith("1TEST001"));
        assertTrue(result.get(0).contains("Data line 1"));
        assertTrue(result.get(0).contains("Data line 2"));
    }

    @Test
    @DisplayName("Should handle empty payload")
    void testSplitByRegexStart_EmptyPayload_ReturnsEmptyList() {
        // Arrange
        String payload = "";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle null payload")
    void testSplitByRegexStart_NullPayload_ReturnsEmptyList() {
        // Arrange
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(null, startRegex);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle no matching patterns")
    void testSplitByRegexStart_NoMatches_ReturnsEmptyList() {
        // Arrange
        String payload = "Data line 1\nData line 2\nData line 3";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should normalize different line endings")
    void testSplitByRegexStart_DifferentLineEndings_NormalizesCorrectly() {
        // Arrange
        String payload = "1TEST001\r\nData line 1\r1TEST002\nData line 2";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).startsWith("1TEST001"));
        assertTrue(result.get(1).startsWith("1TEST002"));
    }

    @Test
    @DisplayName("Should handle complex regex patterns")
    void testSplitByRegexStart_ComplexRegex_WorksCorrectly() {
        // Arrange
        String payload = "HDR001\nData\nHDR002\nMore data\nFTR001\nFooter";
        String startRegex = "^HDR\\d{3}";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).startsWith("HDR001"));
        assertTrue(result.get(1).startsWith("HDR002"));
    }

    @Test
    @DisplayName("Should extract value from payload using regex")
    void testExtractFromPayload_ValidRegex_ReturnsExtractedValue() {
        // Arrange
        String message = "Order ID: 12345, Customer: John Doe";
        String expression = "Order ID: (\\d+)";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertEquals("12345", result);
    }

    @Test
    @DisplayName("Should extract full match when no groups")
    void testExtractFromPayload_NoGroups_ReturnsFullMatch() {
        // Arrange
        String message = "Status: ACTIVE";
        String expression = "Status: \\w+";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertEquals("Status: ACTIVE", result);
    }

    @Test
    @DisplayName("Should return null when no match found")
    void testExtractFromPayload_NoMatch_ReturnsNull() {
        // Arrange
        String message = "Order ID: 12345";
        String expression = "Customer: (\\w+)";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle null message gracefully")
    void testExtractFromPayload_NullMessage_ReturnsNull() {
        // Arrange
        String expression = "Order ID: (\\d+)";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(null, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle null expression gracefully")
    void testExtractFromPayload_NullExpression_ReturnsNull() {
        // Arrange
        String message = "Order ID: 12345";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, null);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle invalid regex gracefully")
    void testExtractFromPayload_InvalidRegex_ReturnsNull() {
        // Arrange
        String message = "Order ID: 12345";
        String expression = "[invalid regex";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should trim extracted values")
    void testExtractFromPayload_ExtractedValue_IsTrimmed() {
        // Arrange
        String message = "Order ID:   12345   ";
        String expression = "Order ID:\\s+(\\d+)\\s+";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertEquals("12345", result);
    }

    @Test
    @DisplayName("Should handle multiple groups and return first")
    void testExtractFromPayload_MultipleGroups_ReturnsFirstGroup() {
        // Arrange
        String message = "Order ID: 12345, Customer: John";
        String expression = "Order ID: (\\d+), Customer: (\\w+)";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertEquals("12345", result);
    }

    @Test
    @DisplayName("Should handle empty string message")
    void testExtractFromPayload_EmptyMessage_ReturnsNull() {
        // Arrange
        String message = "";
        String expression = "Order ID: (\\d+)";
        
        // Act
        String result = FlatFileUtil.extractFromPayload(message, expression);
        
        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should handle records with empty lines")
    void testSplitByRegexStart_RecordsWithEmptyLines_HandlesCorrectly() {
        // Arrange
        String payload = "1TEST001\n\nData line 1\n\n1TEST002\n\nData line 2";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("1TEST001"));
        assertTrue(result.get(1).contains("1TEST002"));
    }

    @Test
    @DisplayName("Should handle consecutive record markers")
    void testSplitByRegexStart_ConsecutiveMarkers_CreatesEmptyRecord() {
        // Arrange
        String payload = "1TEST001\n1TEST002\nData line";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(2, result.size());
        assertEquals("1TEST001", result.get(0));
        assertTrue(result.get(1).startsWith("1TEST002"));
    }

    @Test
    @DisplayName("Should handle case sensitive regex")
    void testSplitByRegexStart_CaseSensitive_WorksCorrectly() {
        // Arrange
        String payload = "1test001\nData\n1TEST002\nMore data";
        String startRegex = "^1TEST";
        
        // Act
        List<String> result = FlatFileUtil.splitByRegexStart(payload, startRegex);
        
        // Assert
        assertEquals(1, result.size());
        assertTrue(result.get(0).startsWith("1TEST002"));
    }
}
