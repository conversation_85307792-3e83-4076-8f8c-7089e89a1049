package com.dell.it.hip.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.integration.support.context.NamedComponent;
import org.springframework.messaging.MessageChannel;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;

/**
 * Comprehensive unit tests for ServiceManager class.
 * Tests all methods and edge cases to improve code coverage.
 */
@ExtendWith(MockitoExtension.class)
public class ServiceManagerTest {

    @Mock
    private TaskExecutor flowExecutor;

    @Mock
    private MessageChannel deadLetterChannel;

    @Mock
    private MessageChannel inputChannel1;

    @Mock
    private MessageChannel inputChannel2;

    @Mock
    private MessageChannel outputChannel;

    @Mock
    private NamedComponent namedChannel;

    @Mock
    private InputAdapterStrategy inputAdapterStrategy;

    @Mock
    private FlowStepStrategy flowStepStrategy;

    @Mock
    private HandlerStrategy handlerStrategy;

    @Mock
    private HIPIntegrationDefinition integrationDefinition;

    @Mock
    private AdapterConfigRef adapterConfigRef;

    @Mock
    private HandlerConfigRef handlerConfigRef;

    @Mock
    private FlowStepConfigRef flowStepConfigRef;

    @InjectMocks
    private ServiceManager serviceManager;

    private final String TEST_INTEGRATION_NAME = "test-integration";
    private final String TEST_VERSION = "1.0";
    private final String TEST_INTEGRATION_KEY = "test-integration:1.0";

    @BeforeEach
    void setUp() throws Exception {
        // Setup strategy maps using reflection since they are private fields
        Map<String, InputAdapterStrategy> inputAdapterStrategyMap = new HashMap<>();
        inputAdapterStrategyMap.put("kafka", inputAdapterStrategy);
        setPrivateField(serviceManager, "inputAdapterStrategyMap", inputAdapterStrategyMap);

        Map<String, FlowStepStrategy> flowStepStrategyMap = new HashMap<>();
        flowStepStrategyMap.put("validation", flowStepStrategy);
        setPrivateField(serviceManager, "flowStepStrategyMap", flowStepStrategyMap);

        Map<String, HandlerStrategy> handlerStrategyMap = new HashMap<>();
        handlerStrategyMap.put("http", handlerStrategy);
        setPrivateField(serviceManager, "handlerStrategyMap", handlerStrategyMap);

        // Setup mock integration definition with lenient stubbing
        lenient().when(integrationDefinition.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        lenient().when(integrationDefinition.getVersion()).thenReturn(TEST_VERSION);
        lenient().when(integrationDefinition.getAdapterConfigRefs()).thenReturn(Arrays.asList(adapterConfigRef));
        lenient().when(integrationDefinition.getHandlerConfigRefs()).thenReturn(Arrays.asList(handlerConfigRef));
        lenient().when(integrationDefinition.getFlowStepConfigRefs()).thenReturn(Arrays.asList(flowStepConfigRef));
    }

    /**
     * Helper method to set private fields using reflection
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    @Test
    @DisplayName("Test registerIntegration_ValidDefinition_RegistersSuccessfully")
    void testRegisterIntegration_ValidDefinition_RegistersSuccessfully() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, outputChannel);

        // Act
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Assert
        HIPIntegrationDefinition retrievedDef = serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION);
        assertNotNull(retrievedDef);
        assertEquals(integrationDefinition, retrievedDef);

        ServiceManager.FlowTopology topology = serviceManager.getTopology(TEST_INTEGRATION_NAME, TEST_VERSION);
        assertNotNull(topology);
        assertEquals(integrationDefinition, topology.getDefinition());
        assertEquals(channels, topology.getChannels());
        assertEquals(outputChannel, topology.getOutputChannel());
    }

    @Test
    @DisplayName("Test registerIntegration_EmptyChannels_RegistersWithNullOutput")
    void testRegisterIntegration_EmptyChannels_RegistersWithNullOutput() {
        // Arrange
        List<MessageChannel> emptyChannels = Collections.emptyList();

        // Act
        serviceManager.registerIntegration(integrationDefinition, emptyChannels);

        // Assert
        ServiceManager.FlowTopology topology = serviceManager.getTopology(TEST_INTEGRATION_NAME, TEST_VERSION);
        assertNotNull(topology);
        assertTrue(topology.getChannels().isEmpty());
        assertNull(topology.getOutputChannel());
    }

    @Test
    @DisplayName("Test unregisterIntegration_ExistingIntegration_RemovesSuccessfully")
    void testUnregisterIntegration_ExistingIntegration_RemovesSuccessfully() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        serviceManager.unregisterIntegration(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertNull(serviceManager.getIntegrationDefinition(TEST_INTEGRATION_NAME, TEST_VERSION));
        assertNull(serviceManager.getTopology(TEST_INTEGRATION_NAME, TEST_VERSION));
    }

    @Test
    @DisplayName("Test unregisterIntegration_NonExistentIntegration_DoesNotThrow")
    void testUnregisterIntegration_NonExistentIntegration_DoesNotThrow() {
        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            serviceManager.unregisterIntegration("non-existent", "1.0");
        });
    }

    @Test
    @DisplayName("Test getIntegrationDefinition_NonExistent_ReturnsNull")
    void testGetIntegrationDefinition_NonExistent_ReturnsNull() {
        // Act
        HIPIntegrationDefinition result = serviceManager.getIntegrationDefinition("non-existent", "1.0");

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test getAllDefinitions_MultipleIntegrations_ReturnsAll")
    void testAllDefinitions_MultipleIntegrations_ReturnsAll() {
        // Arrange
        HIPIntegrationDefinition def2 = mock(HIPIntegrationDefinition.class);
        when(def2.getHipIntegrationName()).thenReturn("integration2");
        when(def2.getVersion()).thenReturn("2.0");

        serviceManager.registerIntegration(integrationDefinition, Arrays.asList(inputChannel1));
        serviceManager.registerIntegration(def2, Arrays.asList(inputChannel2));

        // Act
        List<HIPIntegrationDefinition> allDefs = serviceManager.getAllDefinitions();

        // Assert
        assertEquals(2, allDefs.size());
        assertTrue(allDefs.contains(integrationDefinition));
        assertTrue(allDefs.contains(def2));
    }

    @Test
    @DisplayName("Test getDefinitionsByName_ExistingName_ReturnsMatchingDefinitions")
    void testGetDefinitionsByName_ExistingName_ReturnsMatchingDefinitions() {
        // Arrange
        HIPIntegrationDefinition def2 = mock(HIPIntegrationDefinition.class);
        when(def2.getHipIntegrationName()).thenReturn(TEST_INTEGRATION_NAME);
        when(def2.getVersion()).thenReturn("2.0");

        serviceManager.registerIntegration(integrationDefinition, Arrays.asList(inputChannel1));
        serviceManager.registerIntegration(def2, Arrays.asList(inputChannel2));

        // Act
        List<HIPIntegrationDefinition> definitions = serviceManager.getDefinitionsByName(TEST_INTEGRATION_NAME);

        // Assert
        assertEquals(2, definitions.size());
        assertTrue(definitions.contains(integrationDefinition));
        assertTrue(definitions.contains(def2));
    }

    @Test
    @DisplayName("Test getDefinitionsByName_NonExistentName_ReturnsEmptyList")
    void testGetDefinitionsByName_NonExistentName_ReturnsEmptyList() {
        // Act
        List<HIPIntegrationDefinition> definitions = serviceManager.getDefinitionsByName("non-existent");

        // Assert
        assertTrue(definitions.isEmpty());
    }

    @Test
    @DisplayName("Test getInputChannel_ExistingIntegration_ReturnsFirstChannel")
    void testGetInputChannel_ExistingIntegration_ReturnsFirstChannel() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, inputChannel2);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        MessageChannel result = serviceManager.getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(inputChannel1, result);
    }

    @Test
    @DisplayName("Test getInputChannel_NonExistentIntegration_ReturnsDeadLetterChannel")
    void testGetInputChannel_NonExistentIntegration_ReturnsDeadLetterChannel() {
        // Act
        MessageChannel result = serviceManager.getInputChannel("non-existent", "1.0");

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getInputChannel_EmptyChannels_ReturnsDeadLetterChannel")
    void testGetInputChannel_EmptyChannels_ReturnsDeadLetterChannel() {
        // Arrange
        serviceManager.registerIntegration(integrationDefinition, Collections.emptyList());

        // Act
        MessageChannel result = serviceManager.getInputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getInputChannelByAdapterRef_MatchingNamedChannel_ReturnsCorrectChannel")
    void testGetInputChannelByAdapterRef_MatchingNamedChannel_ReturnsCorrectChannel() {
        // Arrange
        MessageChannel namedChannel = mock(MessageChannel.class, withSettings().extraInterfaces(NamedComponent.class));
        NamedComponent namedComponent = (NamedComponent) namedChannel;
        when(namedComponent.getComponentName()).thenReturn("test-integration.1.0.inputChannel.kafka-adapter");

        List<MessageChannel> channels = Arrays.asList(namedChannel, inputChannel2);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        MessageChannel result = serviceManager.getInputChannelByAdapterRef(TEST_INTEGRATION_NAME, TEST_VERSION, "kafka-adapter");

        // Assert
        assertEquals(namedChannel, result);
    }

    @Test
    @DisplayName("Test getInputChannelByAdapterRef_NoMatchingName_ReturnsFallbackChannel")
    void testGetInputChannelByAdapterRef_NoMatchingName_ReturnsFallbackChannel() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, inputChannel2);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        MessageChannel result = serviceManager.getInputChannelByAdapterRef(TEST_INTEGRATION_NAME, TEST_VERSION, "non-existent-adapter");

        // Assert
        assertEquals(inputChannel1, result); // Should return first channel as fallback
    }

    @Test
    @DisplayName("Test getInputChannelByAdapterRef_NonExistentIntegration_ReturnsDeadLetterChannel")
    void testGetInputChannelByAdapterRef_NonExistentIntegration_ReturnsDeadLetterChannel() {
        // Act
        MessageChannel result = serviceManager.getInputChannelByAdapterRef("non-existent", "1.0", "adapter");

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getInputChannelByAdapterRef_EmptyChannels_ReturnsDeadLetterChannel")
    void testGetInputChannelByAdapterRef_EmptyChannels_ReturnsDeadLetterChannel() {
        // Arrange
        serviceManager.registerIntegration(integrationDefinition, Collections.emptyList());

        // Act
        MessageChannel result = serviceManager.getInputChannelByAdapterRef(TEST_INTEGRATION_NAME, TEST_VERSION, "adapter");

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getChannels_ExistingIntegration_ReturnsChannelList")
    void testGetChannels_ExistingIntegration_ReturnsChannelList() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, inputChannel2);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        List<MessageChannel> result = serviceManager.getChannels(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(channels, result);
    }

    @Test
    @DisplayName("Test getChannels_NonExistentIntegration_ReturnsEmptyList")
    void testGetChannels_NonExistentIntegration_ReturnsEmptyList() {
        // Act
        List<MessageChannel> result = serviceManager.getChannels("non-existent", "1.0");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test getOutputChannel_ExistingIntegration_ReturnsOutputChannel")
    void testGetOutputChannel_ExistingIntegration_ReturnsOutputChannel() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, outputChannel);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        MessageChannel result = serviceManager.getOutputChannel(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertEquals(outputChannel, result);
    }

    @Test
    @DisplayName("Test getOutputChannel_NonExistentIntegration_ReturnsDeadLetterChannel")
    void testGetOutputChannel_NonExistentIntegration_ReturnsDeadLetterChannel() {
        // Act
        MessageChannel result = serviceManager.getOutputChannel("non-existent", "1.0");

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getInputAdapterStrategy_ExistingType_ReturnsStrategy")
    void testGetInputAdapterStrategy_ExistingType_ReturnsStrategy() {
        // Act
        InputAdapterStrategy result = serviceManager.getInputAdapterStrategy("kafka");

        // Assert
        assertEquals(inputAdapterStrategy, result);
    }

    @Test
    @DisplayName("Test getInputAdapterStrategy_NonExistentType_ReturnsNull")
    void testGetInputAdapterStrategy_NonExistentType_ReturnsNull() {
        // Act
        InputAdapterStrategy result = serviceManager.getInputAdapterStrategy("non-existent");

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test getInputAdapterStrategyMap_ReturnsCompleteMap")
    void testGetInputAdapterStrategyMap_ReturnsCompleteMap() {
        // Act
        Map<String, InputAdapterStrategy> result = serviceManager.getInputAdapterStrategyMap();

        // Assert
        assertNotNull(result);
        assertEquals(inputAdapterStrategy, result.get("kafka"));
    }

    @Test
    @DisplayName("Test getHandlerStrategy_ExistingType_ReturnsStrategy")
    void testGetHandlerStrategy_ExistingType_ReturnsStrategy() {
        // Act
        HandlerStrategy result = serviceManager.getHandlerStrategy("http");

        // Assert
        assertEquals(handlerStrategy, result);
    }

    @Test
    @DisplayName("Test getHandlerStrategy_NonExistentType_ReturnsNull")
    void testGetHandlerStrategy_NonExistentType_ReturnsNull() {
        // Act
        HandlerStrategy result = serviceManager.getHandlerStrategy("non-existent");

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test getHandlerStrategyMap_ReturnsCompleteMap")
    void testGetHandlerStrategyMap_ReturnsCompleteMap() {
        // Act
        Map<String, HandlerStrategy> result = serviceManager.getHandlerStrategyMap();

        // Assert
        assertNotNull(result);
        assertEquals(handlerStrategy, result.get("http"));
    }

    @Test
    @DisplayName("Test getFlowStepStrategy_ExistingType_ReturnsStrategy")
    void testGetFlowStepStrategy_ExistingType_ReturnsStrategy() {
        // Act
        FlowStepStrategy result = serviceManager.getFlowStepStrategy("validation");

        // Assert
        assertEquals(flowStepStrategy, result);
    }

    @Test
    @DisplayName("Test getFlowStepStrategy_NonExistentType_ReturnsNull")
    void testGetFlowStepStrategy_NonExistentType_ReturnsNull() {
        // Act
        FlowStepStrategy result = serviceManager.getFlowStepStrategy("non-existent");

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test getFlowStepStrategyMap_ReturnsCompleteMap")
    void testGetFlowStepStrategyMap_ReturnsCompleteMap() {
        // Act
        Map<String, FlowStepStrategy> result = serviceManager.getFlowStepStrategyMap();

        // Assert
        assertNotNull(result);
        assertEquals(flowStepStrategy, result.get("validation"));
    }

    @Test
    @DisplayName("Test getAdapterRefs_ExistingIntegration_ReturnsAdapterRefs")
    void testGetAdapterRefs_ExistingIntegration_ReturnsAdapterRefs() {
        // Arrange
        serviceManager.registerIntegration(integrationDefinition, Arrays.asList(inputChannel1));

        // Act
        List<AdapterConfigRef> result = serviceManager.getAdapterRefs(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(adapterConfigRef, result.get(0));
    }

    @Test
    @DisplayName("Test getAdapterRefs_NonExistentIntegration_ReturnsEmptyList")
    void testGetAdapterRefs_NonExistentIntegration_ReturnsEmptyList() {
        // Act
        List<AdapterConfigRef> result = serviceManager.getAdapterRefs("non-existent", "1.0");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test getHandlerRefs_ExistingIntegration_ReturnsHandlerRefs")
    void testGetHandlerRefs_ExistingIntegration_ReturnsHandlerRefs() {
        // Arrange
        serviceManager.registerIntegration(integrationDefinition, Arrays.asList(inputChannel1));

        // Act
        List<HandlerConfigRef> result = serviceManager.getHandlerRefs(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(handlerConfigRef, result.get(0));
    }

    @Test
    @DisplayName("Test getHandlerRefs_NonExistentIntegration_ReturnsEmptyList")
    void testGetHandlerRefs_NonExistentIntegration_ReturnsEmptyList() {
        // Act
        List<HandlerConfigRef> result = serviceManager.getHandlerRefs("non-existent", "1.0");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test getStepRefs_ExistingIntegration_ReturnsStepRefs")
    void testGetStepRefs_ExistingIntegration_ReturnsStepRefs() {
        // Arrange
        serviceManager.registerIntegration(integrationDefinition, Arrays.asList(inputChannel1));

        // Act
        List<FlowStepConfigRef> result = serviceManager.getStepRefs(TEST_INTEGRATION_NAME, TEST_VERSION);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(flowStepConfigRef, result.get(0));
    }

    @Test
    @DisplayName("Test getStepRefs_NonExistentIntegration_ReturnsEmptyList")
    void testGetStepRefs_NonExistentIntegration_ReturnsEmptyList() {
        // Act
        List<FlowStepConfigRef> result = serviceManager.getStepRefs("non-existent", "1.0");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test getFlowExecutor_ReturnsInjectedExecutor")
    void testGetFlowExecutor_ReturnsInjectedExecutor() {
        // Act
        TaskExecutor result = serviceManager.getFlowExecutor();

        // Assert
        assertEquals(flowExecutor, result);
    }

    @Test
    @DisplayName("Test getChannels_WithNamedChannels_ReturnsChannelMap")
    void testGetChannels_WithNamedChannels_ReturnsChannelMap() {
        // Arrange
        MessageChannel namedChannel = mock(MessageChannel.class, withSettings().extraInterfaces(NamedComponent.class));
        NamedComponent namedComponent = (NamedComponent) namedChannel;
        when(namedComponent.getComponentName()).thenReturn("test.channel");

        List<MessageChannel> channels = Arrays.asList(namedChannel, inputChannel1);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        Map<String, MessageChannel> result = serviceManager.getChannels();

        // Assert
        assertNotNull(result);
        assertEquals(namedChannel, result.get("test.channel"));
        assertEquals(deadLetterChannel, result.get("hip.deadLetterChannel"));
    }

    @Test
    @DisplayName("Test getChannelByName_ExistingChannel_ReturnsChannel")
    void testGetChannelByName_ExistingChannel_ReturnsChannel() {
        // Arrange
        MessageChannel namedChannel = mock(MessageChannel.class, withSettings().extraInterfaces(NamedComponent.class));
        NamedComponent namedComponent = (NamedComponent) namedChannel;
        when(namedComponent.getComponentName()).thenReturn("test.channel");

        List<MessageChannel> channels = Arrays.asList(namedChannel);
        serviceManager.registerIntegration(integrationDefinition, channels);

        // Act
        MessageChannel result = serviceManager.getChannelByName("test.channel");

        // Assert
        assertEquals(namedChannel, result);
    }

    @Test
    @DisplayName("Test getChannelByName_NonExistentChannel_ReturnsDeadLetterChannel")
    void testGetChannelByName_NonExistentChannel_ReturnsDeadLetterChannel() {
        // Act
        MessageChannel result = serviceManager.getChannelByName("non-existent.channel");

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test getDeadLetterChannel_ReturnsInjectedChannel")
    void testGetDeadLetterChannel_ReturnsInjectedChannel() {
        // Act
        MessageChannel result = serviceManager.getDeadLetterChannel();

        // Assert
        assertEquals(deadLetterChannel, result);
    }

    @Test
    @DisplayName("Test FlowTopology_Constructor_InitializesCorrectly")
    void testFlowTopology_Constructor_InitializesCorrectly() {
        // Arrange
        List<MessageChannel> channels = Arrays.asList(inputChannel1, inputChannel2);

        // Act
        ServiceManager.FlowTopology topology = new ServiceManager.FlowTopology(integrationDefinition, channels, outputChannel);

        // Assert
        assertEquals(integrationDefinition, topology.getDefinition());
        assertEquals(channels, topology.getChannels());
        assertEquals(outputChannel, topology.getOutputChannel());

        // Verify channels list is immutable
        assertThrows(UnsupportedOperationException.class, () -> {
            topology.getChannels().add(mock(MessageChannel.class));
        });
    }

    @Test
    @DisplayName("Test FlowTopology_EmptyChannels_HandlesCorrectly")
    void testFlowTopology_EmptyChannels_HandlesCorrectly() {
        // Arrange
        List<MessageChannel> emptyChannels = Collections.emptyList();

        // Act
        ServiceManager.FlowTopology topology = new ServiceManager.FlowTopology(integrationDefinition, emptyChannels, null);

        // Assert
        assertEquals(integrationDefinition, topology.getDefinition());
        assertTrue(topology.getChannels().isEmpty());
        assertNull(topology.getOutputChannel());
    }
}
