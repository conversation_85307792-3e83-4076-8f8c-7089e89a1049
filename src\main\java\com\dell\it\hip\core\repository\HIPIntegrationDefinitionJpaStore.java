package com.dell.it.hip.core.repository;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;

@Component("jpaStore") 
@ConditionalOnProperty(name = "integration.store.type", havingValue = "JPA", matchIfMissing = true)
public class HIPIntegrationDefinitionJpaStore implements HIPIntegrationDefinitionStore {

	@Autowired private HIPIntegrationDefinitionRepository repo;
    @Override
    public void save(HIPIntegrationRequestEntity entity) { repo.save(entity); }
    @Override
    public HIPIntegrationRequestEntity find(String serviceManagerName, String hipIntegrationName, String version) {
        return repo.findByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, convertVersionToBigDecimal(version));
    }
    @Override
    public boolean exists(String serviceManagerName, String hipIntegrationName, String version) {
        return repo.existsByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, convertVersionToBigDecimal(version));
    }

    private BigDecimal convertVersionToBigDecimal(String version) {
        if (version == null) {
            return null;
        }
        try {
            // Handle simple decimal versions directly
            if (version.matches("\\d+(\\.\\d+)?")) {
                return new BigDecimal(version);
            }
            // Handle semantic versions like "1.2.3" by converting to "1.23"
            String[] parts = version.split("\\.");
            if (parts.length >= 2) {
                StringBuilder sb = new StringBuilder(parts[0]);
                sb.append(".");
                for (int i = 1; i < parts.length; i++) {
                    sb.append(parts[i]);
                }
                return new BigDecimal(sb.toString());
            }
            // Fallback: try direct conversion
            return new BigDecimal(version);
        } catch (NumberFormatException e) {
            // If conversion fails, use a hash-based approach or default
            return new BigDecimal(Math.abs(version.hashCode()) % 10000);
        }
    }
    @Override
    public List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName) {
        return repo.findByServiceManagerName(serviceManagerName);
    }
	@Override
	public void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(String serviceManagerName,
			String hipIntegrationName, String version) {
		 HIPIntegrationRequestEntity entity = find(serviceManagerName, hipIntegrationName, version);
	        if (entity != null) repo.delete(entity);
		
	}

}
